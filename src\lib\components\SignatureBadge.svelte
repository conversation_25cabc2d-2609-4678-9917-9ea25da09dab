<script lang="ts">
  import { getSignatureInfo, verifySignature } from '$lib/utils/signatureUtils';
  import { supabase } from '$lib/supabase';
  import SignaturePasswordDialog from './SignaturePasswordDialog.svelte';
  import { onMount, createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher();

  interface SignatureData {
    userId: string;
    userName: string;
    email: string;
    timestamp: string;
    contentHash: string;
    signatureId: string;
    signatureVersion: string;
    signatureMethod: string;
    passwordProtected: boolean;
    receiverId: string | null;
    receiverUnitId: string | null;
    isUserInReceiverUnit?: boolean;
    [key: string]: any;
  }

  interface VerificationResult {
    valid: boolean;
    reason: string;
    isRecipient: boolean;
    isAuthenticated: boolean;
    requiresPassword?: boolean;
  }

  // المدخلات
  export let signature: SignatureData | null = null;
  export let content: string = '';
  export let subject: string = '';
  export let showDetails: boolean = false;
  export let messageId: string | null = null; // معرف الرسالة للتحقق من المستلم

  // حالة التحقق من التوقيع
  let isValid: boolean = false;
  let verificationResult: VerificationResult = {
    valid: false,
    reason: '',
    isRecipient: false,
    isAuthenticated: false,
    requiresPassword: false
  };
  let signatureInfo: any = null;
  let passwordDialogOpen: boolean = false;
  let password: string = '';
  let currentUserId: string | null = null;
  let isRecipient: boolean = false;
  let isAuthenticated: boolean = false;
  let requiresPassword: boolean = false;
  let userUnit: string | null = null;

  // إرسال حدث عند تغيير حالة التحقق
  function dispatchVerificationStatus(): void {
    dispatch('verified', {
      isValid,
      isRecipient,
      isAuthenticated,
      requiresPassword
    });
  }

  // الحصول على معرف المستخدم الحالي والتحقق من انتمائه للوحدة
  onMount(async () => {
    try {
      // جلب بيانات المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        currentUserId = user.id;

        // جلب بيانات وحدة المستخدم
        const { data: profileData } = await supabase
          .from('profiles')
          .select('unit_id')
          .eq('id', user.id)
          .single();

        if (profileData && profileData.unit_id) {
          userUnit = profileData.unit_id;
        }

        // جلب الوحدات المتعددة للمستخدم
        const { data: userUnits } = await supabase
          .from('user_units')
          .select('unit_id')
          .eq('user_id', user.id);

        const userUnitIds = userUnits ? userUnits.map(u => u.unit_id) : [];

        // إعادة التحقق من التوقيع مع معرف المستخدم
        if (signature) {
          // التحقق مما إذا كان المستخدم ينتمي إلى وحدة المستلم
          const isInReceiverUnit = signature.receiverUnitId &&
            (userUnit === signature.receiverUnitId || userUnitIds.includes(signature.receiverUnitId));

          // تعديل التوقيع لتضمين معلومات الوحدة
          const signatureWithUnitInfo = {
            ...signature,
            isUserInReceiverUnit: isInReceiverUnit
          };

          // التحقق من التوقيع
          verificationResult = verifySignature(content, subject, signatureWithUnitInfo, currentUserId);
          isValid = verificationResult.valid;
          isRecipient = verificationResult.isRecipient || false;
          isAuthenticated = verificationResult.isAuthenticated || false;
          requiresPassword = verificationResult.requiresPassword || false;
          signatureInfo = getSignatureInfo(signature);

          // إرسال حدث بحالة التحقق
          dispatchVerificationStatus();

          // إذا كان المستخدم هو المستلم المقصود ويحتاج إلى كلمة مرور، افتح نافذة إدخال كلمة المرور تلقائياً
          if (isRecipient && requiresPassword && !isAuthenticated) {
            setTimeout(() => {
              passwordDialogOpen = true;
            }, 500);
          }
        }
      }
    } catch (error) {
      console.error('Error in SignatureBadge onMount:', error);
    }
  });

  // التحقق من التوقيع عند تغيير المدخلات
  $: {
    if (signature) {
      try {
        // التحقق الأولي
        const result = verifySignature(content, subject, signature, currentUserId || undefined);
        verificationResult = result as VerificationResult;
        isValid = verificationResult.valid;
        isRecipient = verificationResult.isRecipient || false;
        isAuthenticated = verificationResult.isAuthenticated || false;
        requiresPassword = verificationResult.requiresPassword || false;
        signatureInfo = getSignatureInfo(signature);
      } catch (error) {
        console.error('Error verifying signature:', error);
      }
    }
  }

  // تبديل عرض التفاصيل
  function toggleDetails(): void {
    showDetails = !showDetails;
  }

  // فتح نافذة إدخال كلمة المرور للتحقق
  function openPasswordDialog(): void {
    if (signature && signature.passwordProtected && (!isValid || (isRecipient && !isAuthenticated))) {
      passwordDialogOpen = true;
    }
  }

  // معالجة إدخال كلمة المرور للتحقق
  function handlePasswordSubmit(event: { detail: { password: string } }): void {
    const enteredPassword = event.detail.password;

    if (!signature) return;

    try {
      // إعادة التحقق باستخدام كلمة المرور
      const result = verifySignature(content, subject, signature, currentUserId || undefined, enteredPassword);
      verificationResult = result as VerificationResult;
      isValid = verificationResult.valid;
      isRecipient = verificationResult.isRecipient || false;
      isAuthenticated = verificationResult.isAuthenticated || false;

      // تحديث معلومات التوقيع
      if (isValid) {
        signatureInfo = getSignatureInfo(signature);
      }

      // إرسال حدث بحالة التحقق
      dispatchVerificationStatus();
    } catch (error) {
      console.error('Error verifying signature with password:', error);
    }
  }
</script>

{#if signature}
  <div class="signature-badge">
    <button
      type="button"
      class="signature-status {isValid ? 'valid' : 'invalid'}"
      on:click={toggleDetails}
      on:keydown={(e) => e.key === 'Enter' && toggleDetails()}
      aria-expanded={showDetails}
      aria-label={isValid ? 'توقيع صالح - انقر للتفاصيل' : 'توقيع غير صالح - انقر للتفاصيل'}
    >
      <div class="icon">
        {#if isValid}
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        {:else}
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
        {/if}
      </div>
      <div class="text">
        {#if isValid && isAuthenticated}
          <span>تم التوقيع إلكترونياً</span>
          {#if isRecipient}
            <span class="recipient-badge">تم التحقق من هويتك كمستلم مقصود</span>
          {/if}
        {:else if isRecipient && !isAuthenticated}
          <span>رسالة موقعة مقفلة</span>
          <span class="recipient-badge">أنت المستلم المقصود - يرجى إدخال كلمة المرور للتحقق</span>
        {:else}
          <span>توقيع {isValid ? 'صالح' : 'غير صالح'}</span>
          {#if signature.passwordProtected}
            <span class="password-protected">محمي بكلمة مرور</span>
          {/if}
        {/if}
      </div>
    </button>

    {#if (isRecipient && !isAuthenticated) || (!isValid && signature.passwordProtected)}
      <div class="password-verification">
        <button
          type="button"
          class="verify-button"
          on:click={openPasswordDialog}
        >
          {isRecipient ? 'فتح الرسالة باستخدام كلمة المرور' : 'التحقق باستخدام كلمة المرور'}
        </button>
        {#if verificationResult.reason}
          <p class="verification-reason">{verificationResult.reason}</p>
        {/if}
      </div>
    {/if}

    {#if showDetails && signatureInfo && signatureInfo.details}
      <div class="signature-details">
        <div class="detail-item">
          <span class="label">الموقع:</span>
          <span class="value">{signatureInfo.details.userName}</span>
        </div>
        <div class="detail-item">
          <span class="label">البريد الإلكتروني:</span>
          <span class="value">{signatureInfo.details.email}</span>
        </div>
        <div class="detail-item">
          <span class="label">تاريخ التوقيع:</span>
          <span class="value">{signatureInfo.details.date}</span>
        </div>
        <div class="detail-item">
          <span class="label">معرف التوقيع:</span>
          <span class="value signature-id">{signatureInfo.details.signatureId}</span>
        </div>
        <div class="detail-item">
          <span class="label">طريقة التوقيع:</span>
          <span class="value">{signatureInfo.verificationMethod}</span>
        </div>
        <div class="detail-item">
          <span class="label">مستوى الأمان:</span>
          <span class="value">{signatureInfo.securityLevel}</span>
        </div>
      </div>
    {/if}
  </div>

  <!-- نافذة إدخال كلمة مرور التحقق -->
  <SignaturePasswordDialog
    bind:isOpen={passwordDialogOpen}
    title="التحقق من التوقيع الإلكتروني"
    message="هذا التوقيع محمي بكلمة مرور. يرجى إدخال كلمة المرور للتحقق من صحة التوقيع."
    on:submit={handlePasswordSubmit}
    on:close={() => passwordDialogOpen = false}
  />
{/if}

<style>
  .signature-badge {
    border: 1px solid;
    border-radius: 0.5rem;
    overflow: hidden;
    margin: 1rem 0;
  }

  .signature-status {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    cursor: pointer;
    width: 100%;
    text-align: right;
    background: none;
    border: none;
    font-size: inherit;
    font-family: inherit;
  }

  .signature-status.valid {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
    color: rgb(16, 185, 129);
  }

  .signature-status.invalid {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: rgb(239, 68, 68);
  }

  .icon {
    margin-left: 0.75rem;
  }

  .text {
    flex: 1;
  }

  .password-protected {
    display: block;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    opacity: 0.8;
  }

  .recipient-badge {
    display: block;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    background-color: rgba(16, 185, 129, 0.2);
    border-radius: 0.25rem;
    padding: 0.125rem 0.375rem;
    display: inline-block;
    font-weight: 600;
  }

  .password-verification {
    padding: 0.75rem 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
  }

  .verify-button {
    background-color: #4f46e5;
    color: white;
    border: none;
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .verify-button:hover {
    background-color: #4338ca;
  }

  .verification-reason {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: rgb(239, 68, 68);
  }

  .signature-details {
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }

  .detail-item {
    display: flex;
    margin-bottom: 0.5rem;
  }

  .label {
    font-weight: 600;
    margin-left: 0.5rem;
    min-width: 120px;
  }

  .value {
    flex: 1;
  }

  .signature-id {
    font-family: monospace;
    font-size: 0.875rem;
    word-break: break-all;
  }

  :global(.dark) .signature-status.valid {
    background-color: rgba(16, 185, 129, 0.2);
    border-color: rgba(16, 185, 129, 0.4);
    color: rgb(52, 211, 153);
  }

  :global(.dark) .signature-status.invalid {
    background-color: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.4);
    color: rgb(248, 113, 113);
  }

  :global(.dark) .signature-details {
    background-color: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  :global(.dark) .password-verification {
    background-color: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  :global(.dark) .verify-button {
    background-color: #6366f1;
  }

  :global(.dark) .verify-button:hover {
    background-color: #4f46e5;
  }
</style>
