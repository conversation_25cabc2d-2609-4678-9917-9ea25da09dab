import { supabase } from '$lib/supabase';

/**
 * التحقق من وجود عمود في جدول
 */
export async function checkColumnExists(tableName: string, columnName: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .eq('column_name', columnName)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('خطأ في التحقق من وجود العمود:', error);
      return false;
    }

    return !!data;
  } catch (err) {
    console.error('خطأ في التحقق من وجود العمود:', err);
    return false;
  }
}

/**
 * تحديث مستند موقع مع التعامل مع الأعمدة المفقودة
 */
export async function updateSignedDocumentSafely(
  documentId: string,
  updateData: Record<string, any>
): Promise<{ data: any; error: any }> {
  try {
    // محاولة التحديث العادي أولاً
    const { data, error } = await supabase
      .from('signed_documents')
      .update(updateData)
      .eq('id', documentId)
      .select()
      .single();

    if (error) {
      // إذا كان الخطأ متعلق بعمود مفقود، جرب بدون الأعمدة الإضافية
      if (error.message.includes('rejection_reason') || 
          error.message.includes('revision_comments') ||
          error.message.includes('review_notes')) {
        
        console.warn('بعض الأعمدة مفقودة، محاولة التحديث بدونها...');
        
        // إنشاء نسخة من البيانات بدون الأعمدة المشكوك فيها
        const safeUpdateData = { ...updateData };
        delete safeUpdateData.rejection_reason;
        delete safeUpdateData.revision_comments;
        delete safeUpdateData.review_notes;

        const { data: retryData, error: retryError } = await supabase
          .from('signed_documents')
          .update(safeUpdateData)
          .eq('id', documentId)
          .select()
          .single();

        return { data: retryData, error: retryError };
      }
    }

    return { data, error };
  } catch (err) {
    console.error('خطأ في تحديث المستند:', err);
    return { data: null, error: err };
  }
}

/**
 * إضافة الأعمدة المفقودة إلى جدول signed_documents
 */
export async function ensureSignedDocumentsColumns(): Promise<boolean> {
  try {
    // تنفيذ SQL لإضافة الأعمدة المفقودة
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        DO $$
        BEGIN
          -- إضافة عمود rejection_reason إذا لم يكن موجوداً
          IF NOT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'signed_documents'
            AND column_name = 'rejection_reason'
          ) THEN
            ALTER TABLE public.signed_documents ADD COLUMN rejection_reason TEXT;
          END IF;

          -- إضافة عمود revision_comments إذا لم يكن موجوداً
          IF NOT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'signed_documents'
            AND column_name = 'revision_comments'
          ) THEN
            ALTER TABLE public.signed_documents ADD COLUMN revision_comments TEXT;
          END IF;

          -- إضافة عمود review_notes إذا لم يكن موجوداً
          IF NOT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'signed_documents'
            AND column_name = 'review_notes'
          ) THEN
            ALTER TABLE public.signed_documents ADD COLUMN review_notes TEXT;
          END IF;
        END $$;
      `
    });

    if (error) {
      console.error('خطأ في إضافة الأعمدة:', error);
      return false;
    }

    return true;
  } catch (err) {
    console.error('خطأ في إضافة الأعمدة:', err);
    return false;
  }
}
