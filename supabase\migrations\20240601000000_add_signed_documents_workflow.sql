-- إنشاء نوع لحالة المستند الموقع
CREATE TYPE public.signed_document_status AS ENUM (
  'pending_signature',  -- في انتظار التوقيع
  'signed',             -- تم التوقيع
  'sent',               -- تم الإرسال
  'under_review',       -- قيد المراجعة
  'approved',           -- تمت الموافقة
  'rejected',           -- تم الرفض
  'returned_for_edit'   -- تم الإرجاع للتعديل
);

-- إنشاء جدول لتتبع حالة المستندات الموقعة
CREATE TABLE IF NOT EXISTS public.signed_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES public.documents(id) ON DELETE CASCADE,
  
  -- معلومات المنشئ
  creator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- معلوما<PERSON> الموقع
  signer_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  signed_at TIMESTAMP WITH TIME ZONE,
  signature JSONB,
  
  -- معلومات المستلم
  recipient_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  recipient_unit_id UUID REFERENCES public.units(id) ON DELETE SET NULL,
  
  -- حالة المستند
  status public.signed_document_status NOT NULL DEFAULT 'pending_signature',
  
  -- معلومات المراجعة
  reviewer_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  reviewed_at TIMESTAMP WITH TIME ZONE,
  review_notes TEXT,
  
  -- معلومات إضافية
  password_protected BOOLEAN DEFAULT FALSE,
  reference_number TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول لتاريخ المستندات الموقعة
CREATE TABLE IF NOT EXISTS public.signed_document_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  signed_document_id UUID NOT NULL REFERENCES public.signed_documents(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  action TEXT NOT NULL, -- مثل: إنشاء، توقيع، إرسال، مراجعة، موافقة، رفض، إرجاع للتعديل
  status public.signed_document_status,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء وظيفة لتحديث تاريخ المستندات الموقعة
CREATE OR REPLACE FUNCTION public.update_signed_document_history()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.signed_document_history (
    signed_document_id,
    user_id,
    action,
    status,
    details
  ) VALUES (
    NEW.id,
    CASE
      WHEN TG_OP = 'INSERT' THEN NEW.creator_id
      WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        CASE
          WHEN NEW.status = 'signed' THEN NEW.signer_id
          WHEN NEW.status = 'under_review' OR NEW.status = 'approved' OR NEW.status = 'rejected' OR NEW.status = 'returned_for_edit' THEN NEW.reviewer_id
          ELSE auth.uid()
        END
      ELSE auth.uid()
    END,
    CASE
      WHEN TG_OP = 'INSERT' THEN 'create'
      WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        CASE
          WHEN NEW.status = 'pending_signature' THEN 'request_signature'
          WHEN NEW.status = 'signed' THEN 'sign'
          WHEN NEW.status = 'sent' THEN 'send'
          WHEN NEW.status = 'under_review' THEN 'review'
          WHEN NEW.status = 'approved' THEN 'approve'
          WHEN NEW.status = 'rejected' THEN 'reject'
          WHEN NEW.status = 'returned_for_edit' THEN 'return_for_edit'
          ELSE 'update'
        END
      ELSE 'update'
    END,
    NEW.status,
    jsonb_build_object(
      'document_id', NEW.document_id,
      'reference_number', NEW.reference_number,
      'review_notes', NEW.review_notes
    )
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إنشاء المحفز لتحديث تاريخ المستندات الموقعة
CREATE TRIGGER update_signed_document_history
AFTER INSERT OR UPDATE ON public.signed_documents
FOR EACH ROW
EXECUTE FUNCTION public.update_signed_document_history();

-- إنشاء وظيفة لتوليد الرقم الإشاري للمستند الموقع
CREATE OR REPLACE FUNCTION public.generate_signed_document_reference()
RETURNS TRIGGER AS $$
BEGIN
  -- إذا كان المستند موقعاً ولا يوجد رقم إشاري، قم بإنشاء واحد
  IF NEW.status = 'signed' AND (NEW.reference_number IS NULL OR NEW.reference_number = '') THEN
    NEW.reference_number := public.generate_reference_number(NEW.signature->>'signatureId');
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إنشاء المحفز لتوليد الرقم الإشاري
CREATE TRIGGER generate_signed_document_reference
BEFORE UPDATE ON public.signed_documents
FOR EACH ROW
WHEN (NEW.status = 'signed' AND (NEW.reference_number IS NULL OR NEW.reference_number = ''))
EXECUTE FUNCTION public.generate_signed_document_reference();

-- إضافة سياسات الأمان RLS
ALTER TABLE public.signed_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.signed_document_history ENABLE ROW LEVEL SECURITY;

-- سياسة القراءة للمستندات الموقعة
CREATE POLICY "Users can view signed documents they created or are recipients of"
ON public.signed_documents
FOR SELECT
USING (
  creator_id = auth.uid() OR 
  signer_id = auth.uid() OR 
  recipient_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid() AND p.unit_id = recipient_unit_id
  ) OR
  EXISTS (
    SELECT 1 FROM public.profiles p
    JOIN public.roles r ON p.role_id = r.id
    WHERE p.id = auth.uid() AND (r.name = 'admin' OR r.name = 'مشرف')
  )
);

-- سياسة الإنشاء للمستندات الموقعة
CREATE POLICY "Users can create signed documents"
ON public.signed_documents
FOR INSERT
WITH CHECK (creator_id = auth.uid());

-- سياسة التحديث للمستندات الموقعة
CREATE POLICY "Users can update signed documents they created or are signers of"
ON public.signed_documents
FOR UPDATE
USING (
  creator_id = auth.uid() OR 
  signer_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM public.profiles p
    JOIN public.roles r ON p.role_id = r.id
    WHERE p.id = auth.uid() AND (r.name = 'admin' OR r.name = 'مشرف')
  )
);

-- سياسة القراءة لتاريخ المستندات الموقعة
CREATE POLICY "Users can view signed document history they are involved with"
ON public.signed_document_history
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.signed_documents sd
    WHERE sd.id = signed_document_id AND (
      sd.creator_id = auth.uid() OR 
      sd.signer_id = auth.uid() OR 
      sd.recipient_id = auth.uid() OR
      EXISTS (
        SELECT 1 FROM public.profiles p
        WHERE p.id = auth.uid() AND p.unit_id = sd.recipient_unit_id
      ) OR
      EXISTS (
        SELECT 1 FROM public.profiles p
        JOIN public.roles r ON p.role_id = r.id
        WHERE p.id = auth.uid() AND (r.name = 'admin' OR r.name = 'مشرف')
      )
    )
  )
);
