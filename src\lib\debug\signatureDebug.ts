/**
 * أدوات تشخيص مشاكل التوقيع الإلكتروني
 */

import { supabase } from '$lib/supabase';
import { DocumentStorageService } from '$lib/services/documentStorageService';

export interface SignatureDebugInfo {
  documentId: string;
  documentExists: boolean;
  documentData: any;
  contentSource: 'database' | 'storage' | 'none';
  contentLength: number;
  userInfo: any;
  storageInfo?: any;
  errors: string[];
}

/**
 * تشخيص شامل لمشكلة التوقيع
 */
export async function debugSignatureIssue(documentId: string): Promise<SignatureDebugInfo> {
  const debug: SignatureDebugInfo = {
    documentId,
    documentExists: false,
    documentData: null,
    contentSource: 'none',
    contentLength: 0,
    userInfo: null,
    errors: []
  };

  try {
    // 1. التحقق من المستخدم الحالي
    console.log('🔍 تشخيص التوقيع - التحقق من المستخدم...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      debug.errors.push(`خطأ في جلب بيانات المستخدم: ${userError.message}`);
    } else if (!user) {
      debug.errors.push('المستخدم غير مسجل دخول');
    } else {
      debug.userInfo = {
        id: user.id,
        email: user.email,
        hasMetadata: !!user.user_metadata,
        fullName: user.user_metadata?.full_name,
        metadataKeys: Object.keys(user.user_metadata || {})
      };
      console.log('✅ بيانات المستخدم:', debug.userInfo);
    }

    // 2. التحقق من وجود المستند في قاعدة البيانات
    console.log('🔍 تشخيص التوقيع - التحقق من المستند...');
    const { data: documentData, error: docError } = await supabase
      .from('documents')
      .select('id, title, content, content_url, created_by, created_at')
      .eq('id', documentId)
      .single();

    if (docError) {
      debug.errors.push(`خطأ في جلب بيانات المستند: ${docError.message}`);
      console.error('❌ خطأ في جلب المستند:', docError);
    } else if (!documentData) {
      debug.errors.push('المستند غير موجود في قاعدة البيانات');
      console.error('❌ المستند غير موجود');
    } else {
      debug.documentExists = true;
      debug.documentData = {
        id: documentData.id,
        title: documentData.title,
        hasContent: !!documentData.content,
        hasContentUrl: !!documentData.content_url,
        contentLength: documentData.content?.length || 0,
        contentUrlPath: documentData.content_url,
        createdBy: documentData.created_by,
        createdAt: documentData.created_at
      };
      console.log('✅ بيانات المستند:', debug.documentData);

      // 3. تحديد مصدر المحتوى
      if (documentData.content) {
        debug.contentSource = 'database';
        debug.contentLength = documentData.content.length;
        console.log('📄 المحتوى موجود في قاعدة البيانات');
      } else if (documentData.content_url) {
        debug.contentSource = 'storage';
        console.log('☁️ المحتوى موجود في Storage، محاولة جلبه...');
        
        try {
          const storageContent = await DocumentStorageService.getDocumentContent(documentData.content_url);
          if (storageContent) {
            debug.contentLength = storageContent.length;
            debug.storageInfo = {
              path: documentData.content_url,
              contentLength: storageContent.length,
              success: true
            };
            console.log('✅ تم جلب المحتوى من Storage بنجاح');
          } else {
            debug.errors.push('فشل في جلب المحتوى من Storage');
            debug.storageInfo = {
              path: documentData.content_url,
              success: false,
              error: 'فشل في جلب المحتوى'
            };
            console.error('❌ فشل في جلب المحتوى من Storage');
          }
        } catch (storageError: any) {
          debug.errors.push(`خطأ في Storage: ${storageError.message}`);
          debug.storageInfo = {
            path: documentData.content_url,
            success: false,
            error: storageError.message
          };
          console.error('❌ خطأ في Storage:', storageError);
        }
      } else {
        debug.errors.push('لا يوجد محتوى في قاعدة البيانات أو Storage');
        console.error('❌ لا يوجد محتوى');
      }
    }

    // 4. التحقق من signed_documents
    console.log('🔍 تشخيص التوقيع - التحقق من signed_documents...');
    const { data: signedDoc, error: signedError } = await supabase
      .from('signed_documents')
      .select('*')
      .eq('document_id', documentId)
      .single();

    if (signedError) {
      debug.errors.push(`خطأ في جلب signed_document: ${signedError.message}`);
      console.error('❌ خطأ في جلب signed_document:', signedError);
    } else if (signedDoc) {
      console.log('✅ signed_document موجود:', {
        id: signedDoc.id,
        status: signedDoc.status,
        creatorId: signedDoc.creator_id,
        signerId: signedDoc.signer_id
      });
    }

  } catch (error: any) {
    debug.errors.push(`خطأ عام في التشخيص: ${error.message}`);
    console.error('❌ خطأ عام في التشخيص:', error);
  }

  return debug;
}

/**
 * طباعة تقرير تشخيص مفصل
 */
export function printDebugReport(debug: SignatureDebugInfo): void {
  console.log('\n📋 تقرير تشخيص التوقيع الإلكتروني');
  console.log('=====================================');
  
  console.log(`🆔 معرف المستند: ${debug.documentId}`);
  console.log(`📄 المستند موجود: ${debug.documentExists ? '✅ نعم' : '❌ لا'}`);
  console.log(`👤 المستخدم: ${debug.userInfo ? '✅ مسجل دخول' : '❌ غير مسجل'}`);
  
  if (debug.userInfo) {
    console.log(`   - المعرف: ${debug.userInfo.id}`);
    console.log(`   - البريد: ${debug.userInfo.email}`);
    console.log(`   - الاسم: ${debug.userInfo.fullName || 'غير محدد'}`);
  }
  
  console.log(`📝 مصدر المحتوى: ${debug.contentSource}`);
  console.log(`📏 طول المحتوى: ${debug.contentLength} حرف`);
  
  if (debug.storageInfo) {
    console.log(`☁️ معلومات Storage:`);
    console.log(`   - المسار: ${debug.storageInfo.path}`);
    console.log(`   - النجاح: ${debug.storageInfo.success ? '✅' : '❌'}`);
    if (debug.storageInfo.error) {
      console.log(`   - الخطأ: ${debug.storageInfo.error}`);
    }
  }
  
  if (debug.errors.length > 0) {
    console.log('\n❌ الأخطاء المكتشفة:');
    debug.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  } else {
    console.log('\n✅ لا توجد أخطاء مكتشفة');
  }
  
  console.log('\n=====================================\n');
}

/**
 * اختبار إنشاء توقيع مع بيانات تجريبية
 */
export async function testSignatureCreation(documentId: string): Promise<boolean> {
  try {
    console.log('🧪 اختبار إنشاء التوقيع...');
    
    const debug = await debugSignatureIssue(documentId);
    printDebugReport(debug);
    
    if (debug.errors.length > 0) {
      console.error('❌ لا يمكن اختبار إنشاء التوقيع بسبب وجود أخطاء');
      return false;
    }
    
    if (!debug.userInfo) {
      console.error('❌ لا يمكن اختبار إنشاء التوقيع بدون مستخدم');
      return false;
    }
    
    if (debug.contentLength === 0) {
      console.error('❌ لا يمكن اختبار إنشاء التوقيع بدون محتوى');
      return false;
    }
    
    // محاولة إنشاء توقيع تجريبي
    const { createSignature } = await import('$lib/utils/signatureUtils');
    
    const testSignature = createSignature(
      'محتوى تجريبي للاختبار',
      debug.documentData?.title || 'عنوان تجريبي',
      {
        id: debug.userInfo.id,
        email: debug.userInfo.email,
        user_metadata: { full_name: debug.userInfo.fullName }
      },
      null,
      null,
      'كلمة مرور تجريبية'
    );
    
    console.log('✅ تم إنشاء التوقيع التجريبي بنجاح:', testSignature);
    return true;
    
  } catch (error: any) {
    console.error('❌ فشل في اختبار إنشاء التوقيع:', error);
    return false;
  }
}
