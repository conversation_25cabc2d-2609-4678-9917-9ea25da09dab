<script>
  import { createEventDispatcher } from 'svelte';
  
  // المتغيرات
  export let isOpen = false;
  export let title = 'إدخال كلمة مرور التوقيع';
  export let message = 'يرجى إدخال كلمة مرور التوقيع الإلكتروني';
  
  let password = '';
  let confirmPassword = '';
  let isCreating = false;
  let error = '';
  
  // إنشاء مرسل الأحداث
  const dispatch = createEventDispatcher();
  
  // تعيين وضع الإنشاء أو التحقق
  export function setMode(creating) {
    isCreating = creating;
    password = '';
    confirmPassword = '';
    error = '';
  }
  
  // إغلاق النافذة
  function close() {
    isOpen = false;
    password = '';
    confirmPassword = '';
    error = '';
    dispatch('close');
  }
  
  // التحقق من كلمة المرور
  function validatePassword() {
    if (!password) {
      error = 'يرجى إدخال كلمة المرور';
      return false;
    }
    
    if (isCreating) {
      if (password.length < 6) {
        error = 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';
        return false;
      }
      
      if (password !== confirmPassword) {
        error = 'كلمات المرور غير متطابقة';
        return false;
      }
    }
    
    return true;
  }
  
  // معالجة الإرسال
  function handleSubmit() {
    error = '';
    
    if (!validatePassword()) {
      return;
    }
    
    // إرسال كلمة المرور
    dispatch('submit', { password });
    
    // إغلاق النافذة
    close();
  }
</script>

{#if isOpen}
  <div class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md p-6 relative">
      <!-- رأس النافذة -->
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-900 dark:text-white">{title}</h2>
        <button
          class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          on:click={close}
          aria-label="إغلاق"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>
      
      <!-- محتوى النافذة -->
      <div class="mb-6">
        <p class="text-gray-700 dark:text-gray-300 mb-4">{message}</p>
        
        {#if error}
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        {/if}
        
        <form on:submit|preventDefault={handleSubmit}>
          <div class="mb-4">
            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              كلمة المرور
            </label>
            <input
              type="password"
              id="password"
              bind:value={password}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="أدخل كلمة المرور"
            />
          </div>
          
          {#if isCreating}
            <div class="mb-4">
              <label for="confirmPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                تأكيد كلمة المرور
              </label>
              <input
                type="password"
                id="confirmPassword"
                bind:value={confirmPassword}
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="أعد إدخال كلمة المرور"
              />
            </div>
          {/if}
          
          <div class="flex justify-end gap-2 mt-6">
            <button
              type="button"
              class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              on:click={close}
            >
              إلغاء
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {isCreating ? 'إنشاء التوقيع' : 'تأكيد'}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
{/if}
