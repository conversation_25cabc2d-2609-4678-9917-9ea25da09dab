<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';
  import { DocumentService } from '$lib/services/documentService';

  export let data: { params: { id: string } };

  type Document = {
    id: string;
    title: string;
    document_number: string;
    document_date: string;
    document_type: string;
    sender: string;
    receiver: string;
    notes: string;
    created_by: string;
    unit_id: string;
    created_at: string;
    updated_at: string;
    creator?: {
      full_name: string;
    };
    unit?: {
      name: string;
    };
  };

  type Attachment = {
    id: string;
    document_id: string;
    file_name: string;
    file_path: string;
    file_type: string;
    file_size: number;
    created_by: string;
    created_at: string;
    file_url?: string;
  };

  type Reader = {
    id: string;
    document_id: string;
    user_id: string;
    read_at: string;
    user?: {
      full_name: string;
    };
  };

  let document: Document | null = null;
  let attachments: Attachment[] = [];
  let readers: Reader[] = [];
  let loading = true;
  let error = '';
  let currentUserId = '';
  let uploadingFile = false;

  onMount(async () => {
    try {
      console.log('تم تحميل صفحة المستند للمعرف:', data.params.id);

      // التحقق من المستخدم الحالي
      console.log('التحقق من المستخدم الحالي...');
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError) {
        console.error('خطأ في الحصول على المستخدم:', userError);
        error = 'حدث خطأ أثناء التحقق من المستخدم: ' + userError.message;
        return;
      }

      if (!user) {
        console.error('لم يتم العثور على مستخدم');
        error = 'يجب تسجيل الدخول لعرض المستند';
        return;
      }

      console.log('المستخدم الحالي:', user.id);
      currentUserId = user.id;

      // التحقق من صلاحية المستخدم للوصول إلى المستند
      console.log('التحقق من صلاحيات الوصول إلى المستند...');
      const canAccess = await DocumentService.canAccessDocument(user.id, data.params.id);

      if (!canAccess) {
        console.error('المستخدم ليس لديه صلاحية للوصول إلى هذا المستند');
        error = 'ليس لديك صلاحية للوصول إلى هذا المستند';
        return;
      }

      console.log('المستخدم لديه صلاحية للوصول إلى المستند');

      // تحقق مما إذا كان المستخدم قد قرأ المستند بالفعل
      console.log('التحقق مما إذا كان المستخدم قد قرأ المستند بالفعل...');
      try {
        const { data: existingReader, error: readerError } = await supabase
          .from('document_readers')
          .select('id')
          .eq('document_id', data.params.id)
          .eq('user_id', user.id)
          .maybeSingle();

        if (readerError) {
          console.error('خطأ في التحقق من قراء المستند:', readerError);
          // لا نتوقف عند حدوث خطأ في التحقق من القراء
        } else {
          if (!existingReader) {
            console.log('تسجيل قراءة جديدة للمستند...');
            // تسجيل قراءة جديدة
            const { error: insertError } = await supabase
              .from('document_readers')
              .insert({
                document_id: data.params.id,
                user_id: user.id
              });

            if (insertError) {
              console.error('خطأ في تسجيل قراءة المستند:', insertError);
              // لا نتوقف عند حدوث خطأ في تسجيل القراءة
            } else {
              console.log('تم تسجيل قراءة جديدة للمستند بنجاح');
            }
          } else {
            console.log('المستخدم قرأ هذا المستند بالفعل');
          }
        }
      } catch (readerErr) {
        console.error('استثناء في التحقق من قارئ المستند:', readerErr);
        // لا نتوقف عند حدوث خطأ في التحقق من القراء
      }

      // جلب بيانات المستند
      console.log('جلب بيانات المستند...');
      await fetchDocumentData();
    } catch (err: any) {
      console.error('استثناء في onMount:', err);
      error = 'حدث خطأ أثناء تحميل المستند: ' + (err.message || 'خطأ غير معروف');
    } finally {
      console.log('اكتمل onMount. error:', error);
    }
  });

  async function fetchDocumentData() {
    try {
      console.log('بدء جلب بيانات المستند للمعرف:', data.params.id);
      loading = true;

      // جلب بيانات المستند
      console.log('جلب بيانات المستند الأساسية...');
      const { data: documentData, error: documentError } = await supabase
        .from('documents')
        .select(`
          *,
          creator:created_by(full_name),
          unit:unit_id(name)
        `)
        .eq('id', data.params.id)
        .single();

      if (documentError) {
        console.error('خطأ في جلب بيانات المستند:', documentError);
        error = 'حدث خطأ أثناء جلب بيانات المستند: ' + documentError.message;
        return;
      }

      if (!documentData) {
        console.error('لم يتم العثور على بيانات المستند للمعرف:', data.params.id);
        error = 'لم يتم العثور على المستند';
        return;
      }

      console.log('تم جلب بيانات المستند بنجاح:', documentData.id, documentData.title);
      document = documentData;

      // جلب المرفقات
      console.log('جلب مرفقات المستند...');
      try {
        const { data: attachmentsData, error: attachmentsError } = await supabase
          .from('document_attachments')
          .select('*')
          .eq('document_id', data.params.id)
          .order('created_at', { ascending: false });

        if (attachmentsError) {
          console.error('خطأ في جلب المرفقات:', attachmentsError);
          // لا نتوقف عند حدوث خطأ في جلب المرفقات
        } else {
          console.log('تم جلب المرفقات بنجاح:', attachmentsData?.length || 0);
          attachments = attachmentsData.map(attachment => ({
            ...attachment,
            file_url: supabase.storage.from('documents').getPublicUrl(attachment.file_path).data.publicUrl
          }));
        }
      } catch (attachErr) {
        console.error('استثناء في جلب المرفقات:', attachErr);
        // لا نتوقف عند حدوث خطأ في جلب المرفقات
      }

      // جلب قراء المستند
      console.log('جلب قراء المستند...');
      try {
        const { data: readersData, error: readersError } = await supabase
          .from('document_readers')
          .select(`
            *,
            user:user_id(full_name)
          `)
          .eq('document_id', data.params.id)
          .order('read_at', { ascending: false });

        if (readersError) {
          console.error('خطأ في جلب القراء:', readersError);
          // لا نتوقف عند حدوث خطأ في جلب القراء
        } else {
          console.log('تم جلب القراء بنجاح:', readersData?.length || 0);
          readers = readersData || [];
        }
      } catch (readersErr) {
        console.error('استثناء في جلب القراء:', readersErr);
        // لا نتوقف عند حدوث خطأ في جلب القراء
      }

      console.log('اكتمل جلب بيانات المستند بنجاح');
    } catch (err: any) {
      console.error('استثناء في fetchDocumentData:', err);
      error = 'حدث خطأ أثناء جلب بيانات المستند: ' + (err.message || 'خطأ غير معروف');
    } finally {
      loading = false;
      console.log('اكتمل fetchDocumentData. loading:', loading, 'error:', error, 'document:', document?.id);
    }
  }

  async function handleFileUpload(event: Event) {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    if (!files || files.length === 0) return;

    try {
      uploadingFile = true;

      // التحقق من صلاحية المستخدم للوصول إلى المستند
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        alert('يجب تسجيل الدخول لإضافة مرفقات');
        return;
      }

      // التحقق من صلاحية المستخدم للوصول إلى المستند
      const canAccess = await DocumentService.canAccessDocument(user.id, data.params.id);

      if (!canAccess) {
        alert('ليس لديك صلاحية لإضافة مرفقات لهذا المستند');
        return;
      }

      // التحقق من أن المستخدم هو منشئ المستند أو مشرف
      if (document && document.created_by !== user.id) {
        // التحقق من دور المستخدم
        const { data: profile } = await supabase
          .from('profiles')
          .select('role_id')
          .eq('id', user.id)
          .single();

        if (profile && profile.role_id) {
          const { data: role } = await supabase
            .from('roles')
            .select('name')
            .eq('id', profile.role_id)
            .single();

          const isAdmin = !!role && (role.name === 'admin' || role.name === 'مشرف');

          if (!isAdmin) {
            alert('فقط منشئ المستند أو المشرف يمكنه إضافة مرفقات');
            return;
          }
        } else {
          alert('فقط منشئ المستند أو المشرف يمكنه إضافة مرفقات');
          return;
        }
      }

      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // إنشاء اسم فريد للملف
        const fileExt = file.name.split('.').pop();
        const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = `document-${data.params.id}/${fileName}`;

        // رفع الملف إلى Supabase Storage
        const { error: uploadError } = await supabase.storage
          .from('documents')
          .upload(filePath, file);

        if (uploadError) {
          throw uploadError;
        }

        // إضافة المرفق إلى قاعدة البيانات
        const { error: insertError } = await supabase
          .from('document_attachments')
          .insert({
            document_id: data.params.id,
            file_name: file.name,
            file_path: filePath,
            file_type: file.type,
            file_size: file.size,
            created_by: currentUserId
          });

        if (insertError) {
          throw insertError;
        }
      }

      // تحديث قائمة المرفقات
      await fetchDocumentData();

      // إعادة تعيين حقل الملفات
      target.value = '';
    } catch (err) {
      console.error('خطأ في رفع الملف:', err);
      alert('حدث خطأ أثناء رفع الملف');
    } finally {
      uploadingFile = false;
    }
  }

  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  }

  function formatFileSize(bytes: number): string {
    if (bytes < 1024) {
      return bytes + ' بايت';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(2) + ' كيلوبايت';
    } else {
      return (bytes / (1024 * 1024)).toFixed(2) + ' ميجابايت';
    }
  }
</script>

<div>
  <div class="mb-6">
    <a
      href="/dashboard/documents"
      class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="m15 18-6-6 6-6"/></svg>
      العودة إلى المستندات
    </a>
  </div>

  {#if loading}
    <div class="flex justify-center items-center h-64">
      <p class="text-lg">جاري التحميل...</p>
    </div>
  {:else if error}
    <div class="bg-destructive/20 p-4 rounded-md text-destructive">
      <p>{error}</p>
    </div>
  {:else if document}
    <div class="mb-6 flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold">{document.title}</h1>
        <p class="text-muted-foreground">
          رقم المستند: {document.document_number || 'غير محدد'}
        </p>
      </div>

      <div class="px-2.5 py-0.5 rounded-full text-xs font-semibold inline-flex items-center bg-primary text-primary-foreground">
        {document.document_type}
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="md:col-span-2">
        <div class="bg-card rounded-lg shadow p-6 mb-6">
          <h2 class="text-xl font-bold mb-4">تفاصيل المستند</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="font-semibold mb-2">معلومات أساسية</h3>
              <ul class="space-y-2">
                <li class="flex justify-between">
                  <span class="text-muted-foreground">العنوان:</span>
                  <span>{document.title}</span>
                </li>
                <li class="flex justify-between">
                  <span class="text-muted-foreground">الرقم:</span>
                  <span>{document.document_number || 'غير محدد'}</span>
                </li>
                <li class="flex justify-between">
                  <span class="text-muted-foreground">التاريخ:</span>
                  <span>{formatDate(document.document_date)}</span>
                </li>
                <li class="flex justify-between">
                  <span class="text-muted-foreground">النوع:</span>
                  <span>{document.document_type}</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 class="font-semibold mb-2">معلومات إضافية</h3>
              <ul class="space-y-2">
                <li class="flex justify-between">
                  <span class="text-muted-foreground">المرسل:</span>
                  <span>{document.sender}</span>
                </li>
                <li class="flex justify-between">
                  <span class="text-muted-foreground">المستقبل:</span>
                  <span>{document.receiver}</span>
                </li>
                <li class="flex justify-between">
                  <span class="text-muted-foreground">القسم:</span>
                  <span>{document.unit?.name || 'غير محدد'}</span>
                </li>
                <li class="flex justify-between">
                  <span class="text-muted-foreground">تاريخ الإنشاء:</span>
                  <span>{formatDate(document.created_at)}</span>
                </li>
              </ul>
            </div>
          </div>

          {#if document.notes}
            <div class="mt-6">
              <h3 class="font-semibold mb-2">ملاحظات</h3>
              <p class="p-3 bg-muted rounded-md">{document.notes}</p>
            </div>
          {/if}
        </div>

        <div class="bg-card rounded-lg shadow p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">المرفقات ({attachments.length})</h2>

            <label class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 cursor-pointer">
              <input
                type="file"
                class="hidden"
                multiple
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                on:change={handleFileUpload}
                disabled={uploadingFile}
              />
              {uploadingFile ? 'جاري الرفع...' : 'إضافة مرفقات'}
            </label>
          </div>

          {#if attachments.length === 0}
            <p class="text-center text-muted-foreground py-4">لا توجد مرفقات لهذا المستند</p>
          {:else}
            <div class="space-y-4">
              {#each attachments as attachment}
                <div class="flex items-center justify-between p-3 border rounded-md">
                  <div class="flex items-center">
                    <div class="mr-3">
                      {#if attachment.file_type.includes('pdf')}
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
                      {:else if attachment.file_type.includes('image')}
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="8.5" cy="8.5" r="1.5"/><polyline points="21 15 16 10 5 21"/></svg>
                      {:else if attachment.file_type.includes('word')}
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-700"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
                      {:else if attachment.file_type.includes('excel') || attachment.file_type.includes('sheet')}
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
                      {:else}
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
                      {/if}
                    </div>
                    <div>
                      <p class="font-medium">{attachment.file_name}</p>
                      <p class="text-sm text-muted-foreground">{formatFileSize(attachment.file_size)} • {formatDate(attachment.created_at)}</p>
                    </div>
                  </div>
                  <div>
                    <a href={attachment.file_url} target="_blank" rel="noopener noreferrer">
                      <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3">
                        عرض
                      </button>
                    </a>
                  </div>
                </div>
              {/each}
            </div>
          {/if}
        </div>
      </div>

      <div>
        <div class="bg-card rounded-lg shadow p-6">
          <h2 class="text-xl font-bold mb-4">قراء المستند ({readers.length})</h2>

          {#if readers.length === 0}
            <p class="text-center text-muted-foreground py-4">لم يقم أحد بقراءة هذا المستند بعد</p>
          {:else}
            <div class="space-y-2">
              {#each readers as reader}
                <div class="flex justify-between items-center p-2 border-b last:border-0">
                  <span>{reader.user?.full_name || 'مستخدم غير معروف'}</span>
                  <span class="text-sm text-muted-foreground">{formatDate(reader.read_at)}</span>
                </div>
              {/each}
            </div>
          {/if}
        </div>
      </div>
    </div>
  {:else}
    <div class="bg-destructive/20 p-4 rounded-md text-destructive">
      <p>لم يتم العثور على المستند</p>
    </div>
  {/if}
</div>
