// دالة Supabase Edge لإنشاء وثيقة رسمية
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import { corsHeaders } from '../_shared/cors.ts'

interface CreateDocumentRequest {
  title: string
  content: string
  document_type: string
  unit_id: string
  recipient_id?: string
  recipient_unit_id?: string
  recipient_external?: string
  is_signed: boolean
  signature?: any
  password_protected?: boolean
  attachments?: any[]
  metadata?: any
}

serve(async (req) => {
  // التعامل مع طلبات CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // التحقق من طريقة الطلب
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // استخراج بيانات الطلب
    const requestData: CreateDocumentRequest = await req.json()

    // التحقق من البيانات المطلوبة
    if (!requestData.title || !requestData.content) {
      return new Response(JSON.stringify({ error: 'Title and content are required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // إنشاء عميل Supabase
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! }
        }
      }
    )

    // الحصول على المستخدم الحالي
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser()

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // التحقق من صلاحية المستخدم لإنشاء وثيقة رسمية
    const { data: canCreate, error: permissionError } = await supabaseClient.rpc(
      'can_create_official_document',
      { user_id: user.id }
    )

    if (permissionError || !canCreate) {
      return new Response(JSON.stringify({ error: 'Permission denied' }), {
        status: 403,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // معالجة المرفقات إذا وجدت
    let processedAttachments = null
    if (requestData.attachments && requestData.attachments.length > 0) {
      processedAttachments = await processAttachments(supabaseClient, requestData.attachments)
    }

    // إنشاء الوثيقة الرسمية
    const { data: document, error: documentError } = await supabaseClient
      .from('official_documents')
      .insert({
        title: requestData.title,
        content: requestData.content,
        document_type: requestData.document_type || 'letter',
        status: 'draft',
        creator_id: user.id,
        unit_id: requestData.unit_id,
        recipient_id: requestData.recipient_id,
        recipient_unit_id: requestData.recipient_unit_id,
        recipient_external: requestData.recipient_external,
        is_signed: requestData.is_signed || false,
        signature: requestData.signature,
        password_protected: requestData.password_protected || false,
        attachments: processedAttachments,
        metadata: requestData.metadata
      })
      .select()
      .single()

    if (documentError) {
      return new Response(JSON.stringify({ error: documentError.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // إرجاع الوثيقة المنشأة
    return new Response(JSON.stringify({ success: true, data: document }), {
      status: 201,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

// دالة لمعالجة المرفقات
async function processAttachments(supabase: any, attachments: any[]) {
  const processedAttachments = []

  for (const attachment of attachments) {
    // إذا كان المرفق عبارة عن مسار ملف موجود بالفعل
    if (typeof attachment === 'string') {
      processedAttachments.push({
        path: attachment,
        name: attachment.split('/').pop(),
        type: getFileType(attachment)
      })
      continue
    }

    // إذا كان المرفق عبارة عن كائن يحتوي على بيانات الملف
    if (attachment.data && attachment.name) {
      const fileExt = attachment.name.split('.').pop()
      const filePath = `saadabujnah/attachments/${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`

      // تحميل الملف إلى التخزين
      const { data, error } = await supabase.storage
        .from('saadabujnah')
        .upload(filePath, decode(attachment.data), {
          contentType: attachment.type || 'application/octet-stream',
          upsert: true
        })

      if (error) {
        throw new Error(`Error uploading attachment: ${error.message}`)
      }

      processedAttachments.push({
        path: filePath,
        name: attachment.name,
        type: attachment.type || getFileType(attachment.name)
      })
    }
  }

  return processedAttachments
}

// دالة لتحديد نوع الملف
function getFileType(filename: string): string {
  const ext = filename.split('.').pop()?.toLowerCase()
  
  const mimeTypes: Record<string, string> = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'txt': 'text/plain'
  }
  
  return ext && mimeTypes[ext] ? mimeTypes[ext] : 'application/octet-stream'
}

// دالة لفك ترميز البيانات المشفرة بـ Base64
function decode(base64String: string): Uint8Array {
  const binary = atob(base64String.replace(/^data:.+;base64,/, ''))
  const bytes = new Uint8Array(binary.length)
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i)
  }
  return bytes
}
