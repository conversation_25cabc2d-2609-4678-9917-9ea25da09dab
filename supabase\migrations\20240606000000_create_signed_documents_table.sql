-- إنشاء جدول المستندات الموقعة (signed_documents)

-- إنشاء الجدول إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.signed_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES public.documents(id) ON DELETE CASCADE,
  creator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  signer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'pending_signature' CHECK (status IN ('pending_signature', 'signed', 'rejected', 'revision_requested')),
  reference_number TEXT UNIQUE,
  signature JSONB,
  rejection_reason TEXT,
  revision_comments TEXT,
  signed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_signed_documents_document_id ON public.signed_documents(document_id);
CREATE INDEX IF NOT EXISTS idx_signed_documents_creator_id ON public.signed_documents(creator_id);
CREATE INDEX IF NOT EXISTS idx_signed_documents_signer_id ON public.signed_documents(signer_id);
CREATE INDEX IF NOT EXISTS idx_signed_documents_status ON public.signed_documents(status);
CREATE INDEX IF NOT EXISTS idx_signed_documents_reference_number ON public.signed_documents(reference_number);
CREATE INDEX IF NOT EXISTS idx_signed_documents_created_at ON public.signed_documents(created_at);

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_signed_documents_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_signed_documents_updated_at ON public.signed_documents;
CREATE TRIGGER trigger_update_signed_documents_updated_at
  BEFORE UPDATE ON public.signed_documents
  FOR EACH ROW
  EXECUTE FUNCTION update_signed_documents_updated_at();

-- تفعيل Row Level Security
ALTER TABLE public.signed_documents ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات الأمان

-- سياسة القراءة: يمكن للمنشئ والموقع قراءة المستند
CREATE POLICY signed_documents_select_policy ON public.signed_documents
FOR SELECT USING (
  auth.uid() = creator_id OR 
  auth.uid() = signer_id OR
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role IN ('مشرف', 'مدير', 'admin', 'manager')
  )
);

-- سياسة الإنشاء: يمكن للمستخدمين المصرح لهم إنشاء طلبات توقيع
CREATE POLICY signed_documents_insert_policy ON public.signed_documents
FOR INSERT WITH CHECK (
  auth.uid() = creator_id AND
  EXISTS (
    SELECT 1 FROM user_permissions up
    JOIN permissions p ON up.permission_id = p.id
    WHERE up.user_id = auth.uid()
    AND p.resource = 'electronic_signature'
    AND p.action = 'create'
  )
);

-- سياسة التحديث: يمكن للموقع تحديث حالة التوقيع
CREATE POLICY signed_documents_update_policy ON public.signed_documents
FOR UPDATE USING (
  auth.uid() = signer_id OR
  auth.uid() = creator_id OR
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role IN ('مشرف', 'مدير', 'admin', 'manager')
  )
);

-- سياسة الحذف: يمكن للمنشئ أو المشرف حذف طلب التوقيع
CREATE POLICY signed_documents_delete_policy ON public.signed_documents
FOR DELETE USING (
  auth.uid() = creator_id OR
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role IN ('مشرف', 'مدير', 'admin', 'manager')
  )
);

-- إضافة تعليقات للجدول والأعمدة
COMMENT ON TABLE public.signed_documents IS 'جدول المستندات الموقعة وطلبات التوقيع';
COMMENT ON COLUMN public.signed_documents.id IS 'معرف فريد لطلب التوقيع';
COMMENT ON COLUMN public.signed_documents.document_id IS 'معرف المستند المراد توقيعه';
COMMENT ON COLUMN public.signed_documents.creator_id IS 'معرف منشئ طلب التوقيع';
COMMENT ON COLUMN public.signed_documents.signer_id IS 'معرف الموقع المطلوب';
COMMENT ON COLUMN public.signed_documents.status IS 'حالة طلب التوقيع';
COMMENT ON COLUMN public.signed_documents.reference_number IS 'الرقم المرجعي لطلب التوقيع';
COMMENT ON COLUMN public.signed_documents.signature IS 'بيانات التوقيع الإلكتروني';
COMMENT ON COLUMN public.signed_documents.rejection_reason IS 'سبب الرفض إذا تم رفض التوقيع';
COMMENT ON COLUMN public.signed_documents.revision_comments IS 'تعليقات طلب التعديل';
COMMENT ON COLUMN public.signed_documents.signed_at IS 'تاريخ ووقت التوقيع';

-- إنشاء view لعرض المستندات مع تفاصيل إضافية
CREATE OR REPLACE VIEW public.signed_documents_with_details AS
SELECT 
  sd.*,
  d.title as document_title,
  d.content as document_content,
  creator.full_name as creator_name,
  creator.email as creator_email,
  signer.full_name as signer_name,
  signer.email as signer_email,
  creator_unit.name as creator_unit_name,
  signer_unit.name as signer_unit_name
FROM public.signed_documents sd
LEFT JOIN public.documents d ON sd.document_id = d.id
LEFT JOIN public.profiles creator ON sd.creator_id = creator.id
LEFT JOIN public.profiles signer ON sd.signer_id = signer.id
LEFT JOIN public.units creator_unit ON creator.unit_id = creator_unit.id
LEFT JOIN public.units signer_unit ON signer.unit_id = signer_unit.id;

-- منح الصلاحيات للمستخدمين المصرح لهم
GRANT SELECT, INSERT, UPDATE, DELETE ON public.signed_documents TO authenticated;
GRANT SELECT ON public.signed_documents_with_details TO authenticated;

-- تحديث ذاكرة التخزين المؤقت للمخطط
NOTIFY pgrst, 'reload schema';
