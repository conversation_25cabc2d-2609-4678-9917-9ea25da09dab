<script lang="ts">
  import { onMount } from 'svelte';
  import { PermissionService } from '$lib/services/permissionService';
  import { getRoleNameArabic, getPermissionNameArabic } from '$lib/types/permissions';
  import type { Role, Permission } from '$lib/types/permissions';
  import { supabase } from '$lib/supabase';

  // متغيرات البيانات الأساسية
  let roles: Role[] = [];
  let permissions: Permission[] = [];
  let selectedRole: Role | null = null;
  let rolePermissions: Permission[] = [];
  let loading = true;
  let error = '';
  let directDbCheck: { roles: any[], permissions: any[], rolePermissions: any[] } = { roles: [], permissions: [], rolePermissions: [] };

  // متغيرات لإدارة الأدوار
  let showAddRoleModal = false;
  let showEditRoleModal = false;
  let showDeleteRoleModal = false;
  let newRoleName = '';
  let newRoleDescription = '';
  let editingRole: Role | null = null;
  let deletingRole: Role | null = null;

  // متغيرات لعرض الصلاحيات
  let showPermissionDetails = false;
  let selectedPermission: Permission | null = null;
  let permissionFilter = '';
  let showHelp = false;

  // التحقق المباشر من قاعدة البيانات
  async function checkDatabaseDirectly() {
    try {
      console.log('Checking database directly...');

      // التحقق من جدول الأدوار
      const { data: rolesData, error: rolesError } = await supabase
        .from('roles')
        .select('*');

      if (rolesError) {
        console.error('Direct DB check - Error fetching roles:', rolesError);
        return false;
      }

      directDbCheck.roles = rolesData || [];
      console.log('Direct DB check - Roles:', directDbCheck.roles);

      // التحقق من جدول الصلاحيات
      const { data: permissionsData, error: permissionsError } = await supabase
        .from('permissions')
        .select('*');

      if (permissionsError) {
        console.error('Direct DB check - Error fetching permissions:', permissionsError);
        return false;
      }

      directDbCheck.permissions = permissionsData || [];
      console.log('Direct DB check - Permissions:', directDbCheck.permissions);

      // التحقق من جدول العلاقة بين الأدوار والصلاحيات
      const { data: rolePermissionsData, error: rolePermissionsError } = await supabase
        .from('role_permissions')
        .select('*');

      if (rolePermissionsError) {
        console.error('Direct DB check - Error fetching role permissions:', rolePermissionsError);
        return false;
      }

      directDbCheck.rolePermissions = rolePermissionsData || [];
      console.log('Direct DB check - Role Permissions:', directDbCheck.rolePermissions);

      return true;
    } catch (err) {
      console.error('Error in direct database check:', err);
      return false;
    }
  }

  // تحميل البيانات
  onMount(async () => {
    try {
      console.log('Component mounted, loading data...');
      loading = true;
      error = '';

      // التحقق المباشر من قاعدة البيانات
      const dbCheckResult = await checkDatabaseDirectly();

      if (!dbCheckResult) {
        console.error('Direct database check failed');
        error = 'فشل الاتصال المباشر بقاعدة البيانات. يرجى التحقق من الاتصال.';
        loading = false;
        return;
      }

      if (directDbCheck.roles.length === 0) {
        console.error('No roles found in direct database check');
        error = 'لم يتم العثور على بيانات الأدوار في قاعدة البيانات. يرجى التحقق من إعداد قاعدة البيانات.';
        loading = false;
        return;
      }

      // استخدام البيانات من التحقق المباشر
      roles = directDbCheck.roles;
      permissions = directDbCheck.permissions;

      console.log('Using data from direct database check');
      console.log('Roles loaded:', roles.length);
      console.log('Permissions loaded:', permissions.length);

      // تحديد الدور الافتراضي
      if (roles.length > 0) {
        console.log('Selecting default role:', roles[0]);
        await selectRole(roles[0]);
      }
    } catch (err) {
      console.error('Error loading roles and permissions:', err);
      error = 'حدث خطأ أثناء تحميل البيانات';
    } finally {
      loading = false;
      console.log('Loading completed, state:', { roles: roles.length, permissions: permissions.length, error });
    }
  });

  // تحديد دور
  async function selectRole(role: Role) {
    try {
      error = '';
      selectedRole = role;
      console.log(`Selecting role: ${role.name} (${role.id})`);

      // استخدام الاتصال المباشر بقاعدة البيانات
      console.log('Fetching role permissions directly from database...');

      const { data, error: fetchError } = await supabase
        .from('role_permissions')
        .select(`
          permission_id,
          permissions:permission_id (*)
        `)
        .eq('role_id', role.id);

      if (fetchError) {
        console.error('Error fetching role permissions directly:', fetchError);
        error = 'حدث خطأ أثناء تحميل صلاحيات الدور';
        return;
      }

      // استخراج الصلاحيات من البيانات
      const permissionsData: Permission[] = [];

      if (data && data.length > 0) {
        for (const item of data) {
          if (item && item.permissions) {
            permissionsData.push(item.permissions as unknown as Permission);
          }
        }
      }

      rolePermissions = permissionsData;
      console.log(`Permissions loaded for role ${role.name}:`, rolePermissions.length);

      // التحقق من وجود بيانات
      if (rolePermissions.length === 0) {
        console.log(`No permissions found for role: ${role.name}`);
        // هذا ليس خطأ، فقد لا يكون للدور أي صلاحيات بعد
      }
    } catch (err) {
      console.error('Error selecting role:', err);
      error = 'حدث خطأ أثناء تحميل صلاحيات الدور';
    }
  }

  // التحقق من وجود صلاحية للدور
  function hasPermission(permissionId: string): boolean {
    if (!rolePermissions || rolePermissions.length === 0) return false;
    return rolePermissions.some(p => p && p.id === permissionId);
  }

  // تغيير حالة صلاحية للدور
  async function togglePermission(permissionId: string) {
    if (!selectedRole) return;

    try {
      error = '';
      const hasPermissionAlready = hasPermission(permissionId);
      let success = false;

      if (hasPermissionAlready) {
        console.log(`Removing permission ${permissionId} from role ${selectedRole.name}`);

        // حذف الصلاحية مباشرة من قاعدة البيانات
        const { error: deleteError } = await supabase
          .from('role_permissions')
          .delete()
          .eq('role_id', selectedRole.id)
          .eq('permission_id', permissionId);

        if (deleteError) {
          console.error('Error removing permission directly:', deleteError);
          error = 'حدث خطأ أثناء حذف الصلاحية';
          return;
        }

        success = true;
        if (success) {
          rolePermissions = rolePermissions.filter(p => p && p.id !== permissionId);
        }
      } else {
        console.log(`Adding permission ${permissionId} to role ${selectedRole.name}`);

        // إضافة الصلاحية مباشرة إلى قاعدة البيانات
        const { error: insertError } = await supabase
          .from('role_permissions')
          .insert({
            role_id: selectedRole.id,
            permission_id: permissionId
          });

        if (insertError) {
          console.error('Error adding permission directly:', insertError);
          error = 'حدث خطأ أثناء إضافة الصلاحية';
          return;
        }

        success = true;
        if (success) {
          const permission = permissions.find(p => p.id === permissionId);
          if (permission) {
            rolePermissions = [...rolePermissions, permission];
          }
        }
      }

      if (!success) {
        error = 'حدث خطأ أثناء تعديل الصلاحيات';
      }
    } catch (err) {
      console.error('Error toggling permission:', err);
      error = 'حدث خطأ أثناء تعديل الصلاحيات';
    }
  }

  // إضافة دور جديد
  async function addRole() {
    if (!newRoleName.trim()) {
      error = 'يرجى إدخال اسم الدور';
      return;
    }

    try {
      error = '';
      loading = true;

      const role = await PermissionService.createRole(newRoleName.trim(), newRoleDescription.trim() || undefined);

      if (role) {
        roles = [...roles, role];
        newRoleName = '';
        newRoleDescription = '';
        showAddRoleModal = false;

        // تحديد الدور الجديد
        await selectRole(role);
      } else {
        error = 'حدث خطأ أثناء إنشاء الدور';
      }
    } catch (err) {
      console.error('Error adding role:', err);
      error = 'حدث خطأ أثناء إنشاء الدور';
    } finally {
      loading = false;
    }
  }

  // تعديل دور
  async function updateRole() {
    if (!editingRole || !editingRole.name.trim()) {
      error = 'يرجى إدخال اسم الدور';
      return;
    }

    try {
      error = '';
      loading = true;

      const { error: updateError } = await supabase
        .from('roles')
        .update({
          name: editingRole.name.trim(),
          description: editingRole.description?.trim() || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingRole.id);

      if (updateError) {
        console.error('Error updating role:', updateError);
        error = 'حدث خطأ أثناء تحديث الدور';
        return;
      }

      // تحديث القائمة
      if (editingRole) {
        roles = roles.map(r => r.id === editingRole.id ? editingRole : r);
      }

      // إذا كان الدور المحدد هو الذي تم تعديله
      if (selectedRole && selectedRole.id === editingRole.id) {
        selectedRole = { ...editingRole };
      }

      showEditRoleModal = false;
      editingRole = null;
    } catch (err) {
      console.error('Error updating role:', err);
      error = 'حدث خطأ أثناء تحديث الدور';
    } finally {
      loading = false;
    }
  }

  // حذف دور
  async function deleteRole() {
    if (!deletingRole) return;

    try {
      error = '';
      loading = true;

      // التحقق من وجود مستخدمين مرتبطين بهذا الدور
      const { data: usersWithRole, error: usersError } = await supabase
        .from('profiles')
        .select('id')
        .eq('role_id', deletingRole.id);

      if (usersError) {
        console.error('Error checking users with role:', usersError);
        error = 'حدث خطأ أثناء التحقق من المستخدمين المرتبطين بالدور';
        return;
      }

      if (usersWithRole && usersWithRole.length > 0) {
        error = `لا يمكن حذف الدور لأنه مرتبط بـ ${usersWithRole.length} مستخدم`;
        return;
      }

      // حذف العلاقات مع الصلاحيات أولاً
      const { error: deleteRelationsError } = await supabase
        .from('role_permissions')
        .delete()
        .eq('role_id', deletingRole.id);

      if (deleteRelationsError) {
        console.error('Error deleting role permissions:', deleteRelationsError);
        error = 'حدث خطأ أثناء حذف علاقات الدور';
        return;
      }

      // حذف الدور
      const { error: deleteRoleError } = await supabase
        .from('roles')
        .delete()
        .eq('id', deletingRole.id);

      if (deleteRoleError) {
        console.error('Error deleting role:', deleteRoleError);
        error = 'حدث خطأ أثناء حذف الدور';
        return;
      }

      // تحديث القائمة
      roles = roles.filter(r => r.id !== deletingRole.id);

      // إذا كان الدور المحدد هو الذي تم حذفه
      if (selectedRole && selectedRole.id === deletingRole.id) {
        selectedRole = roles.length > 0 ? roles[0] : null;
        if (selectedRole) {
          await selectRole(selectedRole);
        } else {
          rolePermissions = [];
        }
      }

      showDeleteRoleModal = false;
      deletingRole = null;
    } catch (err) {
      console.error('Error deleting role:', err);
      error = 'حدث خطأ أثناء حذف الدور';
    } finally {
      loading = false;
    }
  }

  // فتح نافذة تعديل الدور
  function openEditRoleModal(role: Role) {
    editingRole = { ...role };
    showEditRoleModal = true;
  }

  // فتح نافذة حذف الدور
  function openDeleteRoleModal(role: Role) {
    deletingRole = role;
    showDeleteRoleModal = true;
  }

  // تجميع الصلاحيات حسب المورد
  function groupPermissionsByResource(): Record<string, Permission[]> {
    const grouped: Record<string, Permission[]> = {};

    if (!permissions || permissions.length === 0) return grouped;

    // تصفية الصلاحيات حسب البحث
    const filteredPermissions = permissionFilter
      ? permissions.filter(p =>
          p.name.includes(permissionFilter) ||
          p.description?.includes(permissionFilter) ||
          p.resource.includes(permissionFilter) ||
          p.action.includes(permissionFilter) ||
          getPermissionNameArabic(p.resource, p.action).includes(permissionFilter)
        )
      : permissions;

    filteredPermissions.forEach(permission => {
      if (!permission || !permission.resource) return;

      if (!grouped[permission.resource]) {
        grouped[permission.resource] = [];
      }
      grouped[permission.resource].push(permission);
    });

    return grouped;
  }

  // عرض تفاصيل الصلاحية
  function viewPermissionDetails(permission: Permission) {
    selectedPermission = permission;
    showPermissionDetails = true;
  }

  // تبديل عرض المساعدة
  function toggleHelp() {
    showHelp = !showHelp;
  }

  $: groupedPermissions = groupPermissionsByResource();
</script>

<div class="space-y-6">
  <div class="flex justify-between items-center">
    <h1 class="text-2xl font-bold">إدارة الأدوار والصلاحيات</h1>
    <div class="flex gap-2">
      <button
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        on:click={() => { showHelp = !showHelp; }}
      >
        <span class="flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
          </svg>
          مساعدة
        </span>
      </button>
    </div>
  </div>

  {#if showHelp}
    <div class="bg-blue-50 border border-blue-200 p-4 rounded-md text-blue-800 dark:bg-blue-900/20 dark:border-blue-800/30 dark:text-blue-300">
      <h3 class="text-lg font-bold mb-2">كيفية استخدام صفحة إدارة الأدوار والصلاحيات</h3>
      <ul class="list-disc list-inside space-y-2">
        <li>يمكنك <strong>إضافة دور جديد</strong> بالنقر على زر "إضافة دور جديد".</li>
        <li>يمكنك <strong>تعديل دور</strong> أو <strong>حذفه</strong> باستخدام الأزرار الموجودة بجانب كل دور.</li>
        <li>لتعيين الصلاحيات لدور معين، اختر الدور من القائمة ثم حدد الصلاحيات المطلوبة.</li>
        <li>يمكنك <strong>تصفية الصلاحيات</strong> باستخدام حقل البحث.</li>
        <li>يمكنك <strong>عرض تفاصيل الصلاحية</strong> بالنقر على أيقونة المعلومات بجانب كل صلاحية.</li>
      </ul>
    </div>
  {/if}

  {#if error}
    <div class="bg-destructive/10 border border-red-200/30 p-4 rounded-md text-destructive text-sm">
      <p>{error}</p>
    </div>
  {/if}

  {#if loading}
    <div class="flex items-center justify-center h-64">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary dark:border-blue-400"></div>
    </div>
  {:else if roles.length === 0 || permissions.length === 0}
    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 p-4 rounded-md">
      <p>لم يتم العثور على بيانات الأدوار أو الصلاحيات. يرجى التحقق من إعداد قاعدة البيانات.</p>
    </div>
  {:else}
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- قائمة الأدوار -->
      <div class="bg-card rounded-lg border border-gray-200/50 shadow-sm dark:bg-gray-800 dark:border-gray-700/50 p-4">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold">الأدوار</h2>
          <button
            class="p-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            on:click={() => {
              newRoleName = '';
              newRoleDescription = '';
              showAddRoleModal = true;
            }}
          >
            <span class="flex items-center gap-1">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
              </svg>
              إضافة دور
            </span>
          </button>
        </div>
        <div class="space-y-2">
          {#each roles as role}
            <div class="border border-gray-200/50 dark:border-gray-700/50 rounded-md overflow-hidden">
              <button
                class="w-full text-right p-3 transition-colors {selectedRole?.id === role.id ? 'bg-primary/10 text-primary dark:bg-blue-900/30 dark:text-blue-400' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}"
                on:click={() => selectRole(role)}
              >
                <div class="font-medium">{getRoleNameArabic(role.name)}</div>
                {#if role.description}
                  <div class="text-sm text-muted-foreground dark:text-gray-400">{role.description}</div>
                {/if}
              </button>
              <div class="flex border-t border-gray-200/50 dark:border-gray-700/50">
                <button
                  class="flex-1 p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                  on:click={() => openEditRoleModal(role)}
                >
                  <span class="flex items-center justify-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                    تعديل
                  </span>
                </button>
                <button
                  class="flex-1 p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                  on:click={() => openDeleteRoleModal(role)}
                  disabled={role.name === 'admin'}
                >
                  <span class="flex items-center justify-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                    حذف
                  </span>
                </button>
              </div>
            </div>
          {/each}
        </div>
      </div>

      <!-- تفاصيل الدور والصلاحيات -->
      <div class="lg:col-span-3 bg-card rounded-lg border border-gray-200/50 shadow-sm dark:bg-gray-800 dark:border-gray-700/50 p-4">
        {#if selectedRole}
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">صلاحيات {getRoleNameArabic(selectedRole.name)}</h2>
            <div class="relative">
              <input
                type="text"
                placeholder="بحث في الصلاحيات..."
                class="px-3 py-2 border border-gray-300 rounded-md w-64 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                bind:value={permissionFilter}
              />
              {#if permissionFilter}
                <button
                  class="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  on:click={() => permissionFilter = ''}
                  aria-label="مسح البحث"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </button>
              {/if}
            </div>
          </div>

          {#if Object.keys(groupedPermissions).length === 0}
            <div class="text-center p-8 text-muted-foreground dark:text-gray-400">
              {#if permissionFilter}
                لا توجد نتائج مطابقة لـ "{permissionFilter}"
              {:else}
                لا توجد صلاحيات متاحة
              {/if}
            </div>
          {:else}
            <div class="space-y-6">
              {#each Object.entries(groupedPermissions) as [resource, resourcePermissions]}
                <div class="border border-gray-200/50 dark:border-gray-700/50 rounded-md p-4">
                  <h3 class="text-lg font-medium mb-3">
                    {getPermissionNameArabic(resource, '')}
                  </h3>

                  <div class="space-y-2">
                    {#each resourcePermissions as permission}
                      <div class="flex items-center justify-between p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md">
                        <label class="flex items-center space-x-2 space-x-reverse cursor-pointer">
                          <input
                            type="checkbox"
                            checked={hasPermission(permission.id)}
                            on:change={() => togglePermission(permission.id)}
                            class="rounded text-primary focus:ring-primary dark:bg-gray-700 dark:border-gray-600"
                          />
                          <span>{getPermissionNameArabic('', permission.action)}</span>
                          {#if permission.description}
                            <span class="text-sm text-muted-foreground dark:text-gray-400 mr-2">
                              ({permission.description})
                            </span>
                          {/if}
                        </label>
                        <button
                          class="p-1 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full transition-colors"
                          on:click={() => viewPermissionDetails(permission)}
                          aria-label="عرض تفاصيل الصلاحية"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    {/each}
                  </div>
                </div>
              {/each}
            </div>
          {/if}
        {:else}
          <div class="text-center p-8 text-muted-foreground dark:text-gray-400">
            الرجاء اختيار دور من القائمة
          </div>
        {/if}
      </div>
    </div>
  {/if}

  <!-- نافذة إضافة دور جديد -->
  {#if showAddRoleModal}
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4">إضافة دور جديد</h2>
        <form on:submit|preventDefault={addRole} class="space-y-4">
          <div>
            <label for="roleName" class="block text-sm font-medium mb-1">اسم الدور</label>
            <input
              type="text"
              id="roleName"
              bind:value={newRoleName}
              class="w-full px-3 py-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="أدخل اسم الدور"
              required
            />
          </div>
          <div>
            <label for="roleDescription" class="block text-sm font-medium mb-1">وصف الدور</label>
            <textarea
              id="roleDescription"
              bind:value={newRoleDescription}
              class="w-full px-3 py-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="أدخل وصف الدور (اختياري)"
              rows="3"
            ></textarea>
          </div>
          <div class="flex justify-end gap-2 mt-6">
            <button
              type="button"
              class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700 transition-colors"
              on:click={() => showAddRoleModal = false}
            >
              إلغاء
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              disabled={!newRoleName.trim()}
            >
              إضافة
            </button>
          </div>
        </form>
      </div>
    </div>
  {/if}

  <!-- نافذة تعديل دور -->
  {#if showEditRoleModal && editingRole}
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4">تعديل دور</h2>
        <form on:submit|preventDefault={updateRole} class="space-y-4">
          <div>
            <label for="editRoleName" class="block text-sm font-medium mb-1">اسم الدور</label>
            <input
              type="text"
              id="editRoleName"
              bind:value={editingRole.name}
              class="w-full px-3 py-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="أدخل اسم الدور"
              required
            />
          </div>
          <div>
            <label for="editRoleDescription" class="block text-sm font-medium mb-1">وصف الدور</label>
            <textarea
              id="editRoleDescription"
              bind:value={editingRole.description}
              class="w-full px-3 py-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="أدخل وصف الدور (اختياري)"
              rows="3"
            ></textarea>
          </div>
          <div class="flex justify-end gap-2 mt-6">
            <button
              type="button"
              class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700 transition-colors"
              on:click={() => showEditRoleModal = false}
            >
              إلغاء
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              disabled={!editingRole.name.trim()}
            >
              حفظ
            </button>
          </div>
        </form>
      </div>
    </div>
  {/if}

  <!-- نافذة حذف دور -->
  {#if showDeleteRoleModal && deletingRole}
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4">حذف دور</h2>
        <p class="mb-6">هل أنت متأكد من حذف دور "{getRoleNameArabic(deletingRole.name)}"؟</p>
        <div class="flex justify-end gap-2">
          <button
            type="button"
            class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700 transition-colors"
            on:click={() => showDeleteRoleModal = false}
          >
            إلغاء
          </button>
          <button
            type="button"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            on:click={deleteRole}
          >
            حذف
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- نافذة تفاصيل الصلاحية -->
  {#if showPermissionDetails && selectedPermission}
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4">تفاصيل الصلاحية</h2>
        <div class="space-y-4">
          <div>
            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">الاسم</h3>
            <p>{selectedPermission.name}</p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">المورد</h3>
            <p>{getPermissionNameArabic(selectedPermission.resource, '')}</p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">الإجراء</h3>
            <p>{getPermissionNameArabic('', selectedPermission.action)}</p>
          </div>
          {#if selectedPermission.description}
            <div>
              <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">الوصف</h3>
              <p>{selectedPermission.description}</p>
            </div>
          {/if}
        </div>
        <div class="flex justify-end mt-6">
          <button
            type="button"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            on:click={() => showPermissionDetails = false}
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>
