# ربط المستندات بوحدة المستخدم

تم تنفيذ تعديلات على النظام لربط المستندات بوحدة المستخدم بشكل تلقائي، بحيث:

1. عند إضافة مستند جديد، يتم ربطه تلقائياً بوحدة المستخدم الذي أضافه
2. يستطيع المستخدم رؤية المستندات الخاصة بوحدته فقط
3. يستطيع المدير رؤية المستندات الخاصة بوحدته والوحدات التابعة لها
4. يستطيع المشرف رؤية جميع المستندات في النظام

## التغييرات التي تم تنفيذها:

### 1. تعديل وظيفة إضافة المستند

- تم تعديل وظيفة `handleAddDocument` في صفحة المستندات لاستخدام وحدة المستخدم تلقائياً
- تم تعديل وظيفة `addDocument` في خدمة المستندات لتجاهل أي وحدة مرسلة واستخدام وحدة المستخدم دائماً

### 2. تعديل وظيفة عرض المستندات

- تم تعديل وظيفة `fetchDocuments` في صفحة المستندات لعرض المستندات حسب دور المستخدم:
  - المشرف: يرى جميع المستندات
  - المدير: يرى مستندات وحدته والوحدات التابعة لها
  - المستخدم العادي: يرى مستندات وحدته فقط

### 3. تعديل وظيفة التحقق من صلاحية الوصول

- تم تعديل وظيفة `canAccessDocument` في خدمة المستندات لتحديد صلاحية الوصول حسب دور المستخدم:
  - المشرف: يمكنه الوصول إلى جميع المستندات
  - المدير: يمكنه الوصول إلى مستندات وحدته والوحدات التابعة لها
  - المستخدم العادي: يمكنه الوصول إلى مستندات وحدته فقط ومستندات أنشأها بنفسه

### 4. تعديل واجهة المستخدم

- تم إزالة فلتر الوحدات التنظيمية من صفحة المستندات
- تم إضافة معلومات عن دور المستخدم ووحدته في صفحة المستندات

## كيفية استخدام النظام:

### للمستخدمين العاديين:

- عند إضافة مستند جديد، سيتم ربطه تلقائياً بوحدتك التنظيمية
- ستتمكن من رؤية المستندات الخاصة بوحدتك فقط
- ستتمكن من تعديل وحذف المستندات التي قمت بإنشائها فقط

### للمديرين:

- عند إضافة مستند جديد، سيتم ربطه تلقائياً بوحدتك التنظيمية
- ستتمكن من رؤية المستندات الخاصة بوحدتك والوحدات التابعة لها
- ستتمكن من تعديل وحذف المستندات التي قمت بإنشائها فقط

### للمشرفين:

- عند إضافة مستند جديد، سيتم ربطه تلقائياً بوحدتك التنظيمية (إن وجدت)
- ستتمكن من رؤية جميع المستندات في النظام
- ستتمكن من تعديل وحذف أي مستند في النظام

## ملاحظات هامة:

1. يجب تعيين الوحدة التنظيمية لكل مستخدم في النظام
2. يجب تعيين الدور المناسب لكل مستخدم (مستخدم عادي، مدير، مشرف)
3. يجب إنشاء الهيكل التنظيمي بشكل صحيح (علاقات التبعية بين الوحدات)

## مثال:

إذا كان لدينا الهيكل التنظيمي التالي:

```
الإدارة العامة
  ├── إدارة الشؤون الإدارية
  │     ├── قسم شؤون الموظفين
  │     └── قسم الخدمات
  └── إدارة الشؤون المالية
        ├── قسم المحاسبة
        └── قسم المشتريات
```

وكان لدينا المستخدمون التاليون:
- أحمد: مشرف
- محمد: مدير إدارة الشؤون الإدارية
- علي: موظف في قسم شؤون الموظفين
- خالد: مدير قسم المحاسبة

فإن:
- أحمد (المشرف) يمكنه رؤية جميع المستندات في النظام
- محمد (مدير إدارة الشؤون الإدارية) يمكنه رؤية مستندات إدارة الشؤون الإدارية وقسم شؤون الموظفين وقسم الخدمات
- علي (موظف في قسم شؤون الموظفين) يمكنه رؤية مستندات قسم شؤون الموظفين فقط
- خالد (مدير قسم المحاسبة) يمكنه رؤية مستندات قسم المحاسبة فقط
