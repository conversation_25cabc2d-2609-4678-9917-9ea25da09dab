-- إصلاح سياسات الأمان لجدول المراسلات

-- 0. حذف الوظائف الموجودة التي قد تسبب تعارضاً
DROP FUNCTION IF EXISTS get_message_with_relations(uuid);
DROP FUNCTION IF EXISTS get_inbox_messages(uuid);
DROP FUNCTION IF EXISTS get_sent_messages(uuid);

-- 1. تعطيل سياسات الأمان الحالية
ALTER TABLE messages DISABLE ROW LEVEL SECURITY;

-- 2. حذف السياسات الحالية
DROP POLICY IF EXISTS messages_select_policy ON messages;
DROP POLICY IF EXISTS messages_insert_policy ON messages;
DROP POLICY IF EXISTS messages_update_policy ON messages;
DROP POLICY IF EXISTS messages_delete_policy ON messages;

-- 3. إنشاء سياسات جديدة

-- سياسة القراءة: يمكن للمستخدم قراءة الرسائل التي أرسلها أو استقبلها
CREATE POLICY messages_select_policy ON messages
FOR SELECT USING (
  auth.uid() = sender_id OR
  auth.uid() = recipient_id OR
  auth.uid() = receiver_id OR
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.unit_id = messages.receiver_unit_id
  ) OR
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role IN ('مشرف', 'مدير')
  )
);

-- سياسة الإنشاء: يمكن للمستخدم إنشاء رسائل جديدة
CREATE POLICY messages_insert_policy ON messages
FOR INSERT WITH CHECK (
  auth.uid() = sender_id
);

-- سياسة التحديث: يمكن للمستخدم تحديث الرسائل التي استقبلها (لتحديث حالة القراءة)
CREATE POLICY messages_update_policy ON messages
FOR UPDATE USING (
  auth.uid() = recipient_id OR
  auth.uid() = receiver_id OR
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.unit_id = messages.receiver_unit_id
  ) OR
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role IN ('مشرف', 'مدير')
  )
);

-- سياسة الحذف: يمكن للمستخدم حذف الرسائل التي أرسلها
CREATE POLICY messages_delete_policy ON messages
FOR DELETE USING (
  auth.uid() = sender_id OR
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role IN ('مشرف', 'مدير')
  )
);

-- 4. إعادة تفعيل سياسات الأمان
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- 5. تحديث ذاكرة التخزين المؤقت للمخطط
DROP FUNCTION IF EXISTS refresh_schema_cache();

CREATE OR REPLACE FUNCTION refresh_schema_cache()
RETURNS VOID AS $$
BEGIN
  NOTIFY pgrst, 'reload schema';
END;
$$ LANGUAGE plpgsql;

SELECT refresh_schema_cache();

-- 6. إنشاء وظيفة بسيطة لجلب تفاصيل الرسالة
CREATE OR REPLACE FUNCTION get_message_details(message_id UUID)
RETURNS TABLE (
  id UUID,
  subject TEXT,
  content TEXT,
  sender_id UUID,
  recipient_id UUID,
  receiver_id UUID,
  receiver_unit_id UUID,
  is_read BOOLEAN,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    m.id,
    m.subject,
    m.content,
    m.sender_id,
    m.recipient_id,
    m.receiver_id,
    m.receiver_unit_id,
    m.is_read,
    m.created_at,
    m.updated_at
  FROM
    messages m
  WHERE
    m.id = message_id;
END;
$$ LANGUAGE plpgsql;
