<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';

  type Unit = {
    id: string;
    name: string;
    type: string;
    parent_id: string | null;
    children?: Unit[];
  };

  let units: Unit[] = [];
  let unitTree: Unit[] = [];
  let isAdmin = false;
  let dialogOpen = false;
  let loading = false;
  let updateMessage = '';

  let newUnit = {
    name: '',
    type: '',
    parent_id: null as string | null
  };

  const unitTypes = [
    { value: 'وزارة', label: 'وزارة' },
    { value: 'إدارة', label: 'إدارة' },
    { value: 'فرع', label: 'فرع' },
    { value: 'قسم', label: 'قسم' },
    { value: 'مكتب', label: 'مكتب' }
  ];

  onMount(async () => {
    try {
      loading = true;

      // التحقق مما إذا كان المستخدم مشرفاً
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (profile) {
          isAdmin = profile.role === 'مشرف';
        }
      }

      // تحديث الهيكل التنظيمي تلقائياً
      if (isAdmin) {
        try {
          // تنفيذ استعلام SQL لتحديث الهيكل التنظيمي
          const { error } = await supabase.rpc('update_organization_hierarchy');

          if (error) {
            console.error('Error updating organization hierarchy:', error);
            updateMessage = `حدث خطأ أثناء تحديث الهيكل التنظيمي: ${error.message}`;
          } else {
            updateMessage = 'تم تحديث الهيكل التنظيمي تلقائياً';

            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
              updateMessage = '';
            }, 3000);
          }
        } catch (error) {
          console.error('Error in auto-update organization hierarchy:', error);
        }
      }

      // جلب جميع الوحدات
      const { data } = await supabase
        .from('units')
        .select('*')
        .order('name');

      if (data) {
        units = data;
        buildUnitTree();
      }
    } catch (error) {
      console.error('Error in onMount:', error);
    } finally {
      loading = false;
    }
  });

  function buildUnitTree() {
    // تحديد أنواع الوحدات وترتيبها حسب التسلسل الهرمي
    const unitTypeOrder = {
      'وزارة': 1,
      'إدارة': 2,
      'فرع': 3,
      'قسم': 4,
      'مكتب': 5
    };

    // ترتيب الوحدات حسب نوعها
    const sortedUnits = [...units].sort((a, b) => {
      const orderA = unitTypeOrder[a.type as keyof typeof unitTypeOrder] || 99;
      const orderB = unitTypeOrder[b.type as keyof typeof unitTypeOrder] || 99;
      return orderA - orderB;
    });

    // البحث عن الوحدات الجذرية (بدون وحدة أم)
    const roots = sortedUnits.filter(unit => !unit.parent_id);

    // بناء الشجرة بشكل متدرج
    unitTree = roots.map(root => {
      return {
        ...root,
        children: getChildren(root.id)
      };
    });
  }

  function getChildren(parentId: string): Unit[] {
    // البحث عن الوحدات التابعة للوحدة المحددة
    const children = units.filter(unit => unit.parent_id === parentId);

    // ترتيب الوحدات التابعة حسب النوع ثم الاسم
    const sortedChildren = [...children].sort((a, b) => {
      if (a.type !== b.type) {
        // ترتيب حسب النوع: إدارة، فرع، قسم، مكتب
        const typeOrder = { 'إدارة': 1, 'فرع': 2, 'قسم': 3, 'مكتب': 4 };
        return (typeOrder[a.type as keyof typeof typeOrder] || 99) - (typeOrder[b.type as keyof typeof typeOrder] || 99);
      }
      // ترتيب حسب الاسم إذا كان النوع متساوياً
      return a.name.localeCompare(b.name);
    });

    // بناء الشجرة بشكل متدرج
    return sortedChildren.map(child => {
      return {
        ...child,
        children: getChildren(child.id)
      };
    });
  }

  async function handleAddUnit() {
    if (!newUnit.name || !newUnit.type) {
      alert('يرجى إدخال اسم ونوع الوحدة');
      return;
    }

    try {
      const { data, error } = await supabase
        .from('units')
        .insert([
          {
            name: newUnit.name,
            type: newUnit.type,
            parent_id: newUnit.parent_id
          }
        ])
        .select();

      if (error) {
        alert(`حدث خطأ: ${error.message}`);
        return;
      }

      if (data) {
        units = [...units, data[0]];
        buildUnitTree();

        // Reset form
        newUnit = {
          name: '',
          type: '',
          parent_id: null
        };

        dialogOpen = false;
      }
    } catch (error) {
      console.error('Error adding unit:', error);
      alert('حدث خطأ أثناء إضافة الوحدة');
    }
  }

  async function updateOrganizationHierarchy() {
    try {
      loading = true;
      updateMessage = 'جاري تحديث الهيكل التنظيمي...';

      // تنفيذ استعلام SQL لتحديث الهيكل التنظيمي
      const { error } = await supabase.rpc('update_organization_hierarchy');

      if (error) {
        console.error('Error updating organization hierarchy:', error);
        updateMessage = `حدث خطأ أثناء تحديث الهيكل التنظيمي: ${error.message}`;
        return;
      }

      // إعادة تحميل الوحدات التنظيمية
      const { data } = await supabase
        .from('units')
        .select('*')
        .order('name');

      if (data) {
        units = data;
        buildUnitTree();
        updateMessage = 'تم تحديث الهيكل التنظيمي بنجاح';
      }
    } catch (error) {
      console.error('Error in updateOrganizationHierarchy:', error);
      updateMessage = 'حدث خطأ أثناء تحديث الهيكل التنظيمي';
    } finally {
      loading = false;

      // إخفاء الرسالة بعد 3 ثوان
      setTimeout(() => {
        updateMessage = '';
      }, 3000);
    }
  }

  // وظيفة مساعدة للعثور على اسم الوحدة الأم
  function findParentUnitName(parentId: string | null): string {
    if (!parentId) return '';

    // البحث في جميع الوحدات (وليس فقط في الوحدات المعروضة حالياً)
    const parentUnit = units.find(u => u.id === parentId);
    return parentUnit ? parentUnit.name : 'غير معروف';
  }

  // وظيفة لتحديد لون الوحدة حسب نوعها
  function getUnitTypeColor(type: string): string {
    switch (type) {
      case 'وزارة':
        return 'bg-blue-100 border-blue-300';
      case 'إدارة':
        return 'bg-green-100 border-green-300';
      case 'فرع':
        return 'bg-purple-100 border-purple-300';
      case 'قسم':
        return 'bg-orange-100 border-orange-300';
      case 'مكتب':
        return 'bg-red-100 border-red-300';
      default:
        return 'bg-gray-100 border-gray-300';
    }
  }

  // وظيفة لتحديد لون النص حسب نوع الوحدة
  function getUnitTypeTextColor(type: string): string {
    switch (type) {
      case 'وزارة':
        return 'text-blue-700';
      case 'إدارة':
        return 'text-green-700';
      case 'فرع':
        return 'text-purple-700';
      case 'قسم':
        return 'text-orange-700';
      case 'مكتب':
        return 'text-red-700';
      default:
        return 'text-gray-700';
    }
  }

  function renderUnitTree(units: Unit[], level = 0): string {
    if (!units || units.length === 0) return '';

    return `
      <ul class="mr-${level * 4} mt-2">
        ${units.map(unit => {
          // الحصول على اسم الوحدة الأم
          const parentName = findParentUnitName(unit.parent_id);

          // تحديد ألوان الوحدة
          const bgColor = getUnitTypeColor(unit.type);
          const textColor = getUnitTypeTextColor(unit.type);

          return `
            <li class="mb-2">
              <div class="flex items-center p-2 rounded border ${bgColor}">
                <span class="font-medium ${textColor}">${unit.name}</span>
                <span class="mr-2 text-sm ${textColor}">(${unit.type})</span>
                ${unit.parent_id ? `
                  <span class="mr-2 text-xs text-gray-600">
                    تابع لـ: ${parentName}
                  </span>
                ` : ''}
              </div>
              ${unit.children && unit.children.length > 0 ? renderUnitTree(unit.children, level + 1) : ''}
            </li>
          `;
        }).join('')}
      </ul>
    `;
  }
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">الهيكل التنظيمي</h1>

    {#if isAdmin}
      <div class="flex gap-2">
        <button
          class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
          on:click={() => dialogOpen = true}
        >
          إضافة وحدة جديدة
        </button>

        <button
          class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
          on:click={updateOrganizationHierarchy}
          disabled={loading}
        >
          {loading ? 'جاري التحديث...' : 'تحديث الهيكل التنظيمي'}
        </button>
      </div>
    {/if}
  </div>

  {#if updateMessage}
    <div class="mb-4 p-3 rounded-md {updateMessage.includes('خطأ') ? 'bg-destructive/10 text-destructive' : 'bg-green-100 text-green-800'}">
      {updateMessage}
    </div>
  {/if}

  <div class="bg-card rounded-lg shadow p-6">
    <div class="mb-4">
      <h2 class="text-xl font-bold">الهيكل التنظيمي</h2>
      <p class="text-muted-foreground">عرض الهيكل التنظيمي للوزارة</p>
    </div>

    {#if loading}
      <div class="flex justify-center items-center p-8">
        <div class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
          <span class="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">جاري التحميل...</span>
        </div>
        <span class="mr-3">جاري تحميل الهيكل التنظيمي...</span>
      </div>
    {:else if unitTree.length === 0}
      <p class="text-muted-foreground">لا توجد وحدات تنظيمية بعد</p>
    {:else}
      <div class="organizational-tree">
        {@html renderUnitTree(unitTree)}
      </div>

      <div class="mt-6 border-t pt-4">
        <h3 class="text-lg font-semibold mb-2">دليل الألوان:</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
          <div class="flex items-center p-2 rounded border bg-green-100 border-green-300">
            <span class="text-green-700">إدارة</span>
          </div>
          <div class="flex items-center p-2 rounded border bg-purple-100 border-purple-300">
            <span class="text-purple-700">فرع</span>
          </div>
          <div class="flex items-center p-2 rounded border bg-red-100 border-red-300">
            <span class="text-red-700">مكتب</span>
          </div>
        </div>
      </div>
    {/if}
  </div>

  <!-- Add Unit Dialog -->
  {#if dialogOpen}
    <div class="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="bg-card rounded-lg shadow-lg w-full max-w-md">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">إضافة وحدة تنظيمية جديدة</h2>
            <button
              class="text-muted-foreground hover:text-foreground"
              on:click={() => dialogOpen = false}
            >
              ✕
            </button>
          </div>

          <div class="grid gap-4 py-4">
            <div class="grid gap-2">
              <label for="name" class="block text-sm font-medium">اسم الوحدة</label>
              <input
                id="name"
                bind:value={newUnit.name}
                placeholder="أدخل اسم الوحدة"
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>

            <div class="grid gap-2">
              <label for="type" class="block text-sm font-medium">نوع الوحدة</label>
              <select
                id="type"
                bind:value={newUnit.type}
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="" disabled selected>اختر نوع الوحدة</option>
                {#each unitTypes as type}
                  <option value={type.value}>{type.label}</option>
                {/each}
              </select>
            </div>

            <div class="grid gap-2">
              <label for="parent" class="block text-sm font-medium">الوحدة الأم</label>
              <select
                id="parent"
                bind:value={newUnit.parent_id}
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value={null}>بدون وحدة أم</option>
                {#each units as unit}
                  <option value={unit.id}>{unit.name} ({unit.type})</option>
                {/each}
              </select>
              <p class="text-sm text-muted-foreground">
                اختياري: اختر الوحدة الأم لهذه الوحدة
              </p>
            </div>
          </div>

          <div class="flex justify-end gap-2 mt-4">
            <button
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
              on:click={() => dialogOpen = false}
            >
              إلغاء
            </button>
            <button
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
              on:click={handleAddUnit}
            >
              إضافة
            </button>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .organizational-tree :global(ul) {
    list-style: none;
    position: relative;
  }

  .organizational-tree :global(li) {
    position: relative;
  }

  .organizational-tree :global(li::before) {
    content: "";
    position: absolute;
    top: 0;
    right: -20px;
    border-right: 1px solid #ccc;
    height: 100%;
  }

  .organizational-tree :global(li:last-child::before) {
    height: 20px;
  }

  .organizational-tree :global(li::after) {
    content: "";
    position: absolute;
    top: 20px;
    right: -20px;
    width: 20px;
    border-top: 1px solid #ccc;
  }
</style>
