<script>
  import { onMount } from 'svelte';
  import { supabase } from '$lib/supabaseClient';
  
  export let userId = null;
  export let currentRole = null;
  export let onRoleChange = () => {};
  
  let roles = [];
  let selectedRoleId = null;
  let loading = true;
  let error = null;
  
  onMount(async () => {
    try {
      // جلب قائمة الأدوار من قاعدة البيانات
      const { data: rolesData, error: rolesError } = await supabase
        .from('roles')
        .select('id, name, description')
        .order('name');
      
      if (rolesError) throw rolesError;
      roles = rolesData;
      
      // إذا كان هناك مستخدم محدد، جلب دوره الحالي
      if (userId) {
        const { data: userData, error: userError } = await supabase
          .from('profiles')
          .select('role_id')
          .eq('id', userId)
          .single();
        
        if (userError) throw userError;
        
        if (userData && userData.role_id) {
          selectedRoleId = userData.role_id;
        } else if (currentRole) {
          // البحث عن الدور بالاسم إذا كان متوفرًا
          const role = roles.find(r => r.name === currentRole);
          if (role) {
            selectedRoleId = role.id;
          }
        }
      }
    } catch (e) {
      console.error('Error loading roles:', e);
      error = e.message;
    } finally {
      loading = false;
    }
  });
  
  async function handleRoleChange() {
    if (!userId || !selectedRoleId) return;
    
    try {
      // تحديث دور المستخدم في قاعدة البيانات
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ role_id: selectedRoleId })
        .eq('id', userId);
      
      if (updateError) throw updateError;
      
      // العثور على اسم الدور المحدد
      const selectedRole = roles.find(r => r.id === selectedRoleId);
      
      // استدعاء دالة رد الاتصال مع اسم الدور الجديد
      if (selectedRole) {
        onRoleChange(selectedRole.name);
      }
    } catch (e) {
      console.error('Error updating role:', e);
      error = e.message;
    }
  }
</script>

{#if loading}
  <div class="loading">جاري التحميل...</div>
{:else if error}
  <div class="error">{error}</div>
{:else}
  <div class="role-selector">
    <select
      bind:value={selectedRoleId}
      on:change={handleRoleChange}
      class="select select-bordered w-full"
      dir="rtl"
    >
      <option value="" disabled selected={!selectedRoleId}>اختر الدور</option>
      {#each roles as role}
        <option value={role.id} selected={selectedRoleId === role.id}>
          {role.name} - {role.description}
        </option>
      {/each}
    </select>
  </div>
{/if}

<style>
  .role-selector {
    width: 100%;
  }
  
  .loading {
    color: #666;
    font-style: italic;
  }
  
  .error {
    color: #e53e3e;
    font-size: 0.875rem;
    margin-top: 0.5rem;
  }
  
  select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 0.25rem;
    background-color: white;
  }
  
  select:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
  }
</style>
