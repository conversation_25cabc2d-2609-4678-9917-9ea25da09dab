<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';
  import { createSignature } from '$lib/utils/signatureUtils';
  import { checkSignaturePermission } from '$lib/utils/signaturePermissions';
  import SignaturePasswordDialog from '$lib/components/SignaturePasswordDialog.svelte';

  type Message = {
    id: string;
    subject: string;
    content: string;
    sender_id: string;
    receiver_id: string | null;
    recipient_id: string | null;
    receiver_unit_id: string | null;
    status: string;
    is_read: boolean;
    parent_id: string | null;
    created_at: string;
    updated_at: string;
    is_signed: boolean;
    signature: any;
    sender?: {
      full_name: string;
    };
    receiver?: {
      full_name: string;
    };
    receiver_unit?: {
      name: string;
    };
  };

  type User = {
    id: string;
    full_name: string;
    email: string;
    role: string;
    unit_id: string | null;
  };

  type Unit = {
    id: string;
    name: string;
    type: string;
  };

  let inboxMessages: Message[] = [];
  let sentMessages: Message[] = [];
  let users: User[] = [];
  let units: Unit[] = [];
  let currentUser: User | null = null;
  let dialogOpen = false;
  let passwordDialogOpen = false;
  let currentUserAuth: any = null;
  let userRole = '';
  let canSign = false;
  let signaturePassword = '';

  let newMessage: {
    subject: string;
    content: string;
    receiver_id: string | null;
    receiver_unit_id: string | null;
    is_signed: boolean;
    signature: any;
  } = {
    subject: '',
    content: '',
    receiver_id: null,
    receiver_unit_id: null,
    is_signed: false,
    signature: null
  };

  onMount(async () => {
    // جلب بيانات المستخدم الحالي
    const { data: { user } } = await supabase.auth.getUser();
    currentUserAuth = user;

    if (user) {
      const { data: profile } = await supabase
        .from('profiles')
        .select('*, roles:role_id(name)')
        .eq('id', user.id)
        .single();

      if (profile) {
        currentUser = profile;

        // تحديد دور المستخدم
        if (profile.role) {
          userRole = profile.role;
        } else if (profile.roles && profile.roles.name) {
          userRole = profile.roles.name;
        }

        // التحقق من صلاحية التوقيع
        canSign = await checkSignaturePermission(user.id);
      }

      // جلب الرسائل الواردة
      const { data: inbox } = await supabase
        .from('messages')
        .select(`
          *,
          sender:sender_id(full_name),
          receiver_unit:receiver_unit_id(name)
        `)
        .or(`receiver_id.eq.${user.id},receiver_unit_id.eq.${profile?.unit_id || 'null'}`)
        .order('created_at', { ascending: false });

      if (inbox) {
        inboxMessages = inbox;
      }

      // جلب الرسائل المرسلة
      const { data: sent } = await supabase
        .from('messages')
        .select(`
          *,
          receiver:receiver_id(full_name),
          receiver_unit:receiver_unit_id(name)
        `)
        .eq('sender_id', user.id)
        .order('created_at', { ascending: false });

      if (sent) {
        sentMessages = sent;
      }

      // جلب المستخدمين
      const { data: usersData } = await supabase
        .from('profiles')
        .select('id, full_name, email, role, unit_id')
        .order('full_name');

      if (usersData) {
        users = usersData;
      }

      // جلب الوحدات التنظيمية
      const { data: unitsData } = await supabase
        .from('units')
        .select('id, name, type')
        .order('name');

      if (unitsData) {
        units = unitsData;
      }
    }
  });

  async function handleSendMessage() {
    if (!newMessage.subject || !newMessage.content || (!newMessage.receiver_id && !newMessage.receiver_unit_id)) {
      alert('يرجى إدخال الموضوع والمحتوى واختيار المستقبل');
      return;
    }

    if (!currentUser) {
      alert('يجب تسجيل الدخول لإرسال رسالة');
      return;
    }

    // التحقق من الرسالة الموقعة
    if (newMessage.is_signed) {
      // التحقق من صلاحية التوقيع
      if (!canSign) {
        alert('ليس لديك صلاحية لإنشاء توقيع إلكتروني');
        return;
      }

      // التحقق من وجود توقيع
      if (!newMessage.signature) {
        // فتح نافذة إدخال كلمة المرور للتوقيع
        passwordDialogOpen = true;
        return; // إيقاف الإرسال حتى يتم إدخال كلمة المرور
      }
    }

    try {
      // التحقق من أن المستقبل محدد بشكل صحيح
      if (newMessage.receiver_id === null && newMessage.receiver_unit_id === null) {
        alert('يرجى اختيار مستقبل للرسالة (مستخدم أو وحدة تنظيمية)');
        return;
      }

      // إعداد بيانات الرسالة
      let messageData: any;

      if (newMessage.is_signed) {
        // إذا كانت رسالة موقعة، استخدم الجدول الجديد مع أعمدة التوقيع
        messageData = {
          subject: newMessage.subject,
          content: newMessage.content,
          sender_id: currentUser.id,
          is_read: false,
          is_signed: true
        };

        // إضافة بيانات التوقيع
        if (newMessage.signature) {
          messageData.signature = newMessage.signature;
        }
      } else {
        // إذا كانت رسالة عادية، استخدم الجدول السابق بدون أعمدة التوقيع
        messageData = {
          subject: newMessage.subject,
          content: newMessage.content,
          sender_id: currentUser.id,
          is_read: false
        };
      }

      // إضافة المستقبل (مستخدم أو وحدة تنظيمية)
      if (newMessage.receiver_id) {
        messageData.recipient_id = newMessage.receiver_id; // استخدام recipient_id
        messageData.receiver_id = newMessage.receiver_id; // استخدام receiver_id أيضاً للتوافق
        messageData.receiver_unit_id = null;
      } else if (newMessage.receiver_unit_id) {
        messageData.receiver_unit_id = newMessage.receiver_unit_id;

        // استخدام معرف المستخدم الحالي كمستقبل افتراضي عند إرسال إلى وحدة تنظيمية
        // هذا لتجنب مشكلة قيد NOT NULL على عمود recipient_id
        const firstUserInUnit = users.find(u => u.unit_id === newMessage.receiver_unit_id);
        messageData.recipient_id = firstUserInUnit?.id || currentUser.id;
        messageData.receiver_id = null;
      }

      // تم إزالة قسم إضافة المستند لأنه غير موجود في قاعدة البيانات

      // إرسال الرسالة
      const { data: insertedData, error: insertError } = await supabase
        .from('messages')
        .insert([messageData])
        .select();

      if (insertError) {
        console.error('Supabase insert error:', insertError);
        throw new Error(`فشل إرسال الرسالة: ${insertError.message}`);
      }

      // جلب الرسالة المرسلة مع البيانات المرتبطة
      let data = null;
      if (insertedData && insertedData.length > 0) {
        const messageId = insertedData[0].id;
        const { data: messageData, error: fetchError } = await supabase
          .from('messages')
          .select(`
            *,
            sender:sender_id(full_name),
            receiver_unit:receiver_unit_id(name)
          `)
          .eq('id', messageId)
          .single();

        if (fetchError) {
          console.error('Supabase fetch error:', fetchError);
        } else {
          data = [messageData];

          // إذا كان هناك مستقبل محدد، جلب بياناته
          if (messageData.receiver_id) {
            const { data: receiverData, error: receiverError } = await supabase
              .from('profiles')
              .select('full_name')
              .eq('id', messageData.receiver_id)
              .single();

            if (!receiverError && receiverData) {
              data[0].receiver = receiverData;
            }
          }
        }
      }

      // تم التعامل مع insertError سابقاً

      if (data && data.length > 0) {
        // إضافة الرسالة الجديدة إلى قائمة الرسائل المرسلة
        sentMessages = [data[0], ...sentMessages];

        // إعادة تعيين النموذج
        newMessage = {
          subject: '',
          content: '',
          receiver_id: null,
          receiver_unit_id: null,
          is_signed: false,
          signature: null
        };

        // إغلاق النافذة
        dialogOpen = false;
      } else {
        throw new Error('لم يتم إرجاع بيانات الرسالة بعد الإرسال');
      }
    } catch (error: any) {
      console.error('Error sending message:', error);
      alert(`حدث خطأ أثناء إرسال الرسالة: ${error.message || 'خطأ غير معروف'}`);
    }
  }

  async function markAsRead(message: Message) {
    if (message.status === 'read') return;

    try {
      const { error } = await supabase
        .from('messages')
        .update({ status: 'read', updated_at: new Date().toISOString() })
        .eq('id', message.id);

      if (error) {
        throw error;
      }

      // تحديث حالة الرسالة في القائمة
      inboxMessages = inboxMessages.map(msg =>
        msg.id === message.id ? { ...msg, status: 'read' } : msg
      );
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  }

  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  }

  // فتح نافذة إنشاء رسالة موقعة
  async function openSignedMessageDialog() {
    // التحقق من صلاحية المستخدم لإنشاء توقيع
    if (!currentUserAuth) {
      alert('يجب تسجيل الدخول لإنشاء رسالة موقعة');
      return;
    }

    // التحقق من صلاحية التوقيع
    if (!canSign) {
      alert('ليس لديك صلاحية لإنشاء توقيع إلكتروني');
      return;
    }

    // التأكد من إعادة تعيين حالة الرسالة
    newMessage = {
      subject: '',
      content: '',
      receiver_id: null,
      receiver_unit_id: null,
      is_signed: true, // تعيين الرسالة كموقعة مسبقاً
      signature: null
    };

    // فتح نافذة إنشاء الرسالة
    dialogOpen = true;
  }

  // إنشاء توقيع للرسالة الحالية
  function handleCreateSignature() {
    if (!newMessage.subject || !newMessage.content) {
      alert('يرجى إدخال الموضوع والمحتوى قبل التوقيع');
      return;
    }

    if (!currentUserAuth) {
      alert('يجب تسجيل الدخول لإنشاء توقيع');
      return;
    }

    // التحقق من صلاحية التوقيع
    if (!canSign) {
      alert('ليس لديك صلاحية لإنشاء توقيع إلكتروني');
      return;
    }

    // فتح نافذة إدخال كلمة المرور
    passwordDialogOpen = true;
  }

  // معالجة إدخال كلمة المرور للتوقيع
  async function handlePasswordSubmit(event: { detail: { password: string } }) {
    const password = event.detail.password;
    signaturePassword = password;

    // إنشاء التوقيع باستخدام بيانات المستخدم والرسالة وكلمة المرور
    const signature = createSignature(
      newMessage.content,
      newMessage.subject,
      currentUserAuth,
      newMessage.receiver_id === null ? undefined : newMessage.receiver_id,
      newMessage.receiver_unit_id === null ? undefined : newMessage.receiver_unit_id,
      password
    );

    // تحديث حالة الرسالة
    newMessage.is_signed = true;
    newMessage.signature = signature;

    // إظهار رسالة تأكيد
    alert('تم توقيع الرسالة بنجاح');

    // إذا كان الطلب من نافذة إرسال الرسالة، قم بإرسال الرسالة تلقائياً
    if (dialogOpen && newMessage.subject && newMessage.content &&
        (newMessage.receiver_id || newMessage.receiver_unit_id)) {
      // استدعاء وظيفة إرسال الرسالة
      await handleSendMessage();
    }
  }

  // إلغاء التوقيع
  function handleCancelSignature() {
    newMessage.is_signed = false;
    newMessage.signature = null;
  }

  function getStatusText(status: string) {
    switch (status) {
      case 'sent': return 'تم الإرسال';
      case 'delivered': return 'تم التسليم';
      case 'read': return 'تمت القراءة';
      case 'replied': return 'تم الرد';
      default: return status;
    }
  }

  function getStatusColor(status: string) {
    switch (status) {
      case 'sent': return 'bg-primary text-primary-foreground';
      case 'delivered': return 'bg-secondary text-secondary-foreground';
      case 'read': return 'bg-green-500 text-white';
      case 'replied': return 'border border-input';
      default: return 'bg-primary text-primary-foreground';
    }
  }
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">المراسلات الداخلية</h1>

    <div class="flex gap-2">
      <button
        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
        on:click={() => {
          // إعادة تعيين حالة الرسالة كرسالة عادية بدون توقيع
          newMessage = {
            subject: '',
            content: '',
            receiver_id: null,
            receiver_unit_id: null,
            is_signed: false,
            signature: null
          };
          dialogOpen = true;
        }}
      >
        إرسال رسالة جديدة
      </button>
    </div>
  </div>

  <div class="flex justify-end gap-3 mb-6">
    <button
      class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-green-600 text-white hover:bg-green-700 h-10 px-4 py-2"
      on:click={openSignedMessageDialog}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path><path d="M14.5 19.5h-5c-1.5 0-2.5-1-2.5-2.5v-10c0-1.5 1-2.5 2.5-2.5h5c1.5 0 2.5 1 2.5 2.5v10c0 1.5-1 2.5-2.5 2.5Z"></path><path d="M12 22v-2"></path><path d="M12 4V2"></path></svg>
      إنشاء رسالة موقعة
    </button>
  </div>

  <div class="grid grid-cols-1 gap-6">
    <!-- Inbox -->
    <div class="bg-card rounded-lg shadow overflow-hidden">
      <div class="p-6 border-b">
        <h2 class="text-xl font-bold">صندوق الوارد</h2>
        <p class="text-muted-foreground">الرسائل الواردة إليك أو إلى وحدتك التنظيمية</p>
      </div>

      {#if inboxMessages.length === 0}
        <div class="text-center text-muted-foreground py-8">
          <p>لا توجد رسائل في صندوق الوارد</p>
        </div>
      {:else}
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-muted">
              <tr>
                <th class="text-right p-3 font-medium">الموضوع</th>
                <th class="text-right p-3 font-medium">المرسل</th>
                <th class="text-right p-3 font-medium">التاريخ</th>
                <th class="text-right p-3 font-medium">الحالة</th>
              </tr>
            </thead>
            <tbody>
              {#each inboxMessages as message}
                <tr class="border-t hover:bg-muted/50 {message.status === 'sent' || message.status === 'delivered' ? 'font-bold bg-muted/30' : ''}">
                  <td class="p-3">
                    <a
                      href="/dashboard/messages/view/{message.id}"
                      class="hover:underline text-primary"
                      on:click={() => markAsRead(message)}
                    >
                      {message.subject}
                    </a>
                  </td>
                  <td class="p-3">{message.sender?.full_name || 'غير معروف'}</td>
                  <td class="p-3">{formatDate(message.created_at)}</td>
                  <td class="p-3">
                    <span class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 {getStatusColor(message.status)}">
                      {getStatusText(message.status)}
                    </span>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {/if}
    </div>

    <!-- Sent Messages -->
    <div class="bg-card rounded-lg shadow overflow-hidden">
      <div class="p-6 border-b">
        <h2 class="text-xl font-bold">الرسائل المرسلة</h2>
        <p class="text-muted-foreground">الرسائل التي قمت بإرسالها</p>
      </div>

      {#if sentMessages.length === 0}
        <div class="text-center text-muted-foreground py-8">
          <p>لم تقم بإرسال أي رسائل بعد</p>
        </div>
      {:else}
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-muted">
              <tr>
                <th class="text-right p-3 font-medium">الموضوع</th>
                <th class="text-right p-3 font-medium">المستقبل</th>
                <th class="text-right p-3 font-medium">التاريخ</th>
                <th class="text-right p-3 font-medium">الحالة</th>
                <th class="text-right p-3 font-medium">التوقيع</th>
              </tr>
            </thead>
            <tbody>
              {#each sentMessages as message}
                <tr class="border-t hover:bg-muted/50">
                  <td class="p-3">
                    <a href="/dashboard/messages/view/{message.id}" class="hover:underline text-primary">
                      {message.subject}
                    </a>
                  </td>
                  <td class="p-3">
                    {message.receiver?.full_name || message.receiver_unit?.name || 'غير معروف'}
                  </td>
                  <td class="p-3">{formatDate(message.created_at)}</td>
                  <td class="p-3">
                    <span class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 {getStatusColor(message.status)}">
                      {getStatusText(message.status)}
                    </span>
                  </td>
                  <td class="p-3">
                    {#if message.is_signed}
                      <span class="inline-flex items-center rounded-full bg-green-100 text-green-800 border border-green-200 px-2.5 py-0.5 text-xs font-semibold">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1"><path d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path><path d="M14.5 19.5h-5c-1.5 0-2.5-1-2.5-2.5v-10c0-1.5 1-2.5 2.5-2.5h5c1.5 0 2.5 1 2.5 2.5v10c0 1.5-1 2.5-2.5 2.5Z"></path><path d="M12 22v-2"></path><path d="M12 4V2"></path></svg>
                        موقعة
                      </span>
                    {/if}
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {/if}
    </div>
  </div>

  <!-- New Message Dialog -->
  {#if dialogOpen}
    <div class="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="bg-card rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-auto">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <div>
              <h2 class="text-xl font-bold">
                {newMessage.is_signed ? 'إنشاء رسالة موقعة' : 'إرسال رسالة جديدة'}
              </h2>
              {#if newMessage.is_signed}
                <p class="text-sm text-green-600 mt-1">
                  هذه الرسالة ستكون موقعة إلكترونياً
                </p>
              {/if}
            </div>
            <button
              class="text-muted-foreground hover:text-foreground"
              on:click={() => dialogOpen = false}
            >
              ✕
            </button>
          </div>

          <div class="grid gap-4 py-4">
            <div class="grid gap-2">
              <label for="subject" class="block text-sm font-medium">الموضوع</label>
              <input
                id="subject"
                bind:value={newMessage.subject}
                placeholder="أدخل موضوع الرسالة"
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label for="receiver_type" class="block text-sm font-medium">نوع المستقبل</label>
                <select
                  id="receiver_type"
                  class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  on:change={(e) => {
                    const target = e.target as HTMLSelectElement;
                    if (target.value === 'user') {
                      newMessage.receiver_unit_id = null;
                    } else {
                      newMessage.receiver_id = null;
                    }
                  }}
                >
                  <option value="" disabled selected>اختر نوع المستقبل</option>
                  <option value="user">مستخدم</option>
                  <option value="unit">وحدة تنظيمية</option>
                </select>
              </div>

              {#if newMessage.receiver_unit_id === null && newMessage.receiver_id !== null}
                <div class="space-y-2">
                  <label for="receiver_id" class="block text-sm font-medium">المستخدم</label>
                  <select
                    id="receiver_id"
                    bind:value={newMessage.receiver_id}
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value={null} disabled selected>اختر المستخدم</option>
                    {#each users.filter(u => u.id !== currentUser?.id) as user}
                      <option value={user.id}>{user.full_name}</option>
                    {/each}
                  </select>
                </div>
              {:else}
                <div class="space-y-2">
                  <label for="receiver_unit_id" class="block text-sm font-medium">الوحدة التنظيمية</label>
                  <select
                    id="receiver_unit_id"
                    bind:value={newMessage.receiver_unit_id}
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value={null} disabled selected>اختر الوحدة التنظيمية</option>
                    {#each units as unit}
                      <option value={unit.id}>{unit.name} ({unit.type})</option>
                    {/each}
                  </select>
                </div>
              {/if}
            </div>

            <div class="grid gap-2">
              <label for="content" class="block text-sm font-medium">محتوى الرسالة</label>
              <textarea
                id="content"
                bind:value={newMessage.content}
                placeholder="أدخل محتوى الرسالة"
                class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                rows="6"
              ></textarea>
            </div>

            <!-- عرض حالة التوقيع -->
            {#if newMessage.is_signed}
              <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <div class="flex items-center text-green-700">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <span class="font-medium">سيتم توقيع هذه الرسالة إلكترونياً</span>
                </div>
                <p class="text-sm text-green-600 mr-7 mt-1">
                  سيتم إنشاء توقيع إلكتروني باستخدام بياناتك الشخصية ومحتوى الرسالة
                </p>
                <button
                  class="text-sm text-red-600 hover:text-red-800 mt-2 mr-7 underline"
                  on:click={handleCancelSignature}
                >
                  إلغاء التوقيع
                </button>
              </div>
            {/if}
          </div>

          <div class="flex justify-end gap-2 mt-4">
            <button
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
              on:click={() => dialogOpen = false}
            >
              إلغاء
            </button>

            {#if newMessage.is_signed && !newMessage.signature}
              <button
                class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-green-600 text-white hover:bg-green-700 h-10 px-4 py-2"
                on:click={handleCreateSignature}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path><path d="M14.5 19.5h-5c-1.5 0-2.5-1-2.5-2.5v-10c0-1.5 1-2.5 2.5-2.5h5c1.5 0 2.5 1 2.5 2.5v10c0 1.5-1 2.5-2.5 2.5Z"></path><path d="M12 22v-2"></path><path d="M12 4V2"></path></svg>
                توقيع الرسالة
              </button>
            {/if}

            <button
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
              on:click={handleSendMessage}
              disabled={newMessage.is_signed && !newMessage.signature}
            >
              إرسال
            </button>
          </div>
        </div>
      </div>
    </div>
  {/if}

  <!-- نافذة إدخال كلمة مرور التوقيع -->
  <SignaturePasswordDialog
    bind:isOpen={passwordDialogOpen}
    title="إنشاء توقيع إلكتروني"
    message="يرجى إدخال كلمة مرور للتوقيع الإلكتروني. سيتم استخدام كلمة المرور هذه للتحقق من صحة التوقيع."
    on:submit={handlePasswordSubmit}
    on:close={() => passwordDialogOpen = false}
  />
</div>
