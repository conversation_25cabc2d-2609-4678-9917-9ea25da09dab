<script lang="ts">
  import { onMount } from 'svelte';
  import { supabase } from '$lib/supabase';

  let loading = true;
  let error = null;
  let testResult = null;
  let documents = [];
  let signedDocuments = [];
  let connectionStatus = null;
  let userInfo = null;
  let documentId = '';

  // دالة للتحقق من الاتصال بقاعدة البيانات
  async function testDatabaseConnection() {
    try {
      loading = true;
      error = null;
      testResult = null;
      documents = [];
      signedDocuments = [];
      connectionStatus = "جاري التحقق من الاتصال...";

      // التحقق من المستخدم الحالي
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) {
        error = 'خطأ في التحقق من المستخدم: ' + userError.message;
        connectionStatus = "فشل الاتصال - خطأ في المستخدم";
        return;
      }

      if (!user) {
        error = 'لم يتم العثور على مستخدم مسجل الدخول';
        connectionStatus = "فشل الاتصال - المستخدم غير مسجل الدخول";
        return;
      }

      userInfo = `المستخدم الحالي: ${user.id} (${user.email})`;
      connectionStatus = "تم التحقق من المستخدم بنجاح";

      // اختبار الاتصال بقاعدة البيانات
      const { data: connectionData, error: connectionError } = await supabase
        .from('documents')
        .select('count')
        .limit(1);

      if (connectionError) {
        error = 'خطأ في الاتصال بقاعدة البيانات: ' + connectionError.message;
        connectionStatus = "فشل الاتصال بقاعدة البيانات";
        return;
      }

      connectionStatus = "تم الاتصال بقاعدة البيانات بنجاح";

      // اختبار جلب المستندات
      const { data: docsData, error: docsError } = await supabase
        .from('documents')
        .select('id, title, created_at')
        .limit(5);

      if (docsError) {
        error = 'خطأ في جلب المستندات: ' + docsError.message;
        return;
      }

      documents = docsData || [];
      testResult = `تم جلب ${documents.length} مستند`;

      // اختبار جلب المستندات الموقعة
      const { data: signedDocsData, error: signedDocsError } = await supabase
        .from('signed_documents')
        .select('id, document_id, status, created_at')
        .limit(5);

      if (signedDocsError) {
        error = 'خطأ في جلب المستندات الموقعة: ' + signedDocsError.message;
        return;
      }

      signedDocuments = signedDocsData || [];
      testResult += ` و ${signedDocuments.length} مستند موقع`;
    } catch (err) {
      error = 'خطأ غير متوقع: ' + (err.message || 'خطأ غير معروف');
      connectionStatus = "فشل الاتصال - خطأ غير متوقع";
    } finally {
      loading = false;
    }
  }

  // اختبار جلب مستند محدد
  async function testFetchDocument() {
    try {
      if (!documentId) {
        error = 'يرجى إدخال معرف المستند';
        return;
      }
      
      loading = true;
      error = null;
      testResult = null;
      
      console.log('جلب المستند بالمعرف:', documentId);
      
      const { data, error: fetchError } = await supabase
        .from('documents')
        .select(`
          *,
          creator:created_by(full_name),
          unit:unit_id(name)
        `)
        .eq('id', documentId)
        .single();
        
      if (fetchError) {
        error = 'خطأ في جلب المستند: ' + fetchError.message;
        return;
      }
      
      if (!data) {
        error = 'لم يتم العثور على المستند';
        return;
      }
      
      testResult = `تم جلب المستند بنجاح: ${data.title}`;
      console.log('بيانات المستند:', data);
    } catch (err) {
      error = 'خطأ غير متوقع: ' + (err.message || 'خطأ غير معروف');
    } finally {
      loading = false;
    }
  }

  // تنفيذ الاختبار عند تحميل الصفحة
  onMount(testDatabaseConnection);
</script>

<div class="container mx-auto p-4 rtl" dir="rtl">
  <h1 class="text-2xl font-bold mb-4">صفحة اختبار الاتصال بقاعدة البيانات</h1>

  <div class="mb-6 flex space-x-2 space-x-reverse">
    <button
      class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
      on:click={testDatabaseConnection}
      disabled={loading}
    >
      اختبار الاتصال
    </button>
  </div>

  {#if loading}
    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
      <p>جاري الاختبار...</p>
    </div>
  {/if}

  {#if connectionStatus}
    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
      <p><strong>حالة الاتصال:</strong> {connectionStatus}</p>
    </div>
  {/if}

  {#if userInfo}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      <p>{userInfo}</p>
    </div>
  {/if}

  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p><strong>خطأ:</strong> {error}</p>
    </div>
  {/if}

  {#if testResult}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      <p><strong>نتيجة الاختبار:</strong> {testResult}</p>
    </div>
  {/if}

  <div class="mb-6">
    <h2 class="text-xl font-bold mb-2">اختبار جلب مستند محدد</h2>
    <div class="flex space-x-2 space-x-reverse mb-4">
      <input
        type="text"
        bind:value={documentId}
        placeholder="أدخل معرف المستند"
        class="border border-gray-300 rounded px-4 py-2 flex-grow"
      />
      <button
        class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded"
        on:click={testFetchDocument}
        disabled={loading}
      >
        جلب المستند
      </button>
    </div>
  </div>

  <div class="mb-6">
    <h2 class="text-xl font-bold mb-2">المستندات ({documents.length})</h2>
    {#if documents.length === 0}
      <p class="text-gray-500">لا توجد مستندات</p>
    {:else}
      <table class="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
        <thead class="bg-gray-100">
          <tr>
            <th class="py-2 px-4 text-right">المعرف</th>
            <th class="py-2 px-4 text-right">العنوان</th>
            <th class="py-2 px-4 text-right">تاريخ الإنشاء</th>
            <th class="py-2 px-4 text-right">الإجراءات</th>
          </tr>
        </thead>
        <tbody>
          {#each documents as doc}
            <tr class="border-t">
              <td class="py-2 px-4">{doc.id}</td>
              <td class="py-2 px-4">{doc.title || 'بدون عنوان'}</td>
              <td class="py-2 px-4">{new Date(doc.created_at).toLocaleString('ar-LY')}</td>
              <td class="py-2 px-4">
                <button
                  class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded text-xs"
                  on:click={() => { documentId = doc.id; testFetchDocument(); }}
                >
                  اختبار
                </button>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    {/if}
  </div>

  <div>
    <h2 class="text-xl font-bold mb-2">المستندات الموقعة ({signedDocuments.length})</h2>
    {#if signedDocuments.length === 0}
      <p class="text-gray-500">لا توجد مستندات موقعة</p>
    {:else}
      <table class="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
        <thead class="bg-gray-100">
          <tr>
            <th class="py-2 px-4 text-right">المعرف</th>
            <th class="py-2 px-4 text-right">معرف المستند</th>
            <th class="py-2 px-4 text-right">الحالة</th>
            <th class="py-2 px-4 text-right">تاريخ الإنشاء</th>
          </tr>
        </thead>
        <tbody>
          {#each signedDocuments as doc}
            <tr class="border-t">
              <td class="py-2 px-4">{doc.id}</td>
              <td class="py-2 px-4">{doc.document_id}</td>
              <td class="py-2 px-4">{doc.status}</td>
              <td class="py-2 px-4">{new Date(doc.created_at).toLocaleString('ar-LY')}</td>
            </tr>
          {/each}
        </tbody>
      </table>
    {/if}
  </div>
</div>
