-- إنشاء وظيفة RPC للحصول على المستخدمين الذين لديهم صلاحية التوقيع
CREATE OR REPLACE FUNCTION get_users_with_signature_permission()
RETURNS TABLE (
  id UUID,
  full_name TEXT,
  email TEXT,
  role TEXT,
  unit_id UUID,
  unit_name TEXT,
  can_sign BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.id,
    p.full_name,
    p.email,
    r.name AS role,
    p.unit_id,
    u.name AS unit_name,
    COALESCE(sp.can_sign, r.name IN ('admin', 'مشرف', 'manager', 'مدير')) AS can_sign
  FROM profiles p
  LEFT JOIN roles r ON p.role_id = r.id
  LEFT JOIN units u ON p.unit_id = u.id
  LEFT JOIN signature_permissions sp ON sp.user_id = p.id
  WHERE
    -- المستخدمون الذين لديهم صلاحية التوقيع
    (sp.can_sign = true OR sp.can_sign IS NULL)
    AND (
      -- المستخدمون الذين لديهم صلاحية التوقيع في جدول signature_permissions
      EXISTS (
        SELECT 1 FROM signature_permissions sp2
        WHERE sp2.user_id = p.id AND sp2.can_sign = true
      )
      -- أو المستخدمون الذين لديهم دور مشرف أو مدير
      OR r.name IN ('admin', 'مشرف', 'manager', 'مدير')
    )
  ORDER BY p.full_name;
END;
$$;

-- إنشاء وظيفة RPC للتحقق من صلاحية المستخدم للتوقيع
CREATE OR REPLACE FUNCTION check_user_signature_permission(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  has_permission BOOLEAN;
  has_explicit_permission BOOLEAN;
  has_role_permission BOOLEAN;
BEGIN
  -- التحقق من وجود صلاحية صريحة في جدول signature_permissions
  SELECT EXISTS (
    SELECT 1 FROM signature_permissions sp
    WHERE sp.user_id = user_id AND sp.can_sign = true
  ) INTO has_explicit_permission;

  -- التحقق من وجود صلاحية ضمنية من خلال الدور
  SELECT EXISTS (
    SELECT 1 FROM profiles p
    JOIN roles r ON p.role_id = r.id
    WHERE p.id = user_id AND r.name IN ('admin', 'مشرف', 'manager', 'مدير')
  ) INTO has_role_permission;

  -- التحقق من وجود سجل في جدول signature_permissions يمنع الصلاحية صراحة
  IF has_explicit_permission = false THEN
    SELECT EXISTS (
      SELECT 1 FROM signature_permissions sp
      WHERE sp.user_id = user_id AND sp.can_sign = false
    ) INTO has_permission;

    -- إذا كان هناك سجل يمنع الصلاحية صراحة، فلا يمكن للمستخدم التوقيع حتى لو كان لديه دور مسموح به
    IF has_permission = true THEN
      RETURN false;
    END IF;
  END IF;

  -- إذا كان لديه صلاحية صريحة أو صلاحية من خلال الدور، فيمكنه التوقيع
  RETURN has_explicit_permission OR has_role_permission;
END;
$$;

-- إنشاء وظيفة RPC للحصول على المستندات الموقعة للمستخدم
CREATE OR REPLACE FUNCTION get_user_signed_documents(user_id UUID, status_filter TEXT DEFAULT NULL)
RETURNS TABLE (
  id UUID,
  document_id UUID,
  creator_id UUID,
  signer_id UUID,
  signed_at TIMESTAMPTZ,
  status TEXT,
  reference_number TEXT,
  created_at TIMESTAMPTZ,
  document_title TEXT,
  creator_name TEXT,
  signer_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    sd.id,
    sd.document_id,
    sd.creator_id,
    sd.signer_id,
    sd.signed_at,
    sd.status::TEXT,
    sd.reference_number,
    sd.created_at,
    d.title AS document_title,
    creator.full_name AS creator_name,
    signer.full_name AS signer_name
  FROM signed_documents sd
  JOIN documents d ON sd.document_id = d.id
  JOIN profiles creator ON sd.creator_id = creator.id
  LEFT JOIN profiles signer ON sd.signer_id = signer.id
  WHERE
    (
      -- المستندات التي أنشأها المستخدم
      sd.creator_id = user_id
      -- أو المستندات التي يجب أن يوقعها المستخدم
      OR sd.signer_id = user_id
      -- أو المستندات التي استلمها المستخدم للمراجعة
      OR sd.recipient_id = user_id
      -- أو المستندات التي استلمتها وحدة المستخدم للمراجعة
      OR EXISTS (
        SELECT 1 FROM profiles p
        WHERE p.id = user_id AND p.unit_id = sd.recipient_unit_id
      )
      -- أو المستخدم مشرف
      OR EXISTS (
        SELECT 1 FROM profiles p
        JOIN roles r ON p.role_id = r.id
        WHERE p.id = user_id AND r.name IN ('admin', 'مشرف')
      )
    )
    -- تطبيق فلتر الحالة إذا تم تمريره
    AND (status_filter IS NULL OR sd.status::TEXT = status_filter)
  ORDER BY sd.created_at DESC;
END;
$$;
