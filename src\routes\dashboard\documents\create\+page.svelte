<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib/supabase';
  import OfficialDocument from '$lib/components/OfficialDocument.svelte';
  import { createSignature } from '$lib/utils/signatureUtils';

  // متغيرات الصفحة
  let title = '';
  let content = '';
  let documentType = 'letter';
  let recipientId = '';
  let recipientUnitId = '';
  let recipientExternal = '';
  let unitName = '';
  let unitId: string | null = null;
  let unitLogo: string | null = null;
  let organizationLogo: string | null = null;
  let stamp: string | null = null;
  let signature: any = null;
  let currentUser: any = null;
  let recipients: any[] = [];
  let units: any[] = [];
  let documentTemplates: any[] = [];
  let selectedTemplateId = '';
  let isLoading = true;
  let isSending = false;
  let error: string | null = null;
  let success: string | null = null;
  let printMode = false;
  let passwordProtected = false;
  let signaturePassword = '';
  let attachments: any[] = [];
  let attachmentFiles: FileList | null = null;

  // نوع المستلم
  let recipientType = 'internal'; // internal, unit, external

  // جلب البيانات
  async function fetchData() {
    try {
      isLoading = true;

      // جلب بيانات المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();
      currentUser = user;

      if (!currentUser) {
        error = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة';
        return;
      }

      // التحقق من صلاحية المستخدم لإنشاء وثيقة رسمية
      const { data: canCreate, error: permissionError } = await supabase.rpc(
        'can_create_official_document',
        { user_id: currentUser.id }
      );

      if (permissionError || !canCreate) {
        error = 'ليس لديك صلاحية لإنشاء وثائق رسمية';
        return;
      }

      // جلب قائمة المستخدمين
      const { data: usersData, error: usersError } = await supabase
        .from('profiles')
        .select('id, full_name, email, unit_id, title')
        .neq('id', currentUser.id);

      if (usersError) throw usersError;
      recipients = usersData || [];

      // جلب قائمة الوحدات
      const { data: unitsData, error: unitsError } = await supabase
        .from('units')
        .select('*');

      if (unitsError) throw unitsError;
      units = unitsData || [];

      // جلب بيانات الوحدة الخاصة بالمستخدم الحالي
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('unit_id')
        .eq('id', currentUser.id)
        .single();

      if (userError) throw userError;

      if (userData && userData.unit_id) {
        unitId = userData.unit_id;

        // جلب بيانات الوحدة
        const { data: unitData, error: unitError } = await supabase
          .from('units')
          .select('*')
          .eq('id', userData.unit_id)
          .single();

        if (!unitError && unitData) {
          unitName = unitData.name || '';
          unitLogo = unitData.logo || null;

          // استخدام ختم الوحدة إذا كان موجوداً
          if (unitData.stamp) {
            stamp = unitData.stamp;
          }
        }
      }

      // جلب إعدادات المنظمة
      const { data: orgData, error: orgError } = await supabase
        .from('organization_settings')
        .select('*')
        .single();

      if (!orgError && orgData) {
        // استخدام الشعارات من إعدادات المنظمة
        organizationLogo = orgData.logo;

        // استخدام الختم الافتراضي إذا لم يكن هناك ختم للوحدة
        if (!stamp && orgData.default_stamp) {
          stamp = orgData.default_stamp;
        }

        console.log('Organization settings loaded:', {
          logo: orgData.logo,
          right_logo: orgData.right_logo,
          left_logo: orgData.left_logo,
          default_stamp: orgData.default_stamp
        });
      }

      // جلب قوالب الوثائق
      const { data: templatesData, error: templatesError } = await supabase
        .from('document_templates')
        .select('*')
        .or(`is_public.eq.true,creator_id.eq.${currentUser.id}`);

      if (templatesError) throw templatesError;
      documentTemplates = templatesData || [];

    } catch (err: any) {
      console.error('Error fetching data:', err);
      error = 'حدث خطأ أثناء جلب البيانات: ' + (err.message || err);
    } finally {
      isLoading = false;
    }
  }

  // تحميل قالب
  async function loadTemplate() {
    if (!selectedTemplateId) return;

    const template = documentTemplates.find(t => t.id === selectedTemplateId);
    if (template) {
      title = template.name;
      content = template.content;
      documentType = template.document_type;
    }
  }

  // معالجة المرفقات
  async function processAttachments() {
    const processedAttachments = [];

    if (attachmentFiles && attachmentFiles.length > 0) {
      for (let i = 0; i < attachmentFiles.length; i++) {
        const file = attachmentFiles[i];
        const fileExt = file.name.split('.').pop();
        const filePath = `saadabujnah/attachments/${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;

        // تحميل الملف
        const { data, error: uploadError } = await supabase.storage
          .from('saadabujnah')
          .upload(filePath, file, { upsert: true });

        if (uploadError) {
          throw uploadError;
        }

        processedAttachments.push({
          path: filePath,
          name: file.name,
          type: file.type,
          size: file.size
        });
      }
    }

    return processedAttachments;
  }

  // إنشاء وإرسال الوثيقة الرسمية
  async function createOfficialDocument() {
    try {
      if (!title || !content) {
        error = 'يرجى ملء جميع الحقول المطلوبة';
        return;
      }

      isSending = true;
      error = null;
      success = null;

      // التحقق من نوع المستلم
      let recipient_id = null;
      let recipient_unit_id = null;
      let recipient_external = null;

      if (recipientType === 'internal' && recipientId) {
        recipient_id = recipientId;
      } else if (recipientType === 'unit' && recipientUnitId) {
        recipient_unit_id = recipientUnitId;
      } else if (recipientType === 'external' && recipientExternal) {
        recipient_external = recipientExternal;
      }

      // إنشاء توقيع إلكتروني للوثيقة إذا كان مطلوباً
      let signatureData = null;
      if (passwordProtected) {
        signatureData = await createSignature(
          content,
          title,
          currentUser.id,
          signaturePassword
        );

        if (!signatureData) {
          throw new Error('فشل إنشاء التوقيع الإلكتروني');
        }

        signature = signatureData;
      }

      // معالجة المرفقات
      const processedAttachments = await processAttachments();

      // إنشاء الوثيقة في قاعدة البيانات
      const { data: documentData, error: documentError } = await supabase
        .from('official_documents')
        .insert({
          title,
          content,
          document_type: documentType,
          status: 'draft',
          creator_id: currentUser.id,
          unit_id: unitId,
          recipient_id,
          recipient_unit_id,
          recipient_external,
          is_signed: !!signatureData,
          signature: signatureData,
          password_protected: passwordProtected,
          unit_logo: unitLogo,
          unit_stamp: stamp,
          attachments: processedAttachments.length > 0 ? processedAttachments : null
        })
        .select()
        .single();

      if (documentError) throw documentError;

      success = 'تم إنشاء الوثيقة الرسمية بنجاح';

      // الانتقال إلى صفحة الوثيقة بعد فترة قصيرة
      setTimeout(() => {
        goto(`/dashboard/documents/view/${documentData.id}`);
      }, 2000);

    } catch (err: any) {
      console.error('Error creating official document:', err);
      error = 'حدث خطأ أثناء إنشاء الوثيقة: ' + (err.message || err);
    } finally {
      isSending = false;
    }
  }

  // طباعة الوثيقة
  function printDocument() {
    printMode = true;
    setTimeout(() => {
      window.print();
      printMode = false;
    }, 500);
  }

  // تحديث المحتوى
  function handleContentChange(event: CustomEvent) {
    content = event.detail;
  }

  // تحميل البيانات عند تحميل الصفحة
  onMount(fetchData);
</script>

<svelte:head>
  <title>إنشاء وثيقة رسمية</title>
  <style>
    @media print {
      body * {
        visibility: hidden;
      }
      .official-document, .official-document * {
        visibility: visible;
      }
      .official-document {
        position: absolute;
        left: 0;
        top: 0;
        width: 210mm;
        height: 297mm;
        margin: 0;
        /* هوامش مستند Word القياسية */
        padding: 25.4mm 31.7mm 25.4mm 31.7mm; /* top right bottom left - Word default margins */
        box-sizing: border-box;
        page-break-inside: auto;
        font-family: 'Calibri', 'Arial', sans-serif;
      }

      /* تنسيق محتوى الوثيقة للصفحات المتعددة */
      .document-content {
        overflow: visible;
        line-height: 1.15; /* مسافة السطور في Word الافتراضية */
        text-align: justify;
        font-size: 11pt; /* حجم الخط الافتراضي في Word */
        page-break-inside: auto;
        page-break-before: auto;
        page-break-after: auto;
        margin-bottom: 8pt; /* مسافة الفقرات في Word */
      }

      /* تنسيق الشعارات */
      .logo {
        width: 72pt; /* حوالي 1 بوصة - قياس شائع في Word */
        height: 72pt;
      }

      /* تنسيق العنوان */
      .header-text h1 {
        font-size: 24px;
        margin: 0 0 5px 0;
        font-weight: bold;
      }

      .header-text h2 {
        font-size: 18px;
        margin: 0;
        font-weight: bold;
      }

      /* تنسيق عنوان الوثيقة */
      .document-title h2 {
        font-size: 22px;
        font-weight: bold;
        padding: 5px 30px;
        border-bottom: 2px solid #000;
      }

      /* تنسيق التذييل */
      .document-footer {
        page-break-inside: avoid;
        padding-bottom: 15px;
      }

      /* تنسيق الختم */
      .stamp {
        width: 80px;
        height: 80px;
      }

      @page {
        size: 210mm 297mm; /* تحديد مقاس A4 بالضبط */
        margin: 0;
      }
    }
  </style>
</svelte:head>

<div class="container mx-auto p-4 rtl">
  <div class="mb-6">
    <h1 class="text-2xl font-bold mb-2">إنشاء وثيقة رسمية</h1>
    <p class="text-gray-600">قم بإنشاء وثيقة رسمية جديدة</p>
  </div>

  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      {error}
    </div>
  {/if}

  {#if success}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      {success}
    </div>
  {/if}

  {#if isLoading}
    <div class="flex justify-center items-center h-64">
      <div class="loader"></div>
    </div>
  {:else}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- نموذج إنشاء الوثيقة -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <!-- قوالب الوثائق -->
        <div class="mb-4">
          <label for="template" class="block text-gray-700 mb-2">قالب الوثيقة</label>
          <select
            id="template"
            bind:value={selectedTemplateId}
            on:change={loadTemplate}
            class="w-full p-2 border border-gray-300 rounded"
          >
            <option value="">اختر قالباً (اختياري)</option>
            {#each documentTemplates as template}
              <option value={template.id}>{template.name} ({template.is_public ? 'عام' : 'خاص'})</option>
            {/each}
          </select>
        </div>

        <!-- نوع الوثيقة -->
        <div class="mb-4">
          <label for="document-type" class="block text-gray-700 mb-2">نوع الوثيقة</label>
          <select
            id="document-type"
            bind:value={documentType}
            class="w-full p-2 border border-gray-300 rounded"
            required
          >
            <option value="letter">خطاب</option>
            <option value="memo">مذكرة</option>
            <option value="report">تقرير</option>
            <option value="decision">قرار</option>
            <option value="certificate">شهادة</option>
            <option value="contract">عقد</option>
            <option value="other">أخرى</option>
          </select>
        </div>

        <!-- الوحدة التنظيمية -->
        <div class="mb-4">
          <label for="unit" class="block text-gray-700 mb-2">الوحدة التنظيمية</label>
          <select
            id="unit"
            bind:value={unitId}
            class="w-full p-2 border border-gray-300 rounded"
            required
          >
            <option value="">اختر الوحدة</option>
            {#each units as unit}
              <option value={unit.id} selected={unit.id === unitId}>{unit.name}</option>
            {/each}
          </select>
        </div>

        <!-- نوع المستلم -->
        <div class="mb-4">
          <label class="block text-gray-700 mb-2">نوع المستلم</label>
          <div class="flex space-x-4 space-x-reverse">
            <label class="inline-flex items-center">
              <input type="radio" bind:group={recipientType} value="internal" class="ml-2">
              <span>مستخدم داخلي</span>
            </label>
            <label class="inline-flex items-center">
              <input type="radio" bind:group={recipientType} value="unit" class="ml-2">
              <span>وحدة تنظيمية</span>
            </label>
            <label class="inline-flex items-center">
              <input type="radio" bind:group={recipientType} value="external" class="ml-2">
              <span>جهة خارجية</span>
            </label>
          </div>
        </div>

        <!-- المستلم (حسب النوع) -->
        {#if recipientType === 'internal'}
          <div class="mb-4">
            <label for="recipient" class="block text-gray-700 mb-2">المستلم</label>
            <select
              id="recipient"
              bind:value={recipientId}
              class="w-full p-2 border border-gray-300 rounded"
            >
              <option value="">اختر المستلم</option>
              {#each recipients as recipient}
                <option value={recipient.id}>{recipient.full_name} ({recipient.email})</option>
              {/each}
            </select>
          </div>
        {:else if recipientType === 'unit'}
          <div class="mb-4">
            <label for="recipient-unit" class="block text-gray-700 mb-2">الوحدة المستلمة</label>
            <select
              id="recipient-unit"
              bind:value={recipientUnitId}
              class="w-full p-2 border border-gray-300 rounded"
            >
              <option value="">اختر الوحدة</option>
              {#each units as unit}
                <option value={unit.id}>{unit.name}</option>
              {/each}
            </select>
          </div>
        {:else if recipientType === 'external'}
          <div class="mb-4">
            <label for="recipient-external" class="block text-gray-700 mb-2">الجهة الخارجية</label>
            <input
              type="text"
              id="recipient-external"
              bind:value={recipientExternal}
              class="w-full p-2 border border-gray-300 rounded"
              placeholder="اسم الجهة الخارجية"
            />
          </div>
        {/if}

        <!-- عنوان الوثيقة -->
        <div class="mb-4">
          <label for="title" class="block text-gray-700 mb-2">عنوان الوثيقة</label>
          <input
            type="text"
            id="title"
            bind:value={title}
            class="w-full p-2 border border-gray-300 rounded"
            placeholder="عنوان الوثيقة"
            required
          />
        </div>

        <!-- محتوى الوثيقة -->
        <div class="mb-4">
          <label for="content" class="block text-gray-700 mb-2">محتوى الوثيقة</label>
          <textarea
            id="content"
            bind:value={content}
            class="w-full p-2 border border-gray-300 rounded h-64"
            placeholder="اكتب محتوى الوثيقة هنا..."
            required
          ></textarea>
        </div>

        <!-- المرفقات -->
        <div class="mb-4">
          <label for="attachments" class="block text-gray-700 mb-2">المرفقات</label>
          <input
            type="file"
            id="attachments"
            bind:files={attachmentFiles}
            class="w-full p-2 border border-gray-300 rounded"
            multiple
          />
          {#if attachmentFiles && attachmentFiles.length > 0}
            <div class="mt-2">
              <p>تم اختيار {attachmentFiles.length} ملف(ات)</p>
              <ul class="list-disc list-inside">
                {#each Array.from(attachmentFiles) as file}
                  <li>{file.name} ({Math.round(file.size / 1024)} كيلوبايت)</li>
                {/each}
              </ul>
            </div>
          {/if}
        </div>

        <!-- حماية الوثيقة -->
        <div class="mb-4">
          <label class="flex items-center">
            <input
              type="checkbox"
              bind:checked={passwordProtected}
              class="ml-2"
            />
            <span>حماية الوثيقة بكلمة مرور</span>
          </label>
        </div>

        {#if passwordProtected}
          <div class="mb-4">
            <label for="password" class="block text-gray-700 mb-2">كلمة المرور</label>
            <input
              type="password"
              id="password"
              bind:value={signaturePassword}
              class="w-full p-2 border border-gray-300 rounded"
              placeholder="أدخل كلمة المرور للتوقيع"
              required
            />
          </div>
        {/if}

        <div class="flex justify-between mt-6">
          <button
            type="button"
            class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
            on:click={printDocument}
            disabled={isSending}
          >
            طباعة الوثيقة
          </button>

          <button
            type="button"
            class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded"
            on:click={createOfficialDocument}
            disabled={isSending}
          >
            {isSending ? 'جاري الإنشاء...' : 'إنشاء الوثيقة'}
          </button>
        </div>
      </div>

      <!-- معاينة الوثيقة -->
      <div class="bg-gray-100 p-4 rounded-lg shadow-md overflow-auto max-h-screen">
        <h2 class="text-xl font-bold mb-4 text-center">معاينة الوثيقة</h2>
        <div class="preview-container">
          <OfficialDocument
            subject={title}
            content={content}
            unitName={unitName}
            unitLogo={unitLogo}
            organizationLogo={organizationLogo}
            stamp={stamp}
            signature={signature}
            sender={currentUser}
            printMode={printMode}
          />
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .rtl {
    direction: rtl;
  }

  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .preview-container {
    transform: scale(0.5);
    transform-origin: top center;
    height: 148.5mm; /* Half of A4 height */
    overflow: hidden;
  }
</style>
