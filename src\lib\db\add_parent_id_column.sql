-- إضافة عمود parent_id إلى جدول messages

-- التحقق من وجود العمود قبل إضافته
DO $$
BEGIN
    -- التحقق من وجود العمود
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'messages'
        AND column_name = 'parent_id'
    ) THEN
        -- إضافة العمود إذا لم يكن موجوداً
        ALTER TABLE messages ADD COLUMN parent_id UUID REFERENCES messages(id) NULL;
        
        -- إضافة فهرس للعمود
        CREATE INDEX idx_messages_parent_id ON messages(parent_id);
        
        -- إضافة تعليق للعمود
        COMMENT ON COLUMN messages.parent_id IS 'معرف الرسالة الأصلية التي تم الرد عليها';
    END IF;
END $$;

-- تحديث ذاكرة التخزين المؤقت للمخطط
NOTIFY pgrst, 'reload schema';
