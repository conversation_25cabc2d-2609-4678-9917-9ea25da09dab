/**
 * إدارة صلاحيات التوقيع الإلكتروني
 */
import { supabase } from '$lib/supabase';

// قائمة الأدوار المسموح لها بإنشاء توقيعات
const ALLOWED_SIGNATURE_ROLES = ['admin', 'manager', 'مدير', 'مشرف'];

/**
 * التحقق من صلاحية المستخدم لإنشاء توقيع
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<boolean>} - هل المستخدم مصرح له بإنشاء توقيع
 */
export async function checkSignaturePermission(userId) {
  if (!userId) {
    return false;
  }

  try {
    // استخدام وظيفة قاعدة البيانات للتحقق من صلاحية التوقيع
    const { data, error } = await supabase.rpc('check_user_can_sign', {
      p_user_id: userId
    });

    if (error) {
      console.error('خطأ في استدعاء وظيفة التحقق من صلاحية التوقيع:', error);

      // استخدام الطريقة البديلة في حالة فشل استدعاء الوظيفة
      return await checkSignaturePermissionFallback(userId);
    }

    return data || false;
  } catch (error) {
    console.error('خطأ في التحقق من صلاحيات التوقيع:', error);

    // استخدام الطريقة البديلة في حالة حدوث خطأ
    return await checkSignaturePermissionFallback(userId);
  }
}

/**
 * طريقة بديلة للتحقق من صلاحية المستخدم لإنشاء توقيع
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<boolean>} - هل المستخدم مصرح له بإنشاء توقيع
 */
async function checkSignaturePermissionFallback(userId) {
  try {
    // التحقق من دور المستخدم
    const { data: userData, error } = await supabase
      .from('profiles')
      .select('role, role_id, roles:role_id(name)')
      .eq('id', userId)
      .single();

    if (error || !userData) {
      console.error('خطأ في التحقق من صلاحيات المستخدم:', error);
      return false;
    }

    // التحقق من الدور المباشر
    if (userData.role && ALLOWED_SIGNATURE_ROLES.includes(userData.role)) {
      return true;
    }

    // التحقق من الدور المرتبط
    if (userData.roles && userData.roles.name && ALLOWED_SIGNATURE_ROLES.includes(userData.roles.name)) {
      return true;
    }

    // التحقق من جدول صلاحيات التوقيع الجديد
    const { data: signPermission, error: signError } = await supabase
      .from('signature_permissions')
      .select('can_sign')
      .eq('user_id', userId)
      .single();

    if (!signError && signPermission && signPermission.can_sign) {
      return true;
    }

    // التحقق من وجود صلاحية خاصة للتوقيع في النظام القديم
    const { data: permissionData, error: permError } = await supabase
      .from('user_permissions')
      .select('permission_id, permissions:permission_id(name, resource, action)')
      .eq('user_id', userId);

    if (!permError && permissionData && permissionData.length > 0) {
      const hasSignPermission = permissionData.some(perm =>
        perm.permissions &&
        perm.permissions.resource === 'messages' &&
        perm.permissions.action === 'sign'
      );

      if (hasSignPermission) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('خطأ في التحقق من صلاحيات التوقيع (الطريقة البديلة):', error);
    return false;
  }
}

/**
 * إضافة صلاحية التوقيع لمستخدم
 * @param {string} adminUserId - معرف المستخدم المشرف
 * @param {string} targetUserId - معرف المستخدم المراد منحه الصلاحية
 * @returns {Promise<{success: boolean, message: string}>} - نتيجة العملية
 */
export async function grantSignaturePermission(adminUserId, targetUserId) {
  if (!adminUserId || !targetUserId) {
    return { success: false, message: 'معرفات المستخدمين غير صالحة' };
  }

  try {
    // استخدام وظيفة قاعدة البيانات لمنح صلاحية التوقيع
    const { data, error } = await supabase.rpc('grant_signature_permission', {
      p_admin_id: adminUserId,
      p_user_id: targetUserId
    });

    if (error) {
      console.error('خطأ في استدعاء وظيفة منح صلاحية التوقيع:', error);

      // استخدام الطريقة البديلة في حالة فشل استدعاء الوظيفة
      return await grantSignaturePermissionFallback(adminUserId, targetUserId);
    }

    return {
      success: data || false,
      message: data ? 'تم منح صلاحية التوقيع بنجاح' : 'فشل في منح صلاحية التوقيع'
    };
  } catch (error) {
    console.error('خطأ في منح صلاحية التوقيع:', error);

    // استخدام الطريقة البديلة في حالة حدوث خطأ
    return await grantSignaturePermissionFallback(adminUserId, targetUserId);
  }
}

/**
 * طريقة بديلة لمنح صلاحية التوقيع لمستخدم
 * @param {string} adminUserId - معرف المستخدم المشرف
 * @param {string} targetUserId - معرف المستخدم المراد منحه الصلاحية
 * @returns {Promise<{success: boolean, message: string}>} - نتيجة العملية
 */
async function grantSignaturePermissionFallback(adminUserId, targetUserId) {
  try {
    // التحقق من صلاحيات المستخدم المشرف
    const isAdmin = await checkAdminPermission(adminUserId);
    if (!isAdmin) {
      return { success: false, message: 'ليس لديك صلاحية لمنح أذونات التوقيع' };
    }

    // إضافة صلاحية التوقيع في الجدول الجديد
    const { error: insertError } = await supabase
      .from('signature_permissions')
      .insert({
        user_id: targetUserId,
        can_sign: true,
        created_by: adminUserId
      })
      .onConflict('user_id')
      .merge();

    if (insertError) {
      console.error('خطأ في إضافة صلاحية التوقيع في الجدول الجديد:', insertError);

      // محاولة إضافة الصلاحية في النظام القديم
      return await grantSignaturePermissionLegacy(adminUserId, targetUserId);
    }

    return { success: true, message: 'تم منح صلاحية التوقيع بنجاح' };
  } catch (error) {
    console.error('خطأ في منح صلاحية التوقيع (الطريقة البديلة):', error);
    return { success: false, message: 'حدث خطأ أثناء منح صلاحية التوقيع' };
  }
}

/**
 * طريقة قديمة لمنح صلاحية التوقيع لمستخدم
 * @param {string} adminUserId - معرف المستخدم المشرف
 * @param {string} targetUserId - معرف المستخدم المراد منحه الصلاحية
 * @returns {Promise<{success: boolean, message: string}>} - نتيجة العملية
 */
async function grantSignaturePermissionLegacy(adminUserId, targetUserId) {
  try {
    // البحث عن صلاحية التوقيع
    const { data: permData, error: permError } = await supabase
      .from('permissions')
      .select('id')
      .eq('resource', 'messages')
      .eq('action', 'sign')
      .single();

    if (permError) {
      // إنشاء صلاحية التوقيع إذا لم تكن موجودة
      const { data: newPerm, error: createError } = await supabase
        .from('permissions')
        .insert({
          name: 'messages:sign',
          description: 'صلاحية إنشاء توقيعات إلكترونية للرسائل',
          resource: 'messages',
          action: 'sign'
        })
        .select('id')
        .single();

      if (createError) {
        return { success: false, message: 'فشل في إنشاء صلاحية التوقيع' };
      }

      // استخدام الصلاحية الجديدة
      const permissionId = newPerm.id;

      // منح الصلاحية للمستخدم
      const { error: grantError } = await supabase
        .from('user_permissions')
        .insert({
          user_id: targetUserId,
          permission_id: permissionId
        });

      if (grantError) {
        return { success: false, message: 'فشل في منح صلاحية التوقيع' };
      }

      return { success: true, message: 'تم منح صلاحية التوقيع بنجاح' };
    }

    // استخدام الصلاحية الموجودة
    const permissionId = permData.id;

    // التحقق من وجود الصلاحية بالفعل
    const { data: existingPerm, error: checkError } = await supabase
      .from('user_permissions')
      .select('id')
      .eq('user_id', targetUserId)
      .eq('permission_id', permissionId)
      .single();

    if (!checkError && existingPerm) {
      return { success: true, message: 'المستخدم لديه بالفعل صلاحية التوقيع' };
    }

    // منح الصلاحية للمستخدم
    const { error: grantError } = await supabase
      .from('user_permissions')
      .insert({
        user_id: targetUserId,
        permission_id: permissionId
      });

    if (grantError) {
      return { success: false, message: 'فشل في منح صلاحية التوقيع' };
    }

    return { success: true, message: 'تم منح صلاحية التوقيع بنجاح' };
  } catch (error) {
    console.error('خطأ في منح صلاحية التوقيع (الطريقة القديمة):', error);
    return { success: false, message: 'حدث خطأ أثناء منح صلاحية التوقيع' };
  }
}

/**
 * التحقق من صلاحيات المشرف
 * @param {string} userId - معرف المستخدم
 * @returns {Promise<boolean>} - هل المستخدم مشرف
 */
async function checkAdminPermission(userId) {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('role, roles:role_id(name)')
      .eq('id', userId)
      .single();

    if (error || !data) {
      return false;
    }

    // التحقق من الدور المباشر
    if (data.role === 'admin' || data.role === 'مشرف') {
      return true;
    }

    // التحقق من الدور المرتبط
    if (data.roles && (data.roles.name === 'admin' || data.roles.name === 'مشرف')) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('خطأ في التحقق من صلاحيات المشرف:', error);
    return false;
  }
}
