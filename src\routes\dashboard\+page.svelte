<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';

  let stats = {
    documents: 0,
    messages: 0,
    broadcasts: 0,
    users: 0
  };

  let userName = '';
  let userRole = '';
  let loading = true;
  let recentDocuments = [];
  let recentMessages = [];
  let userUnits = []; // الوحدات التي يكون المستخدم مسؤولاً عنها

  // بيانات الأنشطة الأخيرة
  const recentActivities = [
    {
      title: 'تم إضافة مستند جديد',
      description: 'تم إضافة مستند "تقرير الربع الأول" بواسطة أحمد محمد',
      time: 'منذ 2 ساعة',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>'
    },
    {
      title: 'تم إرسال مراسلة جديدة',
      description: 'تم إرسال مراسلة "طلب اجتماع" إلى قسم الموارد البشرية',
      time: 'منذ 3 ساعات',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 5H3v14h18V5z"/><path d="m3 5 9 9 9-9"/></svg>'
    },
    {
      title: 'تم إضافة مستخدم جديد',
      description: 'تم إضافة المستخدم "سارة أحمد" إلى النظام',
      time: 'منذ 5 ساعات',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>'
    }
  ];

  // روابط سريعة
  const quickLinks = [
    {
      title: 'إضافة مستند',
      href: '/dashboard/documents',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><line x1="12" y1="18" x2="12" y2="12"/><line x1="9" y1="15" x2="15" y2="15"/></svg>',
      color: 'bg-blue-100 text-blue-700'
    },
    {
      title: 'إرسال مراسلة',
      href: '/dashboard/messages',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>',
      color: 'bg-green-100 text-green-700'
    },
    {
      title: 'إضافة مستخدم',
      href: '/dashboard/users',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><line x1="19" y1="8" x2="19" y2="14"/><line x1="22" y1="11" x2="16" y2="11"/></svg>',
      color: 'bg-purple-100 text-purple-700'
    },
    {
      title: 'الهيكل التنظيمي',
      href: '/dashboard/organization',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 3H3v7h18V3z"/><path d="M21 14h-5v7h5v-7z"/><path d="M8 14H3v7h5v-7z"/><path d="M14.5 14.5h-5v7h5v-7z"/></svg>',
      color: 'bg-amber-100 text-amber-700'
    }
  ];

  onMount(async () => {
    loading = true;

    try {
      // جلب بيانات المستخدم
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('full_name, role, unit_id')
          .eq('id', user.id)
          .single();

        if (profile) {
          userName = profile.full_name;
          userRole = profile.role;

          // جلب الوحدات التي يكون المستخدم مسؤولاً عنها
          const { data: userUnitData } = await supabase
            .from('user_units')
            .select(`
              unit_id,
              is_primary,
              units:unit_id (
                id,
                name,
                type
              )
            `)
            .eq('user_id', user.id);

          if (userUnitData && userUnitData.length > 0) {
            userUnits = userUnitData;
          } else if (profile.unit_id) {
            // إذا لم تكن هناك وحدات متعددة، نستخدم الوحدة الأساسية
            const { data: primaryUnit } = await supabase
              .from('units')
              .select('id, name, type')
              .eq('id', profile.unit_id)
              .single();

            if (primaryUnit) {
              userUnits = [{
                unit_id: primaryUnit.id,
                is_primary: true,
                units: primaryUnit
              }];
            }
          }
        }
      }

      // جلب الإحصائيات
      const [documentsCount, messagesCount, broadcastsCount, usersCount] = await Promise.all([
        supabase.from('documents').select('id', { count: 'exact', head: true }),
        supabase.from('messages').select('id', { count: 'exact', head: true }),
        supabase.from('broadcasts').select('id', { count: 'exact', head: true }),
        supabase.from('profiles').select('id', { count: 'exact', head: true })
      ]);

      stats = {
        documents: documentsCount.count || 0,
        messages: messagesCount.count || 0,
        broadcasts: broadcastsCount.count || 0,
        users: usersCount.count || 0
      };

      // Fetch recent documents
      const { data: documents } = await supabase
        .from('documents')
        .select('id, title, document_type, created_at')
        .order('created_at', { ascending: false })
        .limit(5);

      if (documents) {
        recentDocuments = documents;
      }

      // Fetch recent messages
      const { data: messages } = await supabase
        .from('messages')
        .select('id, subject, status, created_at')
        .order('created_at', { ascending: false })
        .limit(5);

      if (messages) {
        recentMessages = messages;
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      loading = false;
    }
  });

  // تحديد الوقت الحالي للترحيب
  let greeting = '';
  const hour = new Date().getHours();

  if (hour >= 5 && hour < 12) {
    greeting = 'صباح الخير';
  } else if (hour >= 12 && hour < 17) {
    greeting = 'مساء الخير';
  } else {
    greeting = 'مساء الخير';
  }

  // دالة لتنسيق التاريخ (تستخدم عند عرض المستندات والمراسلات)
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  }
</script>

<div class="dark:bg-gray-900">
  {#if loading}
    <div class="flex items-center justify-center h-64">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary dark:border-blue-400"></div>
    </div>
  {:else}
    <!-- Welcome Section -->
    <div class="mb-8">
      <div class="bg-card rounded-lg border border-gray-200/50 shadow-sm p-6 dark:bg-gray-800 dark:border-gray-700/50">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div>
            <h1 class="text-3xl font-bold mb-2">
              <span class="text-primary">{greeting}،</span>
              {userName || 'مرحباً بك'}
            </h1>
            <p class="text-muted-foreground">
              {#if userRole}
                أنت مسجل الدخول كـ <span class="font-medium">{userRole}</span>
              {:else}
                مرحباً بك في نظام الأرشفة الإلكترونية
              {/if}
            </p>

            {#if userUnits && userUnits.length > 0}
              <div class="mt-2">
                <p class="text-sm text-muted-foreground mb-1">الوحدات المسؤول عنها:</p>
                <div class="flex flex-wrap gap-2">
                  {#each userUnits as unitItem}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {unitItem.is_primary ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}">
                      {unitItem.units.name}
                      {#if unitItem.is_primary}
                        <span class="mr-1 text-xs">(أساسية)</span>
                      {/if}
                    </span>
                  {/each}
                </div>
              </div>
            {/if}
          </div>
          <div class="mt-4 md:mt-0">
            <div class="text-sm text-muted-foreground">
              {new Date().toLocaleDateString('ar-SA', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Links -->
    <div class="mb-8">
      <h2 class="text-xl font-bold mb-4">روابط سريعة</h2>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        {#each quickLinks as link}
          <a href={link.href} class="bg-card hover:bg-muted/50 border border-gray-200/50 rounded-lg p-4 transition-colors flex flex-col items-center text-center dark:bg-gray-800 dark:border-gray-700/50 dark:hover:bg-gray-700/50">
            <div class="{link.color} p-3 rounded-full mb-3">
              {@html link.icon}
            </div>
            <span class="font-medium">{link.title}</span>
          </a>
        {/each}
      </div>
    </div>

    <!-- Statistics -->
    <div class="mb-8">
      <h2 class="text-xl font-bold mb-4">الإحصائيات</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-card rounded-lg border border-gray-200/50 shadow-sm p-6 transition-all hover:shadow dark:bg-gray-800 dark:border-gray-700/50">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-muted-foreground mb-1 dark:text-gray-400">المستندات</p>
              <p class="text-3xl font-bold">{stats.documents}</p>
            </div>
            <div class="bg-blue-100 text-blue-700 p-3 rounded-full dark:bg-blue-900/30 dark:text-blue-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/></svg>
            </div>
          </div>
        </div>

        <div class="bg-card rounded-lg border border-gray-200/50 shadow-sm p-6 transition-all hover:shadow dark:bg-gray-800 dark:border-gray-700/50">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-muted-foreground mb-1 dark:text-gray-400">المراسلات</p>
              <p class="text-3xl font-bold">{stats.messages}</p>
            </div>
            <div class="bg-green-100 text-green-700 p-3 rounded-full dark:bg-green-900/30 dark:text-green-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 5H3v14h18V5z"/><path d="m3 5 9 9 9-9"/></svg>
            </div>
          </div>
        </div>

        <div class="bg-card rounded-lg border border-gray-200/50 shadow-sm p-6 transition-all hover:shadow dark:bg-gray-800 dark:border-gray-700/50">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-muted-foreground mb-1 dark:text-gray-400">التعميمات</p>
              <p class="text-3xl font-bold">{stats.broadcasts}</p>
            </div>
            <div class="bg-amber-100 text-amber-700 p-3 rounded-full dark:bg-amber-900/30 dark:text-amber-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/><path d="M19 10v2a7 7 0 0 1-14 0v-2"/><line x1="12" y1="19" x2="12" y2="23"/><line x1="8" y1="23" x2="16" y2="23"/></svg>
            </div>
          </div>
        </div>

        <div class="bg-card rounded-lg border border-gray-200/50 shadow-sm p-6 transition-all hover:shadow dark:bg-gray-800 dark:border-gray-700/50">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-muted-foreground mb-1 dark:text-gray-400">المستخدمين</p>
              <p class="text-3xl font-bold">{stats.users}</p>
            </div>
            <div class="bg-purple-100 text-purple-700 p-3 rounded-full dark:bg-purple-900/30 dark:text-purple-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- الوحدات المسؤول عنها -->
    {#if userUnits && userUnits.length > 0}
      <div class="mb-8">
        <h2 class="text-xl font-bold mb-4">الوحدات المسؤول عنها</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {#each userUnits as unitItem}
            <div class="bg-card rounded-lg border border-gray-200/50 shadow-sm p-4 transition-all hover:shadow dark:bg-gray-800 dark:border-gray-700/50 {unitItem.is_primary ? 'border-blue-300/50 dark:border-blue-700/50' : ''}">
              <div class="flex items-center">
                <div class="{unitItem.is_primary ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'} p-3 rounded-full ml-3 dark:bg-gray-700 dark:text-gray-300">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 3H3v7h18V3z"/><path d="M21 14h-5v7h5v-7z"/><path d="M8 14H3v7h5v-7z"/><path d="M14.5 14.5h-5v7h5v-7z"/></svg>
                </div>
                <div>
                  <h3 class="font-medium text-lg">{unitItem.units.name}</h3>
                  <p class="text-sm text-muted-foreground dark:text-gray-400">{unitItem.units.type || 'وحدة تنظيمية'}</p>
                  {#if unitItem.is_primary}
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1 dark:bg-blue-900/30 dark:text-blue-400">
                      الوحدة الأساسية
                    </span>
                  {/if}
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Recent Activity and System Status -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Recent Activity -->
      <div class="lg:col-span-2">
        <div class="bg-card rounded-lg border border-gray-200/50 shadow-sm dark:bg-gray-800 dark:border-gray-700/50">
          <div class="p-6 border-b border-gray-200/50 dark:border-gray-700/50">
            <h2 class="text-xl font-bold">النشاطات الأخيرة</h2>
          </div>
          <div class="divide-y divide-gray-200/50 dark:divide-gray-700/50">
            {#each recentActivities as activity}
              <div class="p-4 flex items-start">
                <div class="bg-primary/10 p-2 rounded-full ml-4 mt-1 dark:bg-blue-900/30">
                  {@html activity.icon}
                </div>
                <div class="flex-1">
                  <h3 class="font-medium">{activity.title}</h3>
                  <p class="text-sm text-muted-foreground dark:text-gray-400">{activity.description}</p>
                  <p class="text-xs text-muted-foreground mt-1 dark:text-gray-500">{activity.time}</p>
                </div>
              </div>
            {/each}
          </div>
          <div class="p-4 text-center">
            <a href="/dashboard/activities" class="text-primary text-sm hover:underline dark:text-blue-400">عرض جميع النشاطات</a>
          </div>
        </div>
      </div>

      <!-- System Status -->
      <div>
        <div class="bg-card rounded-lg border border-gray-200/50 shadow-sm dark:bg-gray-800 dark:border-gray-700/50">
          <div class="p-6 border-b border-gray-200/50 dark:border-gray-700/50">
            <h2 class="text-xl font-bold">حالة النظام</h2>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span>حالة الخادم</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <span class="ml-1 h-2 w-2 rounded-full bg-green-500"></span>
                  متصل
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span>قاعدة البيانات</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <span class="ml-1 h-2 w-2 rounded-full bg-green-500"></span>
                  متصل
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span>خدمة التخزين</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <span class="ml-1 h-2 w-2 rounded-full bg-green-500"></span>
                  متصل
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span>آخر تحديث للنظام</span>
                <span class="text-sm text-muted-foreground">
                  {new Date().toLocaleDateString('ar-SA')}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Help Card -->
        <div class="bg-primary/5 rounded-lg border border-blue-200/20 shadow-sm p-6 mt-6 dark:bg-blue-900/10 dark:border-blue-800/20">
          <div class="flex flex-col items-center text-center">
            <div class="bg-primary/10 p-3 rounded-full mb-4 dark:bg-blue-900/30">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><circle cx="12" cy="12" r="10"/><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/><path d="M12 17h.01"/></svg>
            </div>
            <h3 class="text-lg font-bold mb-2">بحاجة إلى مساعدة؟</h3>
            <p class="text-sm text-muted-foreground mb-4 dark:text-gray-400">
              إذا كنت بحاجة إلى مساعدة في استخدام النظام، يرجى التواصل مع فريق الدعم الفني.
            </p>
            <a href="/dashboard/support" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-200 bg-white hover:bg-gray-100 hover:text-gray-900 h-9 px-4 py-2 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-700">
              طلب مساعدة
            </a>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>
