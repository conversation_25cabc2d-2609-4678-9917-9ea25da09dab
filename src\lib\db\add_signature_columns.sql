-- إضافة أعمدة التوقيع الإلكتروني إلى جدول المراسلات

-- التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
DO $$
BEGIN
  -- إضافة عمود is_signed إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'messages' 
    AND column_name = 'is_signed'
  ) THEN
    ALTER TABLE messages ADD COLUMN is_signed BOOLEAN DEFAULT FALSE;
  END IF;

  -- إضافة عمود signature إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'messages' 
    AND column_name = 'signature'
  ) THEN
    ALTER TABLE messages ADD COLUMN signature JSONB DEFAULT NULL;
  END IF;
END
$$;

-- إنشاء وظيفة للتحقق من وجود أعمدة التوقيع
CREATE OR REPLACE FUNCTION check_signature_columns_exist()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_signed_exists BOOLEAN;
  signature_exists BOOLEAN;
BEGIN
  -- التحقق من وجود عمود is_signed
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'messages' 
    AND column_name = 'is_signed'
  ) INTO is_signed_exists;

  -- التحقق من وجود عمود signature
  SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'messages' 
    AND column_name = 'signature'
  ) INTO signature_exists;

  -- إرجاع true إذا كانت كلا العمودين موجودين
  RETURN is_signed_exists AND signature_exists;
END;
$$;

-- إنشاء وظيفة لإضافة أعمدة التوقيع
CREATE OR REPLACE FUNCTION add_signature_columns_if_not_exist()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- إضافة عمود is_signed إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'messages' 
    AND column_name = 'is_signed'
  ) THEN
    ALTER TABLE messages ADD COLUMN is_signed BOOLEAN DEFAULT FALSE;
  END IF;

  -- إضافة عمود signature إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'messages' 
    AND column_name = 'signature'
  ) THEN
    ALTER TABLE messages ADD COLUMN signature JSONB DEFAULT NULL;
  END IF;
END;
$$;
