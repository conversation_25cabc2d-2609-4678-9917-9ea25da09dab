<script lang="ts">
  import { goto } from '$app/navigation';
  import { supabase } from '$lib/supabase';

  let email = '';
  let password = '';
  let loading = false;
  let errorMessage = '';
  let showPassword = false;

  async function handleLogin() {
    try {
      if (!email || !password) {
        errorMessage = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
        return;
      }

      loading = true;
      errorMessage = '';

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        if (error.message.includes('Invalid login')) {
          errorMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        } else {
          errorMessage = error.message;
        }
        return;
      }

      if (data) {
        goto('/dashboard');
      }
    } catch (error) {
      errorMessage = 'حدث خطأ أثناء تسجيل الدخول';
      console.error('Login error:', error);
    } finally {
      loading = false;
    }
  }

  function togglePasswordVisibility() {
    showPassword = !showPassword;
  }

  // للتجربة السريعة - يمكن إزالة هذا الكود في الإنتاج
  function fillDemoCredentials() {
    email = '<EMAIL>';
    password = 'Admin123!';
  }
</script>

<div class="min-h-screen flex items-center justify-center bg-gradient-to-b from-primary/5 to-background py-12 px-4">
  <div class="w-full max-w-md">
    <div class="text-center mb-8">
      <div class="inline-block p-4 bg-primary/10 rounded-full mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary"><path d="M2 18v3c0 .6.4 1 1 1h4v-3h3v-3h2l1.4-1.4a6.5 6.5 0 1 0-4-4Z"/><circle cx="16.5" cy="7.5" r=".5"/></svg>
      </div>
      <h1 class="text-3xl font-bold mb-2">مرحباً بك مجدداً</h1>
      <p class="text-muted-foreground">قم بتسجيل الدخول للوصول إلى نظام الأرشفة الإلكترونية</p>
    </div>

    <div class="bg-card rounded-lg shadow-lg border border-gray-200/50 p-8">
      <form on:submit|preventDefault={handleLogin} class="space-y-5">
        {#if errorMessage}
          <div class="bg-destructive/10 border border-red-200/30 p-4 rounded-md text-destructive text-sm flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 mt-0.5"><circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="12"/><line x1="12" y1="16" x2="12.01" y2="16"/></svg>
            <span>{errorMessage}</span>
          </div>
        {/if}

        <div class="space-y-2">
          <label for="email" class="block text-sm font-medium">البريد الإلكتروني</label>
          <div class="relative">
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-muted-foreground">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="16" x="2" y="4" rx="2"/><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/></svg>
            </div>
            <input
              id="email"
              type="email"
              bind:value={email}
              placeholder="أدخل البريد الإلكتروني"
              class="flex h-12 w-full rounded-md border border-gray-200 bg-white px-3 py-2 pr-10 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              required
            />
          </div>
        </div>

        <div class="space-y-2">
          <div class="flex justify-between items-center">
            <label for="password" class="block text-sm font-medium">كلمة المرور</label>
          </div>
          <div class="relative">
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-muted-foreground">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"/><path d="M7 11V7a5 5 0 0 1 10 0v4"/></svg>
            </div>
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              bind:value={password}
              placeholder="أدخل كلمة المرور"
              class="flex h-12 w-full rounded-md border border-gray-200 bg-white px-3 py-2 pr-10 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              required
            />
            <button
              type="button"
              class="absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground hover:text-foreground"
              on:click={togglePasswordVisibility}
            >
              {#if showPassword}
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"/><path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68"/><path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61"/><line x1="2" x2="22" y1="2" y2="22"/></svg>
              {:else}
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
              {/if}
            </button>
          </div>
        </div>

        <button
          type="submit"
          class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-12 px-4 py-2 w-full"
          disabled={loading}
        >
          {#if loading}
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            جاري تسجيل الدخول...
          {:else}
            تسجيل الدخول
          {/if}
        </button>

        <!-- للتجربة السريعة - يمكن إزالة هذا الزر في الإنتاج -->
        <button
          type="button"
          class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-gray-200 bg-white hover:bg-gray-100 hover:text-gray-900 h-12 px-4 py-2 w-full mt-2"
          on:click={fillDemoCredentials}
        >
          ملء بيانات تجريبية
        </button>
      </form>

      <div class="text-center mt-6">
        <p class="text-sm text-muted-foreground">
          لا تملك حساب؟ يرجى التواصل مع مسؤول النظام
        </p>
      </div>
    </div>

    <div class="text-center mt-8">
      <a href="/" class="text-sm text-primary hover:underline">العودة إلى الصفحة الرئيسية</a>
    </div>
  </div>
</div>
