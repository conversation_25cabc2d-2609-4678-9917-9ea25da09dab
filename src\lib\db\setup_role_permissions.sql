-- ملف ربط الأدوار بالصلاحيات
-- يقوم هذا الملف بربط الأدوار المختلفة بالصلاحيات المناسبة

-- ربط دور مدير النظام (admin) بجميع الصلاحيات
DO $$
DECLARE
  admin_role_id UUID;
  perm_id UUID;
  perm_cursor CURSOR FOR 
    SELECT id FROM permissions;
BEGIN
  -- الحصول على معرف دور مدير النظام
  SELECT id INTO admin_role_id FROM roles WHERE name = 'admin';
  
  -- ربط جميع الصلاحيات بدور مدير النظام
  IF admin_role_id IS NOT NULL THEN
    OPEN perm_cursor;
    LOOP
      FETCH perm_cursor INTO perm_id;
      EXIT WHEN NOT FOUND;
      
      BEGIN
        INSERT INTO role_permissions (role_id, permission_id)
        VALUES (admin_role_id, perm_id)
        ON CONFLICT (role_id, permission_id) DO NOTHING;
      EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Could not insert permission for admin role: %', SQLERRM;
      END;
    END LOOP;
    CLOSE perm_cursor;
  END IF;
END $$;

-- ربط دور المدير (manager) بالصلاحيات المناسبة
DO $$
DECLARE
  manager_role_id UUID;
  perm_id UUID;
  
  -- الصلاحيات التي يمتلكها المدير
  manager_permissions CURSOR FOR 
    SELECT id FROM permissions 
    WHERE 
      -- صلاحيات المستندات
      (resource = 'documents' AND action IN ('create', 'read', 'update')) OR
      -- صلاحيات المستخدمين
      (resource = 'users' AND action IN ('read', 'update')) OR
      -- صلاحيات المراسلات
      (resource = 'messages' AND action IN ('create', 'read', 'update')) OR
      -- صلاحيات التعميمات
      (resource = 'broadcasts' AND action IN ('create', 'read', 'update')) OR
      -- صلاحيات الهيكل التنظيمي
      (resource = 'organization' AND action IN ('read', 'update')) OR
      -- صلاحيات النظام
      (resource = 'system' AND action = 'logs');
BEGIN
  -- الحصول على معرف دور المدير
  SELECT id INTO manager_role_id FROM roles WHERE name = 'manager';
  
  -- ربط الصلاحيات المناسبة بدور المدير
  IF manager_role_id IS NOT NULL THEN
    OPEN manager_permissions;
    LOOP
      FETCH manager_permissions INTO perm_id;
      EXIT WHEN NOT FOUND;
      
      BEGIN
        INSERT INTO role_permissions (role_id, permission_id)
        VALUES (manager_role_id, perm_id)
        ON CONFLICT (role_id, permission_id) DO NOTHING;
      EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Could not insert permission for manager role: %', SQLERRM;
      END;
    END LOOP;
    CLOSE manager_permissions;
  END IF;
END $$;

-- ربط دور المستخدم العادي (user) بالصلاحيات المناسبة
DO $$
DECLARE
  user_role_id UUID;
  perm_id UUID;
  
  -- الصلاحيات التي يمتلكها المستخدم العادي
  user_permissions CURSOR FOR 
    SELECT id FROM permissions 
    WHERE 
      -- صلاحيات المستندات
      (resource = 'documents' AND action IN ('read')) OR
      -- صلاحيات المستخدمين
      (resource = 'users' AND action = 'read') OR
      -- صلاحيات المراسلات
      (resource = 'messages' AND action IN ('create', 'read')) OR
      -- صلاحيات التعميمات
      (resource = 'broadcasts' AND action = 'read') OR
      -- صلاحيات الهيكل التنظيمي
      (resource = 'organization' AND action = 'read');
BEGIN
  -- الحصول على معرف دور المستخدم العادي
  SELECT id INTO user_role_id FROM roles WHERE name = 'user';
  
  -- ربط الصلاحيات المناسبة بدور المستخدم العادي
  IF user_role_id IS NOT NULL THEN
    OPEN user_permissions;
    LOOP
      FETCH user_permissions INTO perm_id;
      EXIT WHEN NOT FOUND;
      
      BEGIN
        INSERT INTO role_permissions (role_id, permission_id)
        VALUES (user_role_id, perm_id)
        ON CONFLICT (role_id, permission_id) DO NOTHING;
      EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Could not insert permission for user role: %', SQLERRM;
      END;
    END LOOP;
    CLOSE user_permissions;
  END IF;
END $$;
