import { supabase } from '$lib/supabase';

// دالة لتحميل الشعارات والأختام مع دعم روابط Google Drive
export async function loadLogosAndStamp(
  unitId: string | null,
  sender: any,
  organizationLogo: string | null,
  unitLogo: string | null,
  stamp: string | null
) {
  try {
    let rightLogoUrl = '';
    let leftLogoUrl = '';
    let unitLogoUrl = '';
    let organizationLogoUrl = '';
    let stampUrl = '';
    let orgSettings: any = null;

    // جلب معرف الوحدة من بيانات المرسل
    let senderUnitId = unitId;

    if (!senderUnitId && sender && sender.unit_id) {
      senderUnitId = sender.unit_id;
    } else if (!senderUnitId && sender && sender.id) {
      // جلب معرف الوحدة من بيانات المستخدم
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('unit_id')
        .eq('id', sender.id)
        .single();

      if (!userError && userData && userData.unit_id) {
        senderUnitId = userData.unit_id;
      }
    }

    // جلب إعدادات المنظمة
    const { data: settings, error: settingsError } = await supabase
      .from('organization_settings')
      .select('*')
      .single();

    if (!settingsError && settings) {
      orgSettings = settings;

      // استخدام الشعارات من إعدادات المنظمة إذا لم يتم تحديدها
      if (!organizationLogo && settings.logo) {
        organizationLogo = settings.logo;
        // إذا كان الشعار عبارة عن رابط URL، استخدمه مباشرة
        if (settings.logo && settings.logo.startsWith('http')) {
          organizationLogoUrl = settings.logo;
          console.log('Using direct URL for organization logo:', settings.logo);
        }
      }

      // استخدام الشعار الأيمن من إعدادات المنظمة
      if (settings.right_logo) {
        // إذا كان الشعار عبارة عن رابط URL، استخدمه مباشرة
        if (settings.right_logo.startsWith('http')) {
          rightLogoUrl = settings.right_logo;
          console.log('Using direct URL for right logo:', settings.right_logo);
        }
      }

      // استخدام الشعار الأيسر من إعدادات المنظمة
      if (settings.left_logo) {
        // إذا كان الشعار عبارة عن رابط URL، استخدمه مباشرة
        if (settings.left_logo.startsWith('http')) {
          leftLogoUrl = settings.left_logo;
          console.log('Using direct URL for left logo:', settings.left_logo);
        }
      }

      // استخدام الختم الافتراضي إذا لم يتم تحديد ختم
      if (!stamp && settings.default_stamp) {
        stamp = settings.default_stamp;
        // إذا كان الختم عبارة عن رابط URL، استخدمه مباشرة
        if (settings.default_stamp.startsWith('http')) {
          stampUrl = settings.default_stamp;
          console.log('Using direct URL for stamp:', settings.default_stamp);
        }
      }
    }

    // جلب شعارات وأختام الوحدة إذا كان معرف الوحدة متاحاً
    if (senderUnitId) {
      const { data: unitData, error: unitError } = await supabase
        .from('units')
        .select('*')
        .eq('id', senderUnitId)
        .single();

      if (!unitError && unitData) {
        // استخدام شعارات وأختام الوحدة إذا كانت متاحة
        if (!unitLogo && unitData.logo) {
          unitLogo = unitData.logo;
          // إذا كان الشعار عبارة عن رابط URL، استخدمه مباشرة
          if (unitData.logo && unitData.logo.startsWith('http')) {
            unitLogoUrl = unitData.logo;
            console.log('Using direct URL for unit logo:', unitData.logo);
          }
        }

        if (!stamp && unitData.stamp) {
          stamp = unitData.stamp;
          // إذا كان الختم عبارة عن رابط URL، استخدمه مباشرة
          if (unitData.stamp && unitData.stamp.startsWith('http')) {
            stampUrl = unitData.stamp;
            console.log('Using direct URL for unit stamp:', unitData.stamp);
          }
        }

        // استخدام الشعار الأيمن والأيسر من الوحدة إذا كانا متاحين
        if (unitData.right_logo) {
          // إذا كان الشعار عبارة عن رابط URL، استخدمه مباشرة
          if (unitData.right_logo && unitData.right_logo.startsWith('http')) {
            rightLogoUrl = unitData.right_logo;
            console.log('Using direct URL for unit right logo:', unitData.right_logo);
          }
        }

        if (unitData.left_logo) {
          // إذا كان الشعار عبارة عن رابط URL، استخدمه مباشرة
          if (unitData.left_logo && unitData.left_logo.startsWith('http')) {
            leftLogoUrl = unitData.left_logo;
            console.log('Using direct URL for unit left logo:', unitData.left_logo);
          }
        }
      }
    }

    // تحميل الشعارات والأختام من Supabase Storage إذا كانت ليست روابط URL

    // تحميل شعار الوحدة إذا كان موجوداً وليس رابط URL
    if (unitLogo && !unitLogoUrl) {
      if (unitLogo.startsWith('http')) {
        unitLogoUrl = unitLogo;
        console.log('Using direct URL for unit logo (fallback):', unitLogo);
      } else {
        try {
          console.log('Loading unit logo from storage:', unitLogo);
          const { data: unitLogoData, error: unitLogoError } = await supabase
            .storage
            .from('logos')
            .download(unitLogo);

          if (!unitLogoError && unitLogoData) {
            unitLogoUrl = URL.createObjectURL(unitLogoData);
            console.log('Loaded unit logo from storage successfully');
          } else {
            console.error('Error loading unit logo from storage:', unitLogoError);
          }
        } catch (err) {
          console.error('Exception loading unit logo:', err);
        }
      }
    }

    // تحميل شعار المنظمة إذا كان موجوداً وليس رابط URL
    if (organizationLogo && !organizationLogoUrl) {
      if (organizationLogo.startsWith('http')) {
        organizationLogoUrl = organizationLogo;
        console.log('Using direct URL for organization logo (fallback):', organizationLogo);
      } else {
        try {
          console.log('Loading organization logo from storage:', organizationLogo);
          const { data: orgLogoData, error: orgLogoError } = await supabase
            .storage
            .from('logos')
            .download(organizationLogo);

          if (!orgLogoError && orgLogoData) {
            organizationLogoUrl = URL.createObjectURL(orgLogoData);
            console.log('Loaded organization logo from storage successfully');
          } else {
            console.error('Error loading organization logo from storage:', orgLogoError);
          }
        } catch (err) {
          console.error('Exception loading organization logo:', err);
        }
      }
    }

    // تحميل الختم إذا كان موجوداً وليس رابط URL
    if (stamp && !stampUrl) {
      if (stamp.startsWith('http')) {
        stampUrl = stamp;
        console.log('Using direct URL for stamp (fallback):', stamp);
      } else {
        try {
          console.log('Loading stamp from storage:', stamp);
          const { data: stampData, error: stampError } = await supabase
            .storage
            .from('stamps')
            .download(stamp);

          if (!stampError && stampData) {
            stampUrl = URL.createObjectURL(stampData);
            console.log('Loaded stamp from storage successfully');
          } else {
            console.error('Error loading stamp from storage:', stampError);
          }
        } catch (err) {
          console.error('Exception loading stamp:', err);
        }
      }
    }

    return {
      orgSettings,
      unitLogoUrl,
      organizationLogoUrl,
      rightLogoUrl,
      leftLogoUrl,
      stampUrl
    };
  } catch (error) {
    console.error('Error loading logos and stamp:', error);
    return {
      orgSettings: null,
      unitLogoUrl: '',
      organizationLogoUrl: '',
      rightLogoUrl: '',
      leftLogoUrl: '',
      stampUrl: ''
    };
  }
}
