-- وظيفة لاسترجاع جميع الوحدات التابعة لوحدة معينة (بشكل متدرج)
CREATE OR REPLACE FUNCTION get_subordinate_units(parent_unit_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  type TEXT,
  description TEXT,
  parent_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  level INT
) AS $$
WITH RECURSIVE subordinates AS (
  -- الوحدة الأساسية
  SELECT 
    u.id, 
    u.name, 
    u.type, 
    u.description, 
    u.parent_id, 
    u.created_at, 
    u.updated_at,
    0 AS level
  FROM units u
  WHERE u.id = parent_unit_id
  
  UNION ALL
  
  -- الوحدات التابعة بشكل متدرج
  SELECT 
    u.id, 
    u.name, 
    u.type, 
    u.description, 
    u.parent_id, 
    u.created_at, 
    u.updated_at,
    s.level + 1
  FROM units u
  JOIN subordinates s ON u.parent_id = s.id
)
SELECT * FROM subordinates;
$$ LANGUAGE SQL;

-- وظيفة لاسترجاع جميع الوحدات التابعة لوحدة معينة (بشكل متدرج) مع الوحدة نفسها
CREATE OR REPLACE FUNCTION get_unit_and_subordinates(unit_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  type TEXT,
  description TEXT,
  parent_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  level INT
) AS $$
WITH RECURSIVE subordinates AS (
  -- الوحدة الأساسية
  SELECT 
    u.id, 
    u.name, 
    u.type, 
    u.description, 
    u.parent_id, 
    u.created_at, 
    u.updated_at,
    0 AS level
  FROM units u
  WHERE u.id = unit_id
  
  UNION ALL
  
  -- الوحدات التابعة بشكل متدرج
  SELECT 
    u.id, 
    u.name, 
    u.type, 
    u.description, 
    u.parent_id, 
    u.created_at, 
    u.updated_at,
    s.level + 1
  FROM units u
  JOIN subordinates s ON u.parent_id = s.id
)
SELECT * FROM subordinates;
$$ LANGUAGE SQL;
