-- إنشاء جدول لتخزين العلاقة بين المستخدمين والوحدات المتعددة
CREATE TABLE IF NOT EXISTS user_units (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  unit_id UUID NOT NULL REFERENCES units(id) ON DELETE CASCADE,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, unit_id)
);

-- إضافة فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_user_units_user_id ON user_units(user_id);
CREATE INDEX IF NOT EXISTS idx_user_units_unit_id ON user_units(unit_id);

-- تمكين أمان الصفوف للجدول
ALTER TABLE user_units ENABLE ROW LEVEL SECURITY;

-- إن<PERSON>اء سياسة للمشرفين: يمكنهم رؤية وتعديل جميع السجلات
CREATE POLICY admin_all_user_units ON user_units
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND (profiles.role = 'admin' OR profiles.role = 'مشرف')
    )
  );

-- إنشاء سياسة للمستخدمين: يمكنهم رؤية سجلاتهم فقط
CREATE POLICY user_read_own_user_units ON user_units
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

-- إنشاء وظيفة لإضافة وحدة للمستخدم
CREATE OR REPLACE FUNCTION add_unit_to_user(
  p_user_id UUID,
  p_unit_id UUID,
  p_is_primary BOOLEAN DEFAULT FALSE
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- إذا كانت الوحدة الأساسية، قم بإلغاء تعيين الوحدات الأساسية الأخرى
  IF p_is_primary THEN
    UPDATE user_units
    SET is_primary = FALSE
    WHERE user_id = p_user_id AND is_primary = TRUE;
  END IF;
  
  -- إضافة الوحدة للمستخدم
  INSERT INTO user_units (user_id, unit_id, is_primary)
  VALUES (p_user_id, p_unit_id, p_is_primary)
  ON CONFLICT (user_id, unit_id)
  DO UPDATE SET
    is_primary = p_is_primary,
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$;

-- إنشاء وظيفة لإزالة وحدة من المستخدم
CREATE OR REPLACE FUNCTION remove_unit_from_user(
  p_user_id UUID,
  p_unit_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- إزالة الوحدة من المستخدم
  DELETE FROM user_units
  WHERE user_id = p_user_id AND unit_id = p_unit_id;
  
  RETURN TRUE;
END;
$$;

-- إنشاء وظيفة للحصول على وحدات المستخدم
CREATE OR REPLACE FUNCTION get_user_units(p_user_id UUID)
RETURNS TABLE (
  unit_id UUID,
  is_primary BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT uu.unit_id, uu.is_primary
  FROM user_units uu
  WHERE uu.user_id = p_user_id
  ORDER BY uu.is_primary DESC, uu.created_at;
END;
$$;
