import { supabase } from '$lib/supabase';
import { UnitService } from './unitService';

export type Document = {
  id: string;
  title: string;
  document_number: string | null;
  document_date: string;
  document_type: string;
  sender: string | null;
  receiver: string | null;
  notes: string | null;
  created_by: string;
  unit_id: string | null;
  created_at: string;
  updated_at: string;
  creator?: {
    full_name: string;
  };
  unit?: {
    name: string;
  };
};

export type DocumentAttachment = {
  id: string;
  document_id: string;
  file_name: string;
  file_path: string;
  file_type: string;
  file_size: number;
  created_by: string | null;
  created_at: string;
  file_url?: string;
};

export type DocumentReader = {
  id: string;
  document_id: string;
  user_id: string;
  read_at: string;
  user?: {
    full_name: string;
  };
};

export class DocumentService {
  /**
   * الحصول على جميع المستندات
   * @param userId معرف المستخدم (اختياري) - إذا تم تمريره، سيتم جلب المستندات المسموح للمستخدم بالوصول إليها فقط
   */
  static async getAllDocuments(userId?: string): Promise<Document[]> {
    try {
      // إذا تم تمرير معرف المستخدم، نقوم بالتحقق من وحدته التنظيمية
      if (userId) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('unit_id')
          .eq('id', userId)
          .single();

        // إذا كان المستخدم ينتمي لوحدة تنظيمية، نقوم بجلب مستندات وحدته والوحدات التابعة له
        if (profile && profile.unit_id) {
          return this.getDocumentsByUnitAndSubordinates(profile.unit_id);
        }
      }

      // إذا لم يتم تمرير معرف المستخدم أو المستخدم لا ينتمي لوحدة تنظيمية، نقوم بجلب جميع المستندات
      const { data, error } = await supabase
        .from('documents')
        .select(`
          *,
          creator:created_by(full_name),
          unit:unit_id(name)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching documents:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('Error in getAllDocuments:', err);
      return [];
    }
  }

  /**
   * الحصول على مستند بواسطة المعرف
   * @param id معرف المستند
   */
  static async getDocumentById(id: string): Promise<Document | null> {
    try {
      const { data, error } = await supabase
        .from('documents')
        .select(`
          *,
          creator:created_by(full_name),
          unit:unit_id(name)
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching document:', error);
        return null;
      }

      return data;
    } catch (err) {
      console.error('Error in getDocumentById:', err);
      return null;
    }
  }

  /**
   * الحصول على مستندات وحدة معينة
   * @param unitId معرف الوحدة
   */
  static async getDocumentsByUnitId(unitId: string): Promise<Document[]> {
    try {
      const { data, error } = await supabase
        .from('documents')
        .select(`
          *,
          creator:created_by(full_name),
          unit:unit_id(name)
        `)
        .eq('unit_id', unitId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching documents by unit:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('Error in getDocumentsByUnitId:', err);
      return [];
    }
  }

  /**
   * الحصول على مستندات وحدة معينة والوحدات التابعة لها
   * @param unitId معرف الوحدة
   */
  static async getDocumentsByUnitAndSubordinates(unitId: string): Promise<Document[]> {
    try {
      // الحصول على معرفات الوحدة والوحدات التابعة لها
      const unitIds = await UnitService.getUnitAndSubordinatesIds(unitId);

      if (unitIds.length === 0) {
        return [];
      }

      const { data, error } = await supabase
        .from('documents')
        .select(`
          *,
          creator:created_by(full_name),
          unit:unit_id(name)
        `)
        .in('unit_id', unitIds)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching documents by unit and subordinates:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('Error in getDocumentsByUnitAndSubordinates:', err);
      return [];
    }
  }

  /**
   * إضافة مستند جديد
   * @param document بيانات المستند
   */
  static async addDocument(document: Partial<Document>): Promise<Document | null> {
    try {
      // التحقق من أن المستخدم مسجل وله وحدة تنظيمية
      if (!document.created_by) {
        console.error('User ID is required to add a document');
        return null;
      }

      // الحصول على وحدة المستخدم ودوره
      const { data: profile } = await supabase
        .from('profiles')
        .select('unit_id, role_id')
        .eq('id', document.created_by)
        .single();

      if (!profile || !profile.unit_id) {
        console.error('User does not belong to any unit');
        return null;
      }

      // استخدام وحدة المستخدم للمستند بغض النظر عن الوحدة المرسلة
      const documentToAdd = {
        ...document,
        unit_id: profile.unit_id
      };

      const { data, error } = await supabase
        .from('documents')
        .insert([documentToAdd])
        .select();

      if (error) {
        console.error('Error adding document:', error);
        return null;
      }

      return data?.[0] || null;
    } catch (err) {
      console.error('Error in addDocument:', err);
      return null;
    }
  }

  /**
   * التحقق من صلاحية المستخدم للوصول إلى مستند معين
   * @param userId معرف المستخدم
   * @param documentId معرف المستند
   */
  static async canAccessDocument(userId: string, documentId: string): Promise<boolean> {
    try {
      console.log('DocumentService.canAccessDocument called for user:', userId, 'document:', documentId);

      // الحصول على المستند
      console.log('Fetching document data...');
      const { data: document, error } = await supabase
        .from('documents')
        .select('id, title, unit_id, created_by')
        .eq('id', documentId)
        .single();

      if (error) {
        console.error('Error fetching document:', error);
        return false;
      }

      if (!document) {
        console.error('Document not found with ID:', documentId);
        return false;
      }

      console.log('Document found:', document.id, document.title);
      console.log('Document created by:', document.created_by, 'Unit ID:', document.unit_id);

      // إذا كان المستخدم هو منشئ المستند، فله حق الوصول
      if (document.created_by === userId) {
        console.log('User is the creator of the document - access granted');
        return true;
      }

      // الحصول على وحدة المستخدم ودوره
      console.log('Fetching user profile...');
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('unit_id, role_id')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error('Error fetching user profile:', profileError);
        return false;
      }

      if (!profile) {
        console.error('User profile not found for ID:', userId);
        return false;
      }

      console.log('User profile found - Unit ID:', profile.unit_id, 'Role ID:', profile.role_id);

      // التحقق من دور المستخدم
      let isAdmin = false;
      let isManager = false;
      let roleName = 'unknown';

      if (profile.role_id) {
        console.log('Fetching user role...');
        const { data: role, error: roleError } = await supabase
          .from('roles')
          .select('name')
          .eq('id', profile.role_id)
          .single();

        if (roleError) {
          console.error('Error fetching user role:', roleError);
        } else if (role) {
          roleName = role.name;
          isAdmin = role.name === 'admin' || role.name === 'مشرف';
          isManager = role.name === 'manager' || role.name === 'مدير';
          console.log('User role:', roleName, '- isAdmin:', isAdmin, 'isManager:', isManager);
        }
      }

      // المشرف له حق الوصول إلى جميع المستندات
      if (isAdmin) {
        console.log('User is admin - access granted');
        return true;
      }

      // إذا كان المستند لا ينتمي لأي وحدة، فلا يمكن الوصول إليه
      if (!document.unit_id) {
        console.log('Document does not belong to any unit - access denied');
        return false;
      }

      // إذا كان المستخدم لا ينتمي لأي وحدة، فلا يمكن الوصول إلى المستند
      if (!profile.unit_id) {
        console.log('User does not belong to any unit - access denied');
        return false;
      }

      // التحقق مما إذا كانت وحدة المستند هي نفسها وحدة المستخدم
      if (document.unit_id === profile.unit_id) {
        console.log('Document belongs to user\'s unit - access granted');
        return true;
      }

      // إذا كان المستخدم مديراً، يمكنه الوصول إلى مستندات الوحدات التابعة لوحدته
      if (isManager) {
        console.log('User is manager - checking subordinate units...');
        // التحقق مما إذا كانت وحدة المستند تابعة لوحدة المستخدم
        const subordinateUnits = await UnitService.getAllSubordinateUnits(profile.unit_id);
        console.log('Found', subordinateUnits.length, 'subordinate units');

        const hasAccess = subordinateUnits.some(unit => unit.id === document.unit_id);
        if (hasAccess) {
          console.log('Document belongs to a subordinate unit - access granted');
        } else {
          console.log('Document does not belong to a subordinate unit - access denied');
        }
        return hasAccess;
      }

      // المستخدم العادي لا يمكنه الوصول إلى مستندات وحدات أخرى
      console.log('Regular user cannot access documents from other units - access denied');
      return false;
    } catch (err) {
      console.error('Exception in canAccessDocument:', err);
      return false;
    }
  }
}
