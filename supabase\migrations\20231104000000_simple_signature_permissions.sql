-- إنشاء جدول صلاحيات التوقيع بطريقة مبسطة
CREATE TABLE IF NOT EXISTS signature_permissions (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  can_sign BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  UNIQUE(user_id)
);

-- إضافة فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_signature_permissions_user_id ON signature_permissions(user_id);

-- تمكين أمان الصفوف للجدول
ALTER TABLE signature_permissions ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسة للمشرفين: يمكنهم رؤية وتعديل جميع السجلات
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'signature_permissions' 
    AND policyname = 'admin_all_signature_permissions'
  ) THEN
    CREATE POLICY admin_all_signature_permissions ON signature_permissions
      FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid() AND (profiles.role = 'admin' OR profiles.role = 'مشرف')
        )
      );
  END IF;
END
$$;

-- إنشاء سياسة للمستخدمين: يمكنهم رؤية سجلاتهم فقط
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'signature_permissions' 
    AND policyname = 'user_read_own_signature_permissions'
  ) THEN
    CREATE POLICY user_read_own_signature_permissions ON signature_permissions
      FOR SELECT
      TO authenticated
      USING (user_id = auth.uid());
  END IF;
END
$$;
