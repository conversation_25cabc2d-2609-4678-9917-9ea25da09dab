-- إنشاء وظيفة لإنشاء جدول صلاحيات التوقيع إذا لم يكن موجوداً
CREATE OR REPLACE FUNCTION create_signature_permissions_table_if_not_exists()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- التحقق من وجود الجدول
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'signature_permissions'
  ) THEN
    -- إنشاء الجدول
    CREATE TABLE signature_permissions (
      id SERIAL PRIMARY KEY,
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      can_sign BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      created_by UUID REFERENCES auth.users(id),
      UNIQUE(user_id)
    );
    
    -- إن<PERSON><PERSON><PERSON> فهرس
    CREATE INDEX idx_signature_permissions_user_id ON signature_permissions(user_id);
    
    -- تمكين أمان الصفوف
    ALTER TABLE signature_permissions ENABLE ROW LEVEL SECURITY;
    
    -- إنشاء سياسات الأمان
    CREATE POLICY admin_all_signature_permissions ON signature_permissions
      FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid() AND (profiles.role = 'admin' OR profiles.role = 'مشرف')
        )
      );
    
    CREATE POLICY user_read_own_signature_permissions ON signature_permissions
      FOR SELECT
      TO authenticated
      USING (user_id = auth.uid());
  END IF;
  
  RETURN TRUE;
END;
$$;

-- إنشاء وظيفة لتنفيذ أوامر SQL (للمشرفين فقط)
CREATE OR REPLACE FUNCTION execute_sql(sql TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_admin BOOLEAN;
BEGIN
  -- التحقق من صلاحية المستخدم
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (role = 'admin' OR role = 'مشرف')
  ) INTO v_is_admin;
  
  -- إذا لم يكن المستخدم مشرفاً، فلا يمكنه تنفيذ أوامر SQL
  IF NOT v_is_admin THEN
    RAISE EXCEPTION 'ليس لديك صلاحية لتنفيذ أوامر SQL';
  END IF;
  
  -- تنفيذ الأمر SQL
  EXECUTE sql;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'حدث خطأ أثناء تنفيذ الأمر SQL: %', SQLERRM;
END;
$$;
