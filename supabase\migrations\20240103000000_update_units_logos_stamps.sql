-- تحديث جدول الوحدات التنظيمية لدعم الشعارات والأختام
ALTER TABLE public.units
ADD COLUMN IF NOT EXISTS logo TEXT,
ADD COLUMN IF NOT EXISTS stamp TEXT,
ADD COLUMN IF NOT EXISTS right_logo TEXT,
ADD COLUMN IF NOT EXISTS left_logo TEXT;

-- إنشاء سياسات الوصول لجدول الوحدات التنظيمية
CREATE POLICY IF NOT EXISTS "Allow admins to update units"
  ON public.units
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- إنشاء وظيفة للحصول على شعارات وأختام الوحدة
CREATE OR REPLACE FUNCTION public.get_unit_logos_and_stamps(unit_id UUID)
RETURNS TABLE (
  unit_name TEXT,
  logo TEXT,
  stamp TEXT,
  right_logo TEXT,
  left_logo TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    units.name AS unit_name,
    units.logo,
    units.stamp,
    COALESCE(units.right_logo, org.right_logo) AS right_logo,
    COALESCE(units.left_logo, org.left_logo) AS left_logo
  FROM 
    public.units
  LEFT JOIN 
    public.organization_settings AS org ON TRUE
  WHERE 
    units.id = unit_id;
END;
$$;
