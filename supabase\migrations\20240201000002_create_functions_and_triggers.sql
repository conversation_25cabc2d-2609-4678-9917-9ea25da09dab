-- إنشاء الوظائف والمشغلات

-- وظيفة لتوليد الرقم الإشاري
CREATE OR REPLACE FUNCTION public.generate_reference_number(
  p_document_id UUID,
  p_unit_id UUID
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
  v_year TEXT;
  v_month TEXT;
  v_day TEXT;
  v_unit_prefix TEXT;
  v_counter INT;
  v_reference TEXT;
BEGIN
  -- الحصول على التاريخ الحالي
  v_year := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
  v_month := LPAD(EXTRACT(MONTH FROM CURRENT_DATE)::TEXT, 2, '0');
  v_day := LPAD(EXTRACT(DAY FROM CURRENT_DATE)::TEXT, 2, '0');
  
  -- الحصول على بادئة الوحدة
  SELECT reference_prefix INTO v_unit_prefix
  FROM public.units
  WHERE id = p_unit_id;
  
  IF v_unit_prefix IS NULL THEN
    v_unit_prefix := 'DOC';
  END IF;
  
  -- الحصول على العداد
  SELECT COUNT(*) + 1 INTO v_counter
  FROM public.official_documents
  WHERE EXTRACT(YEAR FROM created_at) = EXTRACT(YEAR FROM CURRENT_DATE)
  AND unit_id = p_unit_id;
  
  -- تكوين الرقم الإشاري
  v_reference := v_unit_prefix || '/' || v_year || '/' || v_month || '/' || LPAD(v_counter::TEXT, 4, '0');
  
  RETURN v_reference;
END;
$$;

-- وظيفة لتحديث الرقم الإشاري تلقائياً
CREATE OR REPLACE FUNCTION public.update_reference_number()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- تحديث الرقم الإشاري فقط إذا كان فارغاً
  IF NEW.reference_number IS NULL OR NEW.reference_number = '' THEN
    NEW.reference_number := public.generate_reference_number(NEW.id, NEW.unit_id);
  END IF;
  
  RETURN NEW;
END;
$$;

-- مشغل لتحديث الرقم الإشاري تلقائياً
DROP TRIGGER IF EXISTS update_reference_number_trigger ON public.official_documents;
CREATE TRIGGER update_reference_number_trigger
BEFORE INSERT OR UPDATE ON public.official_documents
FOR EACH ROW
WHEN (NEW.status = 'approved')
EXECUTE FUNCTION public.update_reference_number();

-- وظيفة لتسجيل تاريخ الوثيقة
CREATE OR REPLACE FUNCTION public.log_document_history()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  v_action TEXT;
  v_details JSONB;
BEGIN
  -- تحديد نوع الإجراء
  IF TG_OP = 'INSERT' THEN
    v_action := 'create';
  ELSIF TG_OP = 'UPDATE' THEN
    IF OLD.status != NEW.status THEN
      v_action := 'status_change';
      v_details := jsonb_build_object(
        'old_status', OLD.status,
        'new_status', NEW.status
      );
    ELSE
      v_action := 'update';
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    v_action := 'delete';
  END IF;
  
  -- تسجيل الإجراء في تاريخ الوثيقة
  INSERT INTO public.document_history (
    document_id,
    user_id,
    action,
    status,
    details
  ) VALUES (
    NEW.id,
    auth.uid(),
    v_action,
    NEW.status,
    v_details
  );
  
  -- تحديث وقت الاعتماد إذا تم اعتماد الوثيقة
  IF NEW.status = 'approved' AND (OLD.status IS NULL OR OLD.status != 'approved') THEN
    NEW.approved_at := NOW();
  END IF;
  
  RETURN NEW;
END;
$$;

-- مشغل لتسجيل تاريخ الوثيقة
DROP TRIGGER IF EXISTS log_document_history_trigger ON public.official_documents;
CREATE TRIGGER log_document_history_trigger
AFTER INSERT OR UPDATE ON public.official_documents
FOR EACH ROW
EXECUTE FUNCTION public.log_document_history();

-- وظيفة لتحديث وقت التعديل تلقائياً
CREATE OR REPLACE FUNCTION public.update_modified_timestamp()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- مشغل لتحديث وقت التعديل تلقائياً للوثائق
DROP TRIGGER IF EXISTS update_official_documents_timestamp ON public.official_documents;
CREATE TRIGGER update_official_documents_timestamp
BEFORE UPDATE ON public.official_documents
FOR EACH ROW
EXECUTE FUNCTION public.update_modified_timestamp();

-- مشغل لتحديث وقت التعديل تلقائياً للتعليقات
DROP TRIGGER IF EXISTS update_document_comments_timestamp ON public.document_comments;
CREATE TRIGGER update_document_comments_timestamp
BEFORE UPDATE ON public.document_comments
FOR EACH ROW
EXECUTE FUNCTION public.update_modified_timestamp();

-- مشغل لتحديث وقت التعديل تلقائياً لقوالب الوثائق
DROP TRIGGER IF EXISTS update_document_templates_timestamp ON public.document_templates;
CREATE TRIGGER update_document_templates_timestamp
BEFORE UPDATE ON public.document_templates
FOR EACH ROW
EXECUTE FUNCTION public.update_modified_timestamp();

-- وظيفة للتحقق من صلاحية المستخدم لإنشاء وثيقة رسمية
CREATE OR REPLACE FUNCTION public.can_create_official_document(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE id = user_id
    AND (
      role = 'admin'
      OR EXISTS (
        SELECT 1
        FROM public.user_permissions
        WHERE user_id = profiles.id
        AND permission = 'create_official_document'
      )
    )
  );
END;
$$;
