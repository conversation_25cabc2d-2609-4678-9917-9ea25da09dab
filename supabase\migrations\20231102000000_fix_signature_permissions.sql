-- التحقق من وجود جدول صلاحيات التوقيع وإنشائه إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS signature_permissions (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  can_sign BO<PERSON><PERSON><PERSON> DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  UNIQUE(user_id)
);

-- إضافة فهرس للبحث السريع إذا لم يكن موجوداً
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes WHERE indexname = 'idx_signature_permissions_user_id'
  ) THEN
    CREATE INDEX idx_signature_permissions_user_id ON signature_permissions(user_id);
  END IF;
END
$$;

-- إنشاء أو تحديث وظيفة التحقق من صلاحية المستخدم للتوقيع
CREATE OR REPLACE FUNCTION check_user_can_sign(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_can_sign BOOLEAN;
  v_is_admin BOOLEAN;
BEGIN
  -- التحقق مما إذا كان المستخدم مشرفاً
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_user_id AND (role = 'admin' OR role = 'مشرف')
  ) INTO v_is_admin;
  
  -- إذا كان المستخدم مشرفاً، فلديه صلاحية التوقيع تلقائياً
  IF v_is_admin THEN
    RETURN TRUE;
  END IF;
  
  -- التحقق من صلاحية التوقيع في جدول الصلاحيات
  SELECT can_sign INTO v_can_sign
  FROM signature_permissions
  WHERE user_id = p_user_id;
  
  -- إرجاع النتيجة (false إذا لم يكن هناك سجل)
  RETURN COALESCE(v_can_sign, FALSE);
END;
$$;

-- إنشاء أو تحديث وظيفة منح صلاحية التوقيع
CREATE OR REPLACE FUNCTION grant_signature_permission(
  p_admin_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_admin BOOLEAN;
BEGIN
  -- التحقق من صلاحية المستخدم المشرف
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_admin_id AND (role = 'admin' OR role = 'مشرف')
  ) INTO v_is_admin;
  
  -- إذا لم يكن المستخدم مشرفاً، فلا يمكنه منح الصلاحية
  IF NOT v_is_admin THEN
    RETURN FALSE;
  END IF;
  
  -- إضافة أو تحديث صلاحية التوقيع
  INSERT INTO signature_permissions (user_id, can_sign, created_by)
  VALUES (p_user_id, TRUE, p_admin_id)
  ON CONFLICT (user_id)
  DO UPDATE SET
    can_sign = TRUE,
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$;

-- إنشاء أو تحديث وظيفة إلغاء صلاحية التوقيع
CREATE OR REPLACE FUNCTION revoke_signature_permission(
  p_admin_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_admin BOOLEAN;
BEGIN
  -- التحقق من صلاحية المستخدم المشرف
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_admin_id AND (role = 'admin' OR role = 'مشرف')
  ) INTO v_is_admin;
  
  -- إذا لم يكن المستخدم مشرفاً، فلا يمكنه إلغاء الصلاحية
  IF NOT v_is_admin THEN
    RETURN FALSE;
  END IF;
  
  -- إضافة أو تحديث صلاحية التوقيع
  INSERT INTO signature_permissions (user_id, can_sign, created_by)
  VALUES (p_user_id, FALSE, p_admin_id)
  ON CONFLICT (user_id)
  DO UPDATE SET
    can_sign = FALSE,
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$;

-- إنشاء سياسات أمان للجدول إذا لم تكن موجودة
DO $$
BEGIN
  -- تمكين أمان الصفوف للجدول
  IF NOT EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename = 'signature_permissions' 
    AND rowsecurity = true
  ) THEN
    ALTER TABLE signature_permissions ENABLE ROW LEVEL SECURITY;
  END IF;

  -- إنشاء سياسة للمشرفين إذا لم تكن موجودة
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'signature_permissions' 
    AND policyname = 'admin_all_signature_permissions'
  ) THEN
    CREATE POLICY admin_all_signature_permissions ON signature_permissions
      FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid() AND (profiles.role = 'admin' OR profiles.role = 'مشرف')
        )
      );
  END IF;

  -- إنشاء سياسة للمستخدمين إذا لم تكن موجودة
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'signature_permissions' 
    AND policyname = 'user_read_own_signature_permissions'
  ) THEN
    CREATE POLICY user_read_own_signature_permissions ON signature_permissions
      FOR SELECT
      TO authenticated
      USING (user_id = auth.uid());
  END IF;
END
$$;
