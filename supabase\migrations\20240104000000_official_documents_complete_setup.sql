-- إن<PERSON>ا<PERSON> امتدادات مطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- إن<PERSON>اء أنواع البيانات المخصصة
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('admin', 'manager', 'user');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'unit_type') THEN
        CREATE TYPE unit_type AS ENUM ('إدارة', 'فرع', 'مكتب', 'قسم');
    END IF;
END$$;

-- إنشاء جدول المستخدمين (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    role user_role DEFAULT 'user'::user_role,
    unit_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الوحدات التنظيمية
CREATE TABLE IF NOT EXISTS public.units (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    type unit_type DEFAULT 'إدارة'::unit_type,
    description TEXT,
    parent_id UUID REFERENCES public.units(id) ON DELETE SET NULL,
    logo TEXT,
    stamp TEXT,
    right_logo TEXT,
    left_logo TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة مفتاح أجنبي لربط المستخدمين بالوحدات
ALTER TABLE public.profiles
ADD CONSTRAINT fk_profiles_unit
FOREIGN KEY (unit_id) REFERENCES public.units(id) ON DELETE SET NULL;

-- إنشاء جدول إعدادات المنظمة
CREATE TABLE IF NOT EXISTS public.organization_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL DEFAULT 'دولة ليبيا',
    logo TEXT,
    right_logo TEXT,
    left_logo TEXT,
    default_stamp TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الرسائل
CREATE TABLE IF NOT EXISTS public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    receiver_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    receiver_unit_id UUID REFERENCES public.units(id) ON DELETE SET NULL,
    parent_id UUID REFERENCES public.messages(id) ON DELETE SET NULL,
    is_read BOOLEAN DEFAULT FALSE,
    status TEXT DEFAULT 'sent',
    is_signed BOOLEAN DEFAULT FALSE,
    signature JSONB,
    is_official BOOLEAN DEFAULT FALSE,
    unit_name TEXT,
    unit_logo TEXT,
    organization_logo TEXT,
    stamp TEXT,
    reference_number TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الصلاحيات
CREATE TABLE IF NOT EXISTS public.permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول صلاحيات المستخدمين
CREATE TABLE IF NOT EXISTS public.user_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES public.permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (user_id, permission_id)
);

-- إنشاء مخزن للشعارات
INSERT INTO storage.buckets (id, name, public)
VALUES ('logos', 'Logos', true)
ON CONFLICT (id) DO NOTHING;

-- إنشاء مخزن للأختام
INSERT INTO storage.buckets (id, name, public)
VALUES ('stamps', 'Stamps', true)
ON CONFLICT (id) DO NOTHING;

-- إضافة سياسات الوصول للشعارات
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Logos Access Policy',
  '(bucket_id = ''logos''::text)',
  'logos'
)
ON CONFLICT (name, bucket_id) DO NOTHING;

-- إضافة سياسات الوصول للأختام
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Stamps Access Policy',
  '(bucket_id = ''stamps''::text)',
  'stamps'
)
ON CONFLICT (name, bucket_id) DO NOTHING;

-- إنشاء سجل افتراضي لإعدادات المنظمة
INSERT INTO public.organization_settings (name)
VALUES ('دولة ليبيا')
ON CONFLICT DO NOTHING;

-- إنشاء وظيفة لتوليد الرقم الإشاري
CREATE OR REPLACE FUNCTION public.generate_reference_number(signature_id TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
  short_id TEXT;
  today DATE;
  year_str TEXT;
  month_str TEXT;
  day_str TEXT;
BEGIN
  -- استخدام جزء من معرف التوقيع
  short_id := SUBSTRING(signature_id, 1, 8);
  
  -- الحصول على التاريخ الحالي
  today := CURRENT_DATE;
  
  -- تنسيق التاريخ
  year_str := EXTRACT(YEAR FROM today)::TEXT;
  month_str := LPAD(EXTRACT(MONTH FROM today)::TEXT, 2, '0');
  day_str := LPAD(EXTRACT(DAY FROM today)::TEXT, 2, '0');
  
  -- إرجاع الرقم الإشاري بالتنسيق المطلوب
  RETURN year_str || '/' || month_str || '/' || day_str || '/' || short_id;
END;
$$;

-- إنشاء وظيفة للتحقق من وجود عمود في جدول
CREATE OR REPLACE FUNCTION public.check_column_exists(
  table_name TEXT,
  column_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  column_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = check_column_exists.table_name
    AND column_name = check_column_exists.column_name
  ) INTO column_exists;
  
  RETURN column_exists;
END;
$$;

-- إنشاء وظيفة لإضافة عمود parent_id إذا لم يكن موجوداً
CREATE OR REPLACE FUNCTION public.add_parent_id_column_if_not_exists()
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'parent_id'
  ) THEN
    ALTER TABLE public.messages
    ADD COLUMN parent_id UUID REFERENCES public.messages(id) ON DELETE SET NULL;
  END IF;
END;
$$;

-- إنشاء وظيفة للتحقق من صلاحيات المدير
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  );
END;
$$;

-- إنشاء وظيفة للحصول على شعارات وأختام الوحدة
CREATE OR REPLACE FUNCTION public.get_unit_logos_and_stamps(unit_id UUID)
RETURNS TABLE (
  unit_name TEXT,
  logo TEXT,
  stamp TEXT,
  right_logo TEXT,
  left_logo TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    units.name AS unit_name,
    units.logo,
    units.stamp,
    COALESCE(units.right_logo, org.right_logo) AS right_logo,
    COALESCE(units.left_logo, org.left_logo) AS left_logo
  FROM 
    public.units
  LEFT JOIN 
    public.organization_settings AS org ON TRUE
  WHERE 
    units.id = unit_id;
END;
$$;

-- إنشاء وظيفة للتحقق من صلاحية التوقيع
CREATE OR REPLACE FUNCTION public.check_signature_permission(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_permissions up
    JOIN public.permissions p ON up.permission_id = p.id
    WHERE up.user_id = check_signature_permission.user_id
    AND p.name = 'create_signature'
  );
END;
$$;

-- إنشاء سياسات الوصول لجدول الرسائل
CREATE POLICY IF NOT EXISTS "Users can view their own messages"
  ON public.messages
  FOR SELECT
  TO authenticated
  USING (
    sender_id = auth.uid() OR 
    recipient_id = auth.uid() OR 
    receiver_id = auth.uid() OR
    receiver_unit_id IN (
      SELECT unit_id FROM public.profiles WHERE id = auth.uid()
    )
  );

CREATE POLICY IF NOT EXISTS "Users can insert their own messages"
  ON public.messages
  FOR INSERT
  TO authenticated
  WITH CHECK (sender_id = auth.uid());

CREATE POLICY IF NOT EXISTS "Users can update their own messages"
  ON public.messages
  FOR UPDATE
  TO authenticated
  USING (sender_id = auth.uid());

-- إنشاء سياسات الوصول لجدول الوحدات التنظيمية
CREATE POLICY IF NOT EXISTS "Allow admins to update units"
  ON public.units
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- إنشاء سياسات الوصول لجدول إعدادات المنظمة
CREATE POLICY IF NOT EXISTS "Allow admins to update organization settings"
  ON public.organization_settings
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- إضافة صلاحية التوقيع الإلكتروني
INSERT INTO public.permissions (name, description)
VALUES ('create_signature', 'صلاحية إنشاء توقيع إلكتروني')
ON CONFLICT (name) DO NOTHING;

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON public.messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_recipient_id ON public.messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_messages_receiver_id ON public.messages(receiver_id);
CREATE INDEX IF NOT EXISTS idx_messages_receiver_unit_id ON public.messages(receiver_unit_id);
CREATE INDEX IF NOT EXISTS idx_messages_parent_id ON public.messages(parent_id);
CREATE INDEX IF NOT EXISTS idx_profiles_unit_id ON public.profiles(unit_id);
CREATE INDEX IF NOT EXISTS idx_units_parent_id ON public.units(parent_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON public.user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_permission_id ON public.user_permissions(permission_id);
