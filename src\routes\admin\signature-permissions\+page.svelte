<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { PermissionService } from '$lib/services/permissionService';
  import { RoleType } from '$lib/types/permissions';

  // تعريف أنواع البيانات
  interface User {
    id: string;
    full_name: string;
    email: string;
    role: string;
    unit_id: string | null;
    unit_name?: string;
    can_sign?: boolean;
  }

  // متغيرات الحالة
  let users: User[] = [];
  let loading = true;
  let error: string | null = null;
  let currentUser: any = null;
  let isAdmin = false;
  let searchQuery = '';
  let filteredUsers: User[] = [];
  let savingPermissions: Record<string, boolean> = {};

  // تحميل البيانات عند تحميل الصفحة
  onMount(async () => {
    try {
      // التحقق من المستخدم الحالي وصلاحياته
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        goto('/login');
        return;
      }

      currentUser = user;

      // التحقق من صلاحيات المستخدم
      isAdmin = await PermissionService.checkRole(user.id, RoleType.ADMIN);

      if (!isAdmin) {
        goto('/dashboard');
        return;
      }

      await loadUsers();
    } catch (err) {
      console.error('Error in onMount:', err);
      error = 'حدث خطأ أثناء تحميل البيانات';
    } finally {
      loading = false;
    }
  });

  // تحميل قائمة المستخدمين مع صلاحيات التوقيع
  async function loadUsers() {
    try {
      loading = true;
      error = null;

      // جلب جميع المستخدمين
      const { data: usersData, error: usersError } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          role,
          unit_id,
          units:unit_id (name)
        `)
        .order('full_name');

      if (usersError) {
        throw usersError;
      }

      // جلب صلاحيات التوقيع
      const { data: permissionsData, error: permissionsError } = await supabase
        .from('signature_permissions')
        .select('user_id, can_sign');

      if (permissionsError) {
        throw permissionsError;
      }

      // دمج البيانات
      const permissionsMap = new Map();
      permissionsData?.forEach(permission => {
        permissionsMap.set(permission.user_id, permission.can_sign);
      });

      users = usersData?.map(user => ({
        id: user.id,
        full_name: user.full_name || 'بدون اسم',
        email: user.email || 'بدون بريد إلكتروني',
        role: user.role || 'مستخدم',
        unit_id: user.unit_id,
        unit_name: user.units ? user.units.name : 'غير محدد',
        can_sign: permissionsMap.get(user.id) || false
      })) || [];

      // تطبيق البحث الأولي
      applySearch();
    } catch (err: any) {
      console.error('Error loading users:', err);
      error = err.message || 'حدث خطأ أثناء تحميل بيانات المستخدمين';
    } finally {
      loading = false;
    }
  }

  // تطبيق البحث على قائمة المستخدمين
  function applySearch() {
    if (!searchQuery.trim()) {
      filteredUsers = [...users];
      return;
    }

    const query = searchQuery.toLowerCase();
    filteredUsers = users.filter(user =>
      user.full_name.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query) ||
      user.role.toLowerCase().includes(query) ||
      user.unit_name?.toLowerCase().includes(query)
    );
  }

  // تحديث البحث عند تغيير قيمة البحث
  $: {
    if (users.length > 0) {
      applySearch();
    }
  }

  // إنشاء جدول صلاحيات التوقيع بطريقة مبسطة
  async function createSignaturePermissionsTable() {
    if (!currentUser || !isAdmin) {
      alert('ليس لديك صلاحية لإنشاء جدول صلاحيات التوقيع');
      return;
    }

    try {
      loading = true;

      // SQL لإنشاء الجدول
      const createTableSQL = `
        -- إنشاء جدول صلاحيات التوقيع بطريقة مبسطة
        CREATE TABLE IF NOT EXISTS signature_permissions (
          id SERIAL PRIMARY KEY,
          user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          can_sign BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by UUID REFERENCES auth.users(id),
          UNIQUE(user_id)
        );

        -- إضافة فهرس للبحث السريع
        CREATE INDEX IF NOT EXISTS idx_signature_permissions_user_id ON signature_permissions(user_id);

        -- تمكين أمان الصفوف للجدول
        ALTER TABLE signature_permissions ENABLE ROW LEVEL SECURITY;

        -- إنشاء سياسة للمشرفين
        DROP POLICY IF EXISTS admin_all_signature_permissions ON signature_permissions;
        CREATE POLICY admin_all_signature_permissions ON signature_permissions
          FOR ALL
          TO authenticated
          USING (
            EXISTS (
              SELECT 1 FROM profiles
              WHERE profiles.id = auth.uid() AND (profiles.role = 'admin' OR profiles.role = 'مشرف')
            )
          );

        -- إنشاء سياسة للمستخدمين
        DROP POLICY IF EXISTS user_read_own_signature_permissions ON signature_permissions;
        CREATE POLICY user_read_own_signature_permissions ON signature_permissions
          FOR SELECT
          TO authenticated
          USING (user_id = auth.uid());
      `;

      // تنفيذ SQL مباشرة
      try {
        const { error } = await supabase.rpc('execute_sql', { sql: createTableSQL });
        if (error) {
          throw error;
        }
      } catch (sqlError) {
        console.error('Failed to create table using RPC:', sqlError);

        // محاولة إنشاء الجدول بطريقة أخرى
        try {
          // التحقق من وجود الجدول
          await supabase.from('signature_permissions').select('id').limit(1);

          // إذا وصلنا إلى هنا، فالجدول موجود بالفعل
          console.log('Table already exists');
        } catch (tableError) {
          console.error('Table does not exist and could not be created:', tableError);
          throw new Error('فشل في إنشاء جدول صلاحيات التوقيع');
        }
      }

      // إعادة تحميل البيانات
      await loadUsers();

      alert('تم إنشاء جدول صلاحيات التوقيع بنجاح');
    } catch (error: any) {
      console.error('Error creating signature permissions table:', error);
      alert('حدث خطأ أثناء إنشاء جدول صلاحيات التوقيع: ' + (error.message || 'خطأ غير معروف'));
    } finally {
      loading = false;
    }
  }

  // تطبيق التغييرات على قاعدة البيانات
  async function applyDatabaseMigration() {
    if (!currentUser || !isAdmin) {
      alert('ليس لديك صلاحية لتطبيق التغييرات على قاعدة البيانات');
      return;
    }

    try {
      loading = true;

      // 1. التأكد من وجود جدول صلاحيات التوقيع
      await createSignaturePermissionsTable();

      // 2. إعادة إنشاء وظائف قاعدة البيانات
      const sqlFunctions = [
        `
        CREATE OR REPLACE FUNCTION check_user_can_sign(p_user_id UUID)
        RETURNS BOOLEAN
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
          v_can_sign BOOLEAN;
          v_is_admin BOOLEAN;
        BEGIN
          -- التحقق مما إذا كان المستخدم مشرفاً
          SELECT EXISTS (
            SELECT 1 FROM profiles
            WHERE id = p_user_id AND (role = 'admin' OR role = 'مشرف')
          ) INTO v_is_admin;

          -- إذا كان المستخدم مشرفاً، فلديه صلاحية التوقيع تلقائياً
          IF v_is_admin THEN
            RETURN TRUE;
          END IF;

          -- التحقق من صلاحية التوقيع في جدول الصلاحيات
          SELECT can_sign INTO v_can_sign
          FROM signature_permissions
          WHERE user_id = p_user_id;

          -- إرجاع النتيجة (false إذا لم يكن هناك سجل)
          RETURN COALESCE(v_can_sign, FALSE);
        END;
        $$;
        `,
        `
        CREATE OR REPLACE FUNCTION grant_signature_permission(
          p_admin_id UUID,
          p_user_id UUID
        )
        RETURNS BOOLEAN
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
          v_is_admin BOOLEAN;
        BEGIN
          -- التحقق من صلاحية المستخدم المشرف
          SELECT EXISTS (
            SELECT 1 FROM profiles
            WHERE id = p_admin_id AND (role = 'admin' OR role = 'مشرف')
          ) INTO v_is_admin;

          -- إذا لم يكن المستخدم مشرفاً، فلا يمكنه منح الصلاحية
          IF NOT v_is_admin THEN
            RETURN FALSE;
          END IF;

          -- إضافة أو تحديث صلاحية التوقيع
          INSERT INTO signature_permissions (user_id, can_sign, created_by)
          VALUES (p_user_id, TRUE, p_admin_id)
          ON CONFLICT (user_id)
          DO UPDATE SET
            can_sign = TRUE,
            updated_at = NOW();

          RETURN TRUE;
        END;
        $$;
        `,
        `
        CREATE OR REPLACE FUNCTION revoke_signature_permission(
          p_admin_id UUID,
          p_user_id UUID
        )
        RETURNS BOOLEAN
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
          v_is_admin BOOLEAN;
        BEGIN
          -- التحقق من صلاحية المستخدم المشرف
          SELECT EXISTS (
            SELECT 1 FROM profiles
            WHERE id = p_admin_id AND (role = 'admin' OR role = 'مشرف')
          ) INTO v_is_admin;

          -- إذا لم يكن المستخدم مشرفاً، فلا يمكنه إلغاء الصلاحية
          IF NOT v_is_admin THEN
            RETURN FALSE;
          END IF;

          -- إضافة أو تحديث صلاحية التوقيع
          INSERT INTO signature_permissions (user_id, can_sign, created_by)
          VALUES (p_user_id, FALSE, p_admin_id)
          ON CONFLICT (user_id)
          DO UPDATE SET
            can_sign = FALSE,
            updated_at = NOW();

          RETURN TRUE;
        END;
        $$;
        `
      ];

      // تنفيذ الوظائف SQL
      for (const sql of sqlFunctions) {
        try {
          await supabase.rpc('execute_sql', { sql });
        } catch (sqlError) {
          console.warn('Failed to execute SQL function:', sqlError);
        }
      }

      // 3. إعادة تحميل البيانات
      await loadUsers();

      alert('تم تطبيق التغييرات على قاعدة البيانات بنجاح');
    } catch (error: any) {
      console.error('Error applying database migration:', error);
      alert('حدث خطأ أثناء تطبيق التغييرات على قاعدة البيانات: ' + (error.message || 'خطأ غير معروف'));
    } finally {
      loading = false;
    }
  }

  // تغيير صلاحية التوقيع للمستخدم - طريقة مبسطة ومباشرة
  async function toggleSignPermission(userId: string, canSign: boolean) {
    if (!currentUser || !isAdmin) return;

    try {
      savingPermissions[userId] = true;

      console.log(`Attempting to ${canSign ? 'grant' : 'revoke'} signature permission for user ${userId}`);

      // 1. التحقق من وجود الجدول
      try {
        // محاولة إنشاء الجدول إذا لم يكن موجوداً
        await supabase.rpc('create_signature_permissions_table_if_not_exists').maybeSingle();
      } catch (tableErr) {
        console.warn('Failed to check/create table:', tableErr);

        // محاولة إنشاء الجدول مباشرة
        try {
          await supabase.from('signature_permissions').select('id').limit(1);
        } catch (directTableErr) {
          console.error('Table does not exist and could not be created:', directTableErr);
          throw new Error('فشل في التحقق من وجود جدول صلاحيات التوقيع');
        }
      }

      // 2. تحديث صلاحية التوقيع مباشرة في قاعدة البيانات
      const { data, error } = await supabase
        .from('signature_permissions')
        .upsert({
          user_id: userId,
          can_sign: canSign,
          created_by: currentUser.id,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        console.error('Failed to update signature permission:', error);
        throw error;
      }

      console.log('Signature permission updated successfully:', data);

      // 3. تحديث واجهة المستخدم
      // تحديث حالة المستخدم في القائمة
      users = users.map(user =>
        user.id === userId ? { ...user, can_sign: canSign } : user
      );

      // تحديث القائمة المفلترة أيضاً
      applySearch();

      // 4. إظهار تأكيد مرئي
      // إضافة تأكيد مرئي بدلاً من alert
      const userElement = document.querySelector(`[data-user-id="${userId}"]`);
      if (userElement) {
        const confirmationContainer = userElement.querySelector('.permission-confirmation-container');
        if (confirmationContainer) {
          // إزالة أي تأكيدات سابقة
          confirmationContainer.innerHTML = '';

          // إنشاء عنصر التأكيد
          const confirmationElement = document.createElement('div');
          confirmationElement.className = `text-sm ${canSign ? 'text-green-600' : 'text-red-600'} font-medium mt-2 transition-opacity duration-1000`;
          confirmationElement.textContent = `✓ تم ${canSign ? 'منح' : 'إلغاء'} الصلاحية بنجاح`;

          confirmationContainer.appendChild(confirmationElement);

          // إخفاء التأكيد بعد 3 ثوان
          setTimeout(() => {
            confirmationElement.style.opacity = '0';
            setTimeout(() => {
              if (confirmationContainer.contains(confirmationElement)) {
                confirmationContainer.removeChild(confirmationElement);
              }
            }, 1000);
          }, 3000);
        }
      }
    } catch (err: any) {
      console.error(`Error ${canSign ? 'granting' : 'revoking'} signature permission:`, err);
      alert(`حدث خطأ أثناء ${canSign ? 'منح' : 'إلغاء'} صلاحية التوقيع: ${err.message || 'خطأ غير معروف'}`);

      // إعادة تحميل البيانات في حالة الخطأ
      await loadUsers();
    } finally {
      savingPermissions[userId] = false;
    }
  }
</script>

<svelte:head>
  <title>إدارة صلاحيات التوقيع الإلكتروني</title>
</svelte:head>

<div class="container mx-auto p-4 rtl" dir="rtl">
  <div class="mb-6 flex justify-between items-center">
    <h1 class="text-2xl font-bold">إدارة صلاحيات التوقيع الإلكتروني</h1>

    <div class="flex gap-2">
      <button
        on:click={createSignaturePermissionsTable}
        class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 bg-green-600 text-white hover:bg-green-700 h-10 px-4 py-2 ml-2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
        إنشاء جدول صلاحيات التوقيع
      </button>

      <button
        on:click={applyDatabaseMigration}
        class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="m9 12 2 2 4-4"></path></svg>
        تطبيق التغييرات على قاعدة البيانات
      </button>

      <a
        href="/admin"
        class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="m15 18-6-6 6-6"/></svg>
        العودة إلى لوحة التحكم
      </a>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-6 border-b">
      <p class="text-gray-600 mb-4">
        يمكنك من خلال هذه الصفحة إدارة صلاحيات التوقيع الإلكتروني للمستخدمين. المستخدمون الذين لديهم صلاحية التوقيع يمكنهم إنشاء رسائل موقعة إلكترونياً.
      </p>

      <div class="relative mb-4">
        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>
        </div>
        <input
          type="text"
          bind:value={searchQuery}
          placeholder="البحث عن مستخدم..."
          class="block w-full p-2 pr-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
    </div>

    {#if loading}
      <div class="flex justify-center items-center p-8">
        <p class="text-gray-500">جاري تحميل البيانات...</p>
      </div>
    {:else if error}
      <div class="p-4 bg-red-50 text-red-600 border-t border-red-200">
        <p>{error}</p>
      </div>
    {:else if filteredUsers.length === 0}
      <div class="p-8 text-center text-gray-500">
        <p>لا توجد نتائج مطابقة للبحث</p>
      </div>
    {:else}
      <div class="overflow-x-auto">
        <table class="w-full text-sm text-right">
          <thead class="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
              <th class="px-6 py-3">الاسم</th>
              <th class="px-6 py-3">البريد الإلكتروني</th>
              <th class="px-6 py-3">الدور</th>
              <th class="px-6 py-3">الوحدة التنظيمية</th>
              <th class="px-6 py-3">صلاحية التوقيع</th>
            </tr>
          </thead>
          <tbody>
            {#each filteredUsers as user}
              <tr class="bg-white border-b hover:bg-gray-50" data-user-id={user.id}>
                <td class="px-6 py-4 font-medium text-gray-900">{user.full_name}</td>
                <td class="px-6 py-4">{user.email}</td>
                <td class="px-6 py-4">
                  <span class="px-2 py-1 text-xs rounded-full {user.role === 'admin' || user.role === 'مشرف' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}">
                    {user.role}
                  </span>
                </td>
                <td class="px-6 py-4">{user.unit_name}</td>
                <td class="px-6 py-4 relative">
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      class="sr-only peer"
                      checked={user.can_sign}
                      disabled={savingPermissions[user.id] || user.role === 'admin' || user.role === 'مشرف'}
                      on:change={() => toggleSignPermission(user.id, !user.can_sign)}
                    />
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:right-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    <span class="mr-3 text-sm font-medium text-gray-900">
                      {#if savingPermissions[user.id]}
                        <svg class="animate-spin h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      {:else if user.role === 'admin' || user.role === 'مشرف'}
                        <span class="text-green-600">صلاحية تلقائية</span>
                      {:else}
                        {user.can_sign ? 'مفعل' : 'غير مفعل'}
                      {/if}
                    </span>
                  </label>

                  <!-- مكان لعرض رسالة التأكيد -->
                  <div class="permission-confirmation-container"></div>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>
</div>

<style>
  .rtl {
    direction: rtl;
    text-align: right;
  }
</style>
