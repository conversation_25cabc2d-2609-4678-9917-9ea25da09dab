<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib/supabase';
  import { PermissionService } from '$lib/services/permissionService';
  import { RoleType, Resource, Action } from '$lib/types/permissions';

  let isAdmin = false;
  let loading = true;
  let stats = {
    users: 0,
    units: 0,
    documents: 0,
    messages: 0,
    broadcasts: 0
  };

  onMount(async () => {
    try {
      // الحصول على المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        goto('/login');
        return;
      }

      // التحقق من صلاحيات المستخدم
      isAdmin = await PermissionService.checkRole(user.id, RoleType.ADMIN);

      if (!isAdmin) {
        // التحقق من صلاحيات المدير
        const isManager = await PermissionService.checkRole(user.id, RoleType.MANAGER);

        if (!isManager) {
          goto('/dashboard');
          return;
        }
      }

      // جلب إحصائيات النظام
      await loadStats();
    } catch (error) {
      console.error('Error loading admin dashboard:', error);
    } finally {
      loading = false;
    }
  });

  async function loadStats() {
    try {
      // جلب عدد المستخدمين
      const { count: usersCount } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // جلب عدد الوحدات التنظيمية
      const { count: unitsCount } = await supabase
        .from('units')
        .select('*', { count: 'exact', head: true });

      // جلب عدد المستندات
      const { count: documentsCount } = await supabase
        .from('documents')
        .select('*', { count: 'exact', head: true });

      // جلب عدد المراسلات
      const { count: messagesCount } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true });

      // جلب عدد التعميمات
      const { count: broadcastsCount } = await supabase
        .from('broadcasts')
        .select('*', { count: 'exact', head: true });

      stats = {
        users: usersCount || 0,
        units: unitsCount || 0,
        documents: documentsCount || 0,
        messages: messagesCount || 0,
        broadcasts: broadcastsCount || 0
      };
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  }
</script>

<svelte:head>
  <title>لوحة تحكم المدير</title>
</svelte:head>

<div class="container mx-auto p-4 rtl" dir="rtl">
  <h1 class="text-3xl font-bold mb-6">لوحة تحكم المدير</h1>

  {#if loading}
    <div class="flex justify-center items-center h-64">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  {:else}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <!-- بطاقة إحصائيات المستخدمين -->
      <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-blue-500">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="text-lg font-semibold text-gray-700">المستخدمين</h3>
            <p class="text-3xl font-bold">{stats.users}</p>
          </div>
          <div class="bg-blue-100 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          </div>
        </div>
        <a href="/admin/users" class="block mt-4 text-blue-500 hover:underline">إدارة المستخدمين</a>
      </div>

      <!-- بطاقة إحصائيات الوحدات التنظيمية -->
      <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-green-500">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="text-lg font-semibold text-gray-700">الوحدات التنظيمية</h3>
            <p class="text-3xl font-bold">{stats.units}</p>
          </div>
          <div class="bg-green-100 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
        </div>
        <a href="/admin/units" class="block mt-4 text-green-500 hover:underline">إدارة الوحدات التنظيمية</a>
      </div>

      <!-- بطاقة إحصائيات المستندات -->
      <div class="bg-white rounded-lg shadow-md p-6 border-r-4 border-purple-500">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="text-lg font-semibold text-gray-700">المستندات</h3>
            <p class="text-3xl font-bold">{stats.documents}</p>
          </div>
          <div class="bg-purple-100 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        </div>
        <a href="/dashboard/documents" class="block mt-4 text-purple-500 hover:underline">إدارة المستندات</a>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <!-- قسم إدارة النظام -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold mb-4 border-r-4 border-indigo-500 pr-3">إدارة النظام</h2>
        <div class="grid grid-cols-1 gap-4">
          <a href="/admin/roles" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-indigo-50 transition-colors">
            <div class="bg-indigo-100 p-2 rounded-full mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <span>إدارة الأدوار والصلاحيات</span>
          </a>

          <a href="/admin/signature-permissions" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-indigo-50 transition-colors">
            <div class="bg-indigo-100 p-2 rounded-full mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path>
                <path d="M14.5 19.5h-5c-1.5 0-2.5-1-2.5-2.5v-10c0-1.5 1-2.5 2.5-2.5h5c1.5 0 2.5 1 2.5 2.5v10c0 1.5-1 2.5-2.5 2.5Z"></path>
                <path d="M12 22v-2"></path>
                <path d="M12 4V2"></path>
              </svg>
            </div>
            <span>صلاحيات التوقيع الإلكتروني</span>
          </a>

          <a href="/admin/settings" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-indigo-50 transition-colors">
            <div class="bg-indigo-100 p-2 rounded-full mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <span>إعدادات النظام</span>
          </a>

          <a href="/admin/logs" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-indigo-50 transition-colors">
            <div class="bg-indigo-100 p-2 rounded-full mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <span>سجلات النظام</span>
          </a>
        </div>
      </div>

      <!-- قسم الإحصائيات والتقارير -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold mb-4 border-r-4 border-amber-500 pr-3">الإحصائيات والتقارير</h2>
        <div class="grid grid-cols-1 gap-4">
          <a href="/admin/reports/users" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-amber-50 transition-colors">
            <div class="bg-amber-100 p-2 rounded-full mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <span>تقارير المستخدمين</span>
          </a>

          <a href="/admin/reports/documents" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-amber-50 transition-colors">
            <div class="bg-amber-100 p-2 rounded-full mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <span>تقارير المستندات</span>
          </a>

          <a href="/admin/reports/activity" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-amber-50 transition-colors">
            <div class="bg-amber-100 p-2 rounded-full mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <span>تقارير النشاط</span>
          </a>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .rtl {
    direction: rtl;
    text-align: right;
  }
</style>
