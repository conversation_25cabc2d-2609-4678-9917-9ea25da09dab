import { supabase } from '$lib/supabase';

export type Unit = {
  id: string;
  name: string;
  type: string;
  description: string | null;
  parent_id: string | null;
  created_at: string;
  updated_at: string;
};

export class UnitService {
  /**
   * الحصول على جميع الوحدات التنظيمية
   */
  static async getAllUnits(): Promise<Unit[]> {
    try {
      const { data, error } = await supabase
        .from('units')
        .select('*')
        .order('type')
        .order('name');

      if (error) {
        console.error('Error fetching units:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('Error in getAllUnits:', err);
      return [];
    }
  }

  /**
   * الحصول على وحدة تنظيمية بواسطة المعرف
   * @param id معرف الوحدة
   */
  static async getUnitById(id: string): Promise<Unit | null> {
    try {
      const { data, error } = await supabase
        .from('units')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching unit:', error);
        return null;
      }

      return data;
    } catch (err) {
      console.error('Error in getUnitById:', err);
      return null;
    }
  }

  /**
   * الحصول على الوحدات التابعة لوحدة معينة (المستوى الأول فقط)
   * @param parentId معرف الوحدة الأم
   */
  static async getDirectSubordinateUnits(parentId: string): Promise<Unit[]> {
    try {
      const { data, error } = await supabase
        .from('units')
        .select('*')
        .eq('parent_id', parentId)
        .order('type')
        .order('name');

      if (error) {
        console.error('Error fetching subordinate units:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('Error in getDirectSubordinateUnits:', err);
      return [];
    }
  }

  /**
   * الحصول على جميع الوحدات التابعة لوحدة معينة (بشكل متدرج)
   * @param unitId معرف الوحدة
   */
  static async getAllSubordinateUnits(unitId: string): Promise<Unit[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_subordinate_units', { parent_unit_id: unitId });

      if (error) {
        console.error('Error fetching all subordinate units:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('Error in getAllSubordinateUnits:', err);
      return [];
    }
  }

  /**
   * الحصول على الوحدة والوحدات التابعة لها (بشكل متدرج)
   * @param unitId معرف الوحدة
   */
  static async getUnitAndSubordinates(unitId: string): Promise<Unit[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_unit_and_subordinates', { unit_id: unitId });

      if (error) {
        console.error('Error fetching unit and subordinates:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('Error in getUnitAndSubordinates:', err);
      return [];
    }
  }

  /**
   * الحصول على معرفات الوحدة والوحدات التابعة لها
   * @param unitId معرف الوحدة
   */
  static async getUnitAndSubordinatesIds(unitId: string): Promise<string[]> {
    try {
      const units = await this.getUnitAndSubordinates(unitId);
      return units.map(unit => unit.id);
    } catch (err) {
      console.error('Error in getUnitAndSubordinatesIds:', err);
      return [];
    }
  }
}
