// ملف لإنشاء وظائف RPC في Supabase
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// إعداد عميل Supabase
const supabaseUrl = 'https://bgbzirxgewwidgybccxq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJnYnppcnhnZXd3aWRneWJjY3hxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTc1MjA0NzYsImV4cCI6MjAzMzA5NjQ3Nn0.Yd-ZVKwKMQZYBjUxmz-Iy6dJOKjXdGDRyQdZPM-4QYE';
const supabase = createClient(supabaseUrl, supabaseKey);

// قراءة ملف SQL
const sqlPath = path.resolve(__dirname, './src/lib/db/create_tables.sql');
const sql = fs.readFileSync(sqlPath, 'utf8');

// تقسيم الاستعلامات SQL
const queries = sql
  .split('CREATE OR REPLACE FUNCTION')
  .filter(query => query.trim().length > 0)
  .map(query => 'CREATE OR REPLACE FUNCTION' + query);

// تنفيذ الاستعلامات SQL
async function executeQueries() {
  console.log('تنفيذ استعلامات SQL لإنشاء وظائف RPC...');
  
  for (let i = 0; i < queries.length; i++) {
    const query = queries[i];
    console.log(`تنفيذ الاستعلام ${i + 1}/${queries.length}`);
    
    try {
      const { data, error } = await supabase.rpc('exec_sql', { query });
      
      if (error) {
        console.error(`خطأ في تنفيذ الاستعلام ${i + 1}:`, error);
      } else {
        console.log(`تم تنفيذ الاستعلام ${i + 1} بنجاح`);
      }
    } catch (err) {
      console.error(`استثناء في تنفيذ الاستعلام ${i + 1}:`, err);
    }
  }
  
  console.log('اكتمل تنفيذ استعلامات SQL');
}

// تنفيذ الاستعلامات
executeQueries().catch(err => {
  console.error('خطأ في تنفيذ الاستعلامات:', err);
});
