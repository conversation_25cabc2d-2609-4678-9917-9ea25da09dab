# نظام الأدوار والصلاحيات

هذا الدليل يشرح كيفية إعداد نظام الأدوار والصلاحيات في قاعدة بيانات Supabase.

## ملفات SQL

- `schema.sql`: يحتوي على استعلامات إنشاء الجداول من الصفر.
- `alter_tables.sql`: يحتوي على استعلامات لتعديل الجداول الموجودة وإضافة الأعمدة المفقودة.
- `setup_complete.sql`: يحتوي على استعلامات لحذف الجداول الموجودة وإعادة إنشائها من الصفر (مفيد لإعادة تعيين قاعدة البيانات).
- `update_profiles.sql`: يحتوي على استعلامات لتحديث جدول المستخدمين (profiles) لربطه بجدول الأدوار (roles).

## خطوات الإعداد

### إذا كانت الجداول غير موجودة

1. قم بتسجيل الدخول إلى لوحة تحكم Supabase الخاصة بك.
2. انتقل إلى قسم "SQL Editor".
3. انقر على "New Query".
4. انسخ محتوى ملف `schema.sql` والصقه في محرر SQL.
5. انقر على "Run" لتنفيذ الاستعلامات.

### إذا كانت الجداول موجودة بالفعل

1. قم بتسجيل الدخول إلى لوحة تحكم Supabase الخاصة بك.
2. انتقل إلى قسم "SQL Editor".
3. انقر على "New Query".
4. انسخ محتوى ملف `alter_tables.sql` والصقه في محرر SQL.
5. انقر على "Run" لتنفيذ الاستعلامات.

### إذا كنت ترغب في إعادة تعيين قاعدة البيانات بالكامل

1. قم بتسجيل الدخول إلى لوحة تحكم Supabase الخاصة بك.
2. انتقل إلى قسم "SQL Editor".
3. انقر على "New Query".
4. انسخ محتوى ملف `setup_complete.sql` والصقه في محرر SQL.
5. انقر على "Run" لتنفيذ الاستعلامات.

**تحذير**: هذا سيحذف جميع البيانات الموجودة في جداول الأدوار والصلاحيات. استخدم هذا الخيار فقط إذا كنت ترغب في إعادة تعيين قاعدة البيانات بالكامل.

### تحديث جدول المستخدمين (profiles)

1. قم بتسجيل الدخول إلى لوحة تحكم Supabase الخاصة بك.
2. انتقل إلى قسم "SQL Editor".
3. انقر على "New Query".
4. انسخ محتوى ملف `update_profiles.sql` والصقه في محرر SQL.
5. انقر على "Run" لتنفيذ الاستعلامات.

هذا سيقوم بإضافة عمود `role_id` إلى جدول `profiles` وربطه بجدول `roles`، وتحديث قيم `role_id` بناءً على قيم `role` الحالية. كما سيقوم بإنشاء triggers للحفاظ على تزامن العمودين `role` و `role_id`.

## هيكل الجداول

### جدول الأدوار (roles)

يحتوي على الأدوار المتاحة في النظام:

- `id`: معرف فريد للدور (UUID)
- `name`: اسم الدور (مثل admin, manager, user)
- `description`: وصف الدور
- `created_at`: تاريخ إنشاء الدور
- `updated_at`: تاريخ تحديث الدور

### جدول الصلاحيات (permissions)

يحتوي على الصلاحيات المتاحة في النظام:

- `id`: معرف فريد للصلاحية (UUID)
- `name`: اسم الصلاحية (مثل documents:create)
- `description`: وصف الصلاحية
- `resource`: المورد الذي تنطبق عليه الصلاحية (مثل documents, users)
- `action`: الإجراء المسموح به (مثل create, read, update, delete)
- `created_at`: تاريخ إنشاء الصلاحية
- `updated_at`: تاريخ تحديث الصلاحية

### جدول العلاقة بين الأدوار والصلاحيات (role_permissions)

يربط بين الأدوار والصلاحيات:

- `id`: معرف فريد للعلاقة (UUID)
- `role_id`: معرف الدور (مفتاح أجنبي يشير إلى جدول الأدوار)
- `permission_id`: معرف الصلاحية (مفتاح أجنبي يشير إلى جدول الصلاحيات)
- `created_at`: تاريخ إنشاء العلاقة

## الأدوار المعرفة مسبقًا

1. **مدير النظام (admin)**:
   - لديه جميع الصلاحيات في النظام.

2. **مدير (manager)**:
   - صلاحيات الإنشاء، القراءة، التحديث للمستندات، المراسلات، التعميمات، الهيكل التنظيمي.
   - صلاحية قراءة المستخدمين فقط.

3. **مستخدم عادي (user)**:
   - صلاحيات الإنشاء والقراءة للمستندات والمراسلات.
   - صلاحية القراءة فقط للتعميمات والهيكل التنظيمي.

## استخدام نظام الأدوار والصلاحيات في التطبيق

يمكنك استخدام خدمة `PermissionService` للتحقق من صلاحيات المستخدم:

```typescript
import { PermissionService } from '$lib/services/permissionService';
import { Action, Resource, RoleType } from '$lib/types/permissions';

// التحقق من دور المستخدم
const isAdmin = await PermissionService.checkRole(userId, RoleType.ADMIN);

// التحقق من صلاحية المستخدم
const canCreateDocument = await PermissionService.checkPermission(
  userId,
  Resource.DOCUMENTS,
  Action.CREATE
);
```

## تعديل الأدوار والصلاحيات

يمكنك استخدام صفحة `/dashboard/roles` لإدارة الأدوار والصلاحيات في واجهة المستخدم.

## تعيين الأدوار للمستخدمين

يمكنك استخدام صفحة `/dashboard/users` لتعيين الأدوار للمستخدمين.
