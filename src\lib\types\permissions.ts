// أنواع البيانات المتعلقة بالأدوار والصلاحيات

export type Role = {
  id: string;
  name: string;
  description: string | null;
  created_at: string;
  updated_at: string;
};

export type Permission = {
  id: string;
  name: string;
  description: string | null;
  resource: string;
  action: string;
  created_at: string;
  updated_at: string;
};

export type RolePermission = {
  id: string;
  role_id: string;
  permission_id: string;
  created_at: string;
};

export type UserRole = {
  id: string;
  user_id: string;
  role_id: string;
  created_at: string;
};

// أنواع الموارد المتاحة في النظام
export enum Resource {
  DOCUMENTS = 'documents',
  USERS = 'users',
  MESSAGES = 'messages',
  BROADCASTS = 'broadcasts',
  ORGANIZATION = 'organization',
  SYSTEM = 'system'
}

// أنواع الإجراءات المتاحة على الموارد
export enum Action {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  SETTINGS = 'settings',
  LOGS = 'logs'
}

// أنواع الأدوار المعرفة في النظام
export enum RoleType {
  ADMIN = 'admin',
  MANAGER = 'manager',
  USER = 'user'
}

// دالة للتحقق من صلاحيات المستخدم
export function hasPermission(
  userPermissions: Permission[],
  resource: Resource,
  action: Action
): boolean {
  return userPermissions.some(
    (permission) => permission.resource === resource && permission.action === action
  );
}

// دالة للتحقق من دور المستخدم
export function hasRole(userRole: string, role: RoleType): boolean {
  return userRole === role;
}

// دالة للحصول على اسم الدور بالعربية
export function getRoleNameArabic(role: string): string {
  switch (role) {
    case RoleType.ADMIN:
      return 'مدير النظام';
    case RoleType.MANAGER:
      return 'مدير';
    case RoleType.USER:
      return 'مستخدم';
    default:
      return 'غير معروف';
  }
}

// دالة للحصول على اسم الصلاحية بالعربية
export function getPermissionNameArabic(resource: string, action: string): string {
  const resourceNames = {
    [Resource.DOCUMENTS]: 'المستندات',
    [Resource.USERS]: 'المستخدمين',
    [Resource.MESSAGES]: 'المراسلات',
    [Resource.BROADCASTS]: 'التعميمات',
    [Resource.ORGANIZATION]: 'الهيكل التنظيمي',
    [Resource.SYSTEM]: 'النظام'
  };
  
  const actionNames = {
    [Action.CREATE]: 'إنشاء',
    [Action.READ]: 'قراءة',
    [Action.UPDATE]: 'تعديل',
    [Action.DELETE]: 'حذف',
    [Action.SETTINGS]: 'إعدادات',
    [Action.LOGS]: 'سجلات'
  };
  
  const resourceName = resourceNames[resource] || resource;
  const actionName = actionNames[action] || action;
  
  return `${actionName} ${resourceName}`;
}
