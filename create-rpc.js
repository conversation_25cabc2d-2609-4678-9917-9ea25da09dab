// ملف لإنشاء وظيفة RPC في Supabase
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// إعداد عميل Supabase
const supabaseUrl = 'https://bgbzirxgewwidgybccxq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJnYnppcnhnZXd3aWRneWJjY3hxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTc1MjA0NzYsImV4cCI6MjAzMzA5NjQ3Nn0.Yd-ZVKwKMQZYBjUxmz-Iy6dJOKjXdGDRyQdZPM-4QYE';
const supabase = createClient(supabaseUrl, supabaseKey);

// قراءة ملف SQL
const sqlPath = path.resolve(__dirname, './src/lib/db/exec_sql.sql');
const sql = fs.readFileSync(sqlPath, 'utf8');

// تنفيذ استعلام SQL لإنشاء وظيفة RPC
async function createRpcFunction() {
  try {
    console.log('Creating RPC function...');
    const { data, error } = await supabase.rpc('exec_sql', { query: sql });

    if (error) {
      console.error('Error creating RPC function:', error);
    } else {
      console.log('RPC function created successfully');
    }
  } catch (err) {
    console.error('Exception creating RPC function:', err);
  }
}

// تنفيذ الوظيفة
createRpcFunction();
