-- إعداد Supabase Storage للمستندات والمرفقات (نسخة محدثة)
-- هذا الملف يحل مشاكل الـ syntax في الملف السابق

-- 1. التأكد من وجود bucket saadabujnah
INSERT INTO storage.buckets (id, name, public)
VALUES ('saadabujnah', 'Saadabujnah Documents', false)
ON CONFLICT (id) DO NOTHING;

-- 2. حذف السياسات الموجودة أولاً (إن وجدت)
DO $$
BEGIN
    -- حذف السياسات إن وجدت
    DROP POLICY IF EXISTS "Authenticated users can read documents" ON storage.objects;
    DROP POLICY IF EXISTS "Authenticated users can upload documents" ON storage.objects;
    DROP POLICY IF EXISTS "Authenticated users can update documents" ON storage.objects;
    DROP POLICY IF EXISTS "Authenticated users can delete documents" ON storage.objects;
EXCEPTION
    WHEN OTHERS THEN
        -- تجاهل الأخطاء إذا لم تكن السياسات موجودة
        NULL;
END $$;

-- 3. إنشاء سياسات الأمان للـ bucket
-- سياسة القراءة
CREATE POLICY "Authenticated users can read documents"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'saadabujnah' 
  AND auth.role() = 'authenticated'
);

-- سياسة الكتابة
CREATE POLICY "Authenticated users can upload documents"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'saadabujnah' 
  AND auth.role() = 'authenticated'
);

-- سياسة التحديث
CREATE POLICY "Authenticated users can update documents"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'saadabujnah' 
  AND auth.role() = 'authenticated'
);

-- سياسة الحذف
CREATE POLICY "Authenticated users can delete documents"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'saadabujnah' 
  AND auth.role() = 'authenticated'
);

-- 4. إضافة الأعمدة الجديدة
ALTER TABLE public.documents 
ADD COLUMN IF NOT EXISTS content_url TEXT;

ALTER TABLE public.messages 
ADD COLUMN IF NOT EXISTS attachment_content_url TEXT;

-- 5. إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_documents_content_url 
ON public.documents(content_url) 
WHERE content_url IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_messages_attachment_content_url 
ON public.messages(attachment_content_url) 
WHERE attachment_content_url IS NOT NULL;

-- 6. إنشاء الوظائف المساعدة
CREATE OR REPLACE FUNCTION public.generate_document_storage_path(
  document_id UUID,
  file_type TEXT DEFAULT 'html'
)
RETURNS TEXT AS $$
BEGIN
  RETURN 'documents/' || 
         EXTRACT(YEAR FROM NOW())::TEXT || '/' ||
         EXTRACT(MONTH FROM NOW())::TEXT || '/' ||
         document_id::TEXT || '.' || file_type;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.generate_attachment_storage_path(
  message_id UUID,
  file_type TEXT DEFAULT 'html'
)
RETURNS TEXT AS $$
BEGIN
  RETURN 'attachments/' || 
         EXTRACT(YEAR FROM NOW())::TEXT || '/' ||
         EXTRACT(MONTH FROM NOW())::TEXT || '/' ||
         message_id::TEXT || '.' || file_type;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. إضافة التعليقات
COMMENT ON COLUMN public.documents.content IS 'محتوى المستند (مهجور - استخدم content_url)';
COMMENT ON COLUMN public.documents.content_url IS 'رابط محتوى المستند في Supabase Storage';
COMMENT ON COLUMN public.messages.attachment_content_url IS 'رابط محتوى المرفق في Supabase Storage';

-- 8. إنشاء Views للمراقبة
DROP VIEW IF EXISTS public.storage_migration_status;

CREATE VIEW public.storage_migration_status AS
SELECT 
  'documents' as table_name,
  COUNT(*) as total_records,
  COUNT(content) as records_with_content,
  COUNT(content_url) as records_with_storage_url,
  COUNT(CASE WHEN content IS NOT NULL AND content_url IS NOT NULL THEN 1 END) as records_migrated,
  COUNT(CASE WHEN content IS NOT NULL AND content_url IS NULL THEN 1 END) as records_pending_migration
FROM public.documents

UNION ALL

SELECT 
  'messages' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN attachment IS NOT NULL AND attachment->>'content' IS NOT NULL THEN 1 END) as records_with_content,
  COUNT(attachment_content_url) as records_with_storage_url,
  COUNT(CASE WHEN attachment IS NOT NULL AND attachment->>'content' IS NOT NULL AND attachment_content_url IS NOT NULL THEN 1 END) as records_migrated,
  COUNT(CASE WHEN attachment IS NOT NULL AND attachment->>'content' IS NOT NULL AND attachment_content_url IS NULL THEN 1 END) as records_pending_migration
FROM public.messages;

-- 9. إنشاء وظيفة للتحقق من حالة النقل
CREATE OR REPLACE FUNCTION public.get_storage_migration_stats()
RETURNS TABLE (
  table_name TEXT,
  total_records BIGINT,
  records_with_content BIGINT,
  records_with_storage_url BIGINT,
  records_migrated BIGINT,
  records_pending_migration BIGINT,
  migration_percentage NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.table_name,
    s.total_records,
    s.records_with_content,
    s.records_with_storage_url,
    s.records_migrated,
    s.records_pending_migration,
    CASE 
      WHEN s.records_with_content > 0 THEN 
        ROUND((s.records_migrated::NUMERIC / s.records_with_content::NUMERIC) * 100, 2)
      ELSE 0
    END as migration_percentage
  FROM public.storage_migration_status s;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. تحديث ذاكرة التخزين المؤقت
NOTIFY pgrst, 'reload schema';
