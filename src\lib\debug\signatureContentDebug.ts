/**
 * أدوات تشخيص مشاكل محتوى التوقيع في المرفقات
 */

import { supabase } from '$lib/supabase';
import { DocumentStorageService } from '$lib/services/documentStorageService';

export interface AttachmentDebugInfo {
  messageId: string;
  messageExists: boolean;
  hasAttachment: boolean;
  attachmentContentSource: 'database' | 'storage' | 'none';
  contentLength: number;
  hasSignatureRequest: boolean;
  signatureRequestId?: string;
  hasSignaturePlaceholder: boolean;
  hasActualSignature: boolean;
  storageInfo?: any;
  errors: string[];
}

/**
 * تشخيص محتوى المرفق والتوقيع
 */
export async function debugAttachmentSignature(signatureRequestId: string): Promise<AttachmentDebugInfo> {
  const debug: AttachmentDebugInfo = {
    messageId: '',
    messageExists: false,
    hasAttachment: false,
    attachmentContentSource: 'none',
    contentLength: 0,
    hasSignatureRequest: false,
    hasSignaturePlaceholder: false,
    hasActualSignature: false,
    errors: []
  };

  try {
    console.log('🔍 تشخيص محتوى المرفق للتوقيع:', signatureRequestId);

    // البحث عن الرسالة التي تحتوي على هذا المستند كمرفق
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('id, attachment, attachment_content_url')
      .contains('attachment', { signature_request_id: signatureRequestId })
      .limit(1);

    if (messagesError) {
      debug.errors.push(`خطأ في البحث عن الرسالة: ${messagesError.message}`);
      console.error('❌ خطأ في البحث عن الرسالة:', messagesError);
      return debug;
    }

    if (!messages || messages.length === 0) {
      debug.errors.push('لم يتم العثور على رسالة تحتوي على هذا المستند كمرفق');
      console.error('❌ لم يتم العثور على رسالة');
      return debug;
    }

    const message = messages[0];
    debug.messageId = message.id;
    debug.messageExists = true;

    console.log('✅ تم العثور على الرسالة:', message.id);

    // التحقق من وجود المرفق
    if (!message.attachment) {
      debug.errors.push('الرسالة لا تحتوي على مرفق');
      console.error('❌ لا يوجد مرفق');
      return debug;
    }

    debug.hasAttachment = true;
    debug.hasSignatureRequest = !!message.attachment.signature_request_id;
    debug.signatureRequestId = message.attachment.signature_request_id;

    console.log('✅ المرفق موجود:', {
      type: message.attachment.type,
      title: message.attachment.title,
      hasSignatureRequest: debug.hasSignatureRequest,
      signatureRequestId: debug.signatureRequestId
    });

    // تحديد مصدر المحتوى
    let content = message.attachment.content;

    if (!content && message.attachment_content_url) {
      debug.attachmentContentSource = 'storage';
      console.log('☁️ جلب المحتوى من Storage...');
      
      try {
        content = await DocumentStorageService.getDocumentContent(message.attachment_content_url);
        if (content) {
          debug.storageInfo = {
            path: message.attachment_content_url,
            success: true,
            contentLength: content.length
          };
          console.log('✅ تم جلب المحتوى من Storage بنجاح');
        } else {
          debug.errors.push('فشل في جلب المحتوى من Storage');
          debug.storageInfo = {
            path: message.attachment_content_url,
            success: false,
            error: 'فشل في جلب المحتوى'
          };
          console.error('❌ فشل في جلب المحتوى من Storage');
        }
      } catch (storageError: any) {
        debug.errors.push(`خطأ في Storage: ${storageError.message}`);
        debug.storageInfo = {
          path: message.attachment_content_url,
          success: false,
          error: storageError.message
        };
        console.error('❌ خطأ في Storage:', storageError);
      }
    } else if (content) {
      debug.attachmentContentSource = 'database';
      console.log('📄 المحتوى موجود في قاعدة البيانات');
    }

    if (content) {
      debug.contentLength = content.length;

      // البحث عن مساحة التوقيع المؤقتة
      const signaturePlaceholderPatterns = [
        /في انتظار التوقيع الإلكتروني/g,
        /سيتم عرض التوقيع الإلكتروني هنا/g,
        /<div[^>]*background-color:\s*#fffbeb[^>]*>/g
      ];

      for (const pattern of signaturePlaceholderPatterns) {
        const matches = content.match(pattern);
        if (matches && matches.length > 0) {
          debug.hasSignaturePlaceholder = true;
          console.log(`✅ تم العثور على مساحة التوقيع المؤقتة: ${matches.length} مطابقة`);
          break;
        }
      }

      // البحث عن التوقيع الفعلي
      const actualSignaturePatterns = [
        /توقيع إلكتروني معتمد/g,
        /تم التحقق من صحة التوقيع الإلكتروني/g,
        /<div[^>]*background-color:\s*#f0fdf4[^>]*>/g
      ];

      for (const pattern of actualSignaturePatterns) {
        const matches = content.match(pattern);
        if (matches && matches.length > 0) {
          debug.hasActualSignature = true;
          console.log(`✅ تم العثور على التوقيع الفعلي: ${matches.length} مطابقة`);
          break;
        }
      }

      console.log('📊 تحليل المحتوى:');
      console.log('- طول المحتوى:', debug.contentLength);
      console.log('- يحتوي على مساحة التوقيع المؤقتة:', debug.hasSignaturePlaceholder);
      console.log('- يحتوي على التوقيع الفعلي:', debug.hasActualSignature);
    } else {
      debug.errors.push('لا يوجد محتوى في المرفق');
      console.error('❌ لا يوجد محتوى');
    }

  } catch (error: any) {
    debug.errors.push(`خطأ عام في التشخيص: ${error.message}`);
    console.error('❌ خطأ عام في التشخيص:', error);
  }

  return debug;
}

/**
 * طباعة تقرير تشخيص مفصل للمرفق
 */
export function printAttachmentDebugReport(debug: AttachmentDebugInfo): void {
  console.log('\n📋 تقرير تشخيص محتوى المرفق والتوقيع');
  console.log('==========================================');
  
  console.log(`📧 معرف الرسالة: ${debug.messageId || 'غير محدد'}`);
  console.log(`📄 الرسالة موجودة: ${debug.messageExists ? '✅ نعم' : '❌ لا'}`);
  console.log(`📎 يحتوي على مرفق: ${debug.hasAttachment ? '✅ نعم' : '❌ لا'}`);
  console.log(`🔗 يحتوي على طلب توقيع: ${debug.hasSignatureRequest ? '✅ نعم' : '❌ لا'}`);
  
  if (debug.signatureRequestId) {
    console.log(`   - معرف طلب التوقيع: ${debug.signatureRequestId}`);
  }
  
  console.log(`📝 مصدر المحتوى: ${debug.attachmentContentSource}`);
  console.log(`📏 طول المحتوى: ${debug.contentLength} حرف`);
  
  console.log(`🔄 يحتوي على مساحة التوقيع المؤقتة: ${debug.hasSignaturePlaceholder ? '✅ نعم' : '❌ لا'}`);
  console.log(`✅ يحتوي على التوقيع الفعلي: ${debug.hasActualSignature ? '✅ نعم' : '❌ لا'}`);
  
  if (debug.storageInfo) {
    console.log(`☁️ معلومات Storage:`);
    console.log(`   - المسار: ${debug.storageInfo.path}`);
    console.log(`   - النجاح: ${debug.storageInfo.success ? '✅' : '❌'}`);
    if (debug.storageInfo.error) {
      console.log(`   - الخطأ: ${debug.storageInfo.error}`);
    }
  }
  
  if (debug.errors.length > 0) {
    console.log('\n❌ الأخطاء المكتشفة:');
    debug.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  } else {
    console.log('\n✅ لا توجد أخطاء مكتشفة');
  }
  
  // تقييم الحالة
  console.log('\n🎯 تقييم الحالة:');
  if (debug.hasActualSignature) {
    console.log('✅ التوقيع موجود ويجب أن يظهر في المرفق');
  } else if (debug.hasSignaturePlaceholder) {
    console.log('⚠️ مساحة التوقيع المؤقتة موجودة ولكن لم يتم استبدالها بالتوقيع الفعلي');
  } else {
    console.log('❌ لا يوجد توقيع أو مساحة توقيع في المحتوى');
  }
  
  console.log('\n==========================================\n');
}

/**
 * إصلاح محتوى المرفق بإضافة التوقيع المفقود
 */
export async function fixAttachmentSignature(signatureRequestId: string, signature: any): Promise<boolean> {
  try {
    console.log('🔧 محاولة إصلاح محتوى المرفق...');
    
    const debug = await debugAttachmentSignature(signatureRequestId);
    printAttachmentDebugReport(debug);
    
    if (!debug.messageExists || !debug.hasAttachment) {
      console.error('❌ لا يمكن إصلاح المرفق: الرسالة أو المرفق غير موجود');
      return false;
    }
    
    if (debug.hasActualSignature) {
      console.log('✅ التوقيع موجود بالفعل، لا حاجة للإصلاح');
      return true;
    }
    
    if (!debug.hasSignaturePlaceholder) {
      console.error('❌ لا يمكن إصلاح المرفق: لا توجد مساحة توقيع للاستبدال');
      return false;
    }
    
    // محاولة إصلاح المحتوى
    console.log('🔧 بدء عملية الإصلاح...');
    
    // هنا يمكن إضافة منطق الإصلاح
    // سيتم تنفيذه في updateDocumentContentWithSignature
    
    return true;
    
  } catch (error: any) {
    console.error('❌ فشل في إصلاح محتوى المرفق:', error);
    return false;
  }
}
