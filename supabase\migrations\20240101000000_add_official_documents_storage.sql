-- إنشاء مخزن للشعارات
INSERT INTO storage.buckets (id, name, public)
VALUES ('logos', 'Logos', true)
ON CONFLICT (id) DO NOTHING;

-- إنشاء مخزن للأختام
INSERT INTO storage.buckets (id, name, public)
VALUES ('stamps', 'Stamps', true)
ON CONFLICT (id) DO NOTHING;

-- إضافة سياسات الوصول للشعارات
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Logos Access Policy',
  '(bucket_id = ''logos''::text)',
  'logos'
)
ON CONFLICT (name, bucket_id) DO NOTHING;

-- إضافة سياسات الوصول للأختام
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Stamps Access Policy',
  '(bucket_id = ''stamps''::text)',
  'stamps'
)
ON CONFLICT (name, bucket_id) DO NOTHING;

-- إضافة أعمدة جديدة لجدول الرسائل
ALTER TABLE public.messages
ADD COLUMN IF NOT EXISTS is_official BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS unit_name TEXT,
ADD COLUMN IF NOT EXISTS unit_logo TEXT,
ADD COLUMN IF NOT EXISTS organization_logo TEXT,
ADD COLUMN IF NOT EXISTS stamp TEXT;

-- إنشاء جدول إعدادات المنظمة
CREATE TABLE IF NOT EXISTS public.organization_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT,
  logo TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة سجل افتراضي لإعدادات المنظمة
INSERT INTO public.organization_settings (name)
VALUES ('المنظمة الافتراضية')
ON CONFLICT DO NOTHING;

-- إضافة عمود للشعار في جدول الوحدات التنظيمية
ALTER TABLE public.units
ADD COLUMN IF NOT EXISTS logo TEXT;

-- إنشاء وظيفة لتوليد الرقم الإشاري
CREATE OR REPLACE FUNCTION public.generate_reference_number(signature_id TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
  short_id TEXT;
  today DATE;
  year_str TEXT;
  month_str TEXT;
  day_str TEXT;
BEGIN
  -- استخدام جزء من معرف التوقيع
  short_id := SUBSTRING(signature_id, 1, 8);
  
  -- الحصول على التاريخ الحالي
  today := CURRENT_DATE;
  
  -- تنسيق التاريخ
  year_str := EXTRACT(YEAR FROM today)::TEXT;
  month_str := LPAD(EXTRACT(MONTH FROM today)::TEXT, 2, '0');
  day_str := LPAD(EXTRACT(DAY FROM today)::TEXT, 2, '0');
  
  -- إرجاع الرقم الإشاري بالتنسيق المطلوب
  RETURN year_str || '/' || month_str || '/' || day_str || '/' || short_id;
END;
$$;

-- إنشاء وظيفة للتحقق من وجود عمود في جدول
CREATE OR REPLACE FUNCTION public.check_column_exists(
  table_name TEXT,
  column_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  column_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = check_column_exists.table_name
    AND column_name = check_column_exists.column_name
  ) INTO column_exists;
  
  RETURN column_exists;
END;
$$;

-- إنشاء وظيفة لإضافة عمود parent_id إذا لم يكن موجوداً
CREATE OR REPLACE FUNCTION public.add_parent_id_column_if_not_exists()
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'parent_id'
  ) THEN
    ALTER TABLE public.messages
    ADD COLUMN parent_id UUID REFERENCES public.messages(id) ON DELETE SET NULL;
  END IF;
END;
$$;
