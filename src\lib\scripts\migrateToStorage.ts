import { supabase } from '$lib/supabase';
import { DocumentStorageService } from '$lib/services/documentStorageService';

/**
 * سكريبت لنقل المحتوى الموجود من قاعدة البيانات إلى Supabase Storage
 */

interface MigrationResult {
  success: boolean;
  message: string;
  details?: any;
}

interface MigrationStats {
  documentsProcessed: number;
  documentsSuccess: number;
  documentsFailed: number;
  messagesProcessed: number;
  messagesSuccess: number;
  messagesFailed: number;
  errors: string[];
}

/**
 * نقل جميع المستندات من قاعدة البيانات إلى Storage
 */
export async function migrateDocumentsToStorage(): Promise<MigrationResult> {
  try {
    console.log('بدء نقل المستندات إلى Storage...');
    
    // جلب جميع المستندات التي تحتوي على محتوى ولا تحتوي على content_url
    const { data: documents, error: fetchError } = await supabase
      .from('documents')
      .select('id, title, content')
      .not('content', 'is', null)
      .is('content_url', null)
      .limit(100); // معالجة 100 مستند في كل مرة

    if (fetchError) {
      console.error('خطأ في جلب المستندات:', fetchError);
      return {
        success: false,
        message: 'فشل في جلب المستندات من قاعدة البيانات'
      };
    }

    if (!documents || documents.length === 0) {
      return {
        success: true,
        message: 'لا توجد مستندات تحتاج إلى نقل'
      };
    }

    console.log(`تم العثور على ${documents.length} مستند للنقل`);

    const stats: MigrationStats = {
      documentsProcessed: 0,
      documentsSuccess: 0,
      documentsFailed: 0,
      messagesProcessed: 0,
      messagesSuccess: 0,
      messagesFailed: 0,
      errors: []
    };

    // نقل كل مستند
    for (const document of documents) {
      stats.documentsProcessed++;
      
      try {
        console.log(`نقل المستند ${document.id}: ${document.title}`);
        
        // نقل المستند إلى Storage
        const migrated = await DocumentStorageService.migrateDocumentToStorage(document.id);
        
        if (migrated) {
          stats.documentsSuccess++;
          console.log(`تم نقل المستند ${document.id} بنجاح`);
        } else {
          stats.documentsFailed++;
          stats.errors.push(`فشل في نقل المستند ${document.id}: ${document.title}`);
          console.error(`فشل في نقل المستند ${document.id}`);
        }
      } catch (err) {
        stats.documentsFailed++;
        const errorMsg = `خطأ في نقل المستند ${document.id}: ${err}`;
        stats.errors.push(errorMsg);
        console.error(errorMsg);
      }

      // توقف قصير لتجنب إرهاق الخادم
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return {
      success: stats.documentsFailed === 0,
      message: `تم نقل ${stats.documentsSuccess} من ${stats.documentsProcessed} مستند بنجاح`,
      details: stats
    };
  } catch (err) {
    console.error('خطأ في نقل المستندات:', err);
    return {
      success: false,
      message: `خطأ في نقل المستندات: ${err}`
    };
  }
}

/**
 * نقل جميع مرفقات الرسائل من قاعدة البيانات إلى Storage
 */
export async function migrateAttachmentsToStorage(): Promise<MigrationResult> {
  try {
    console.log('بدء نقل مرفقات الرسائل إلى Storage...');
    
    // جلب جميع الرسائل التي تحتوي على مرفقات ولا تحتوي على attachment_content_url
    const { data: messages, error: fetchError } = await supabase
      .from('messages')
      .select('id, subject, attachment')
      .not('attachment', 'is', null)
      .is('attachment_content_url', null)
      .limit(100); // معالجة 100 رسالة في كل مرة

    if (fetchError) {
      console.error('خطأ في جلب الرسائل:', fetchError);
      return {
        success: false,
        message: 'فشل في جلب الرسائل من قاعدة البيانات'
      };
    }

    if (!messages || messages.length === 0) {
      return {
        success: true,
        message: 'لا توجد مرفقات تحتاج إلى نقل'
      };
    }

    console.log(`تم العثور على ${messages.length} رسالة تحتوي على مرفقات للنقل`);

    const stats: MigrationStats = {
      documentsProcessed: 0,
      documentsSuccess: 0,
      documentsFailed: 0,
      messagesProcessed: 0,
      messagesSuccess: 0,
      messagesFailed: 0,
      errors: []
    };

    // نقل كل مرفق
    for (const message of messages) {
      stats.messagesProcessed++;
      
      try {
        // التحقق من وجود محتوى في المرفق
        if (!message.attachment?.content) {
          console.log(`تخطي الرسالة ${message.id}: لا يوجد محتوى في المرفق`);
          continue;
        }

        console.log(`نقل مرفق الرسالة ${message.id}: ${message.subject}`);
        
        // حفظ محتوى المرفق في Storage
        const storagePath = await DocumentStorageService.saveAttachmentContent(
          message.id, 
          message.attachment.content
        );
        
        if (storagePath) {
          // تحديث الرسالة برابط Storage
          const updated = await DocumentStorageService.updateAttachmentContentUrl(message.id, storagePath);
          
          if (updated) {
            stats.messagesSuccess++;
            console.log(`تم نقل مرفق الرسالة ${message.id} بنجاح`);
          } else {
            stats.messagesFailed++;
            stats.errors.push(`فشل في تحديث رابط مرفق الرسالة ${message.id}`);
            console.error(`فشل في تحديث رابط مرفق الرسالة ${message.id}`);
          }
        } else {
          stats.messagesFailed++;
          stats.errors.push(`فشل في حفظ مرفق الرسالة ${message.id} في Storage`);
          console.error(`فشل في حفظ مرفق الرسالة ${message.id} في Storage`);
        }
      } catch (err) {
        stats.messagesFailed++;
        const errorMsg = `خطأ في نقل مرفق الرسالة ${message.id}: ${err}`;
        stats.errors.push(errorMsg);
        console.error(errorMsg);
      }

      // توقف قصير لتجنب إرهاق الخادم
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return {
      success: stats.messagesFailed === 0,
      message: `تم نقل ${stats.messagesSuccess} من ${stats.messagesProcessed} مرفق بنجاح`,
      details: stats
    };
  } catch (err) {
    console.error('خطأ في نقل المرفقات:', err);
    return {
      success: false,
      message: `خطأ في نقل المرفقات: ${err}`
    };
  }
}

/**
 * نقل جميع البيانات (المستندات والمرفقات) إلى Storage
 */
export async function migrateAllToStorage(): Promise<MigrationResult> {
  try {
    console.log('بدء نقل جميع البيانات إلى Storage...');
    
    // نقل المستندات أولاً
    const documentsResult = await migrateDocumentsToStorage();
    console.log('نتيجة نقل المستندات:', documentsResult);
    
    // نقل المرفقات
    const attachmentsResult = await migrateAttachmentsToStorage();
    console.log('نتيجة نقل المرفقات:', attachmentsResult);
    
    const totalSuccess = documentsResult.success && attachmentsResult.success;
    const combinedStats = {
      documents: documentsResult.details,
      attachments: attachmentsResult.details
    };
    
    return {
      success: totalSuccess,
      message: `نقل البيانات مكتمل. المستندات: ${documentsResult.message}. المرفقات: ${attachmentsResult.message}`,
      details: combinedStats
    };
  } catch (err) {
    console.error('خطأ في نقل جميع البيانات:', err);
    return {
      success: false,
      message: `خطأ في نقل جميع البيانات: ${err}`
    };
  }
}

/**
 * تنظيف البيانات القديمة من قاعدة البيانات بعد التأكد من نقلها بنجاح
 */
export async function cleanupOldData(): Promise<MigrationResult> {
  try {
    console.log('بدء تنظيف البيانات القديمة...');
    
    // تنظيف محتوى المستندات التي تم نقلها بنجاح
    const { error: documentsError } = await supabase
      .from('documents')
      .update({ content: null })
      .not('content_url', 'is', null)
      .not('content', 'is', null);
    
    if (documentsError) {
      console.error('خطأ في تنظيف محتوى المستندات:', documentsError);
      return {
        success: false,
        message: 'فشل في تنظيف محتوى المستندات'
      };
    }
    
    // تنظيف محتوى المرفقات التي تم نقلها بنجاح
    // ملاحظة: نحتفظ بالمرفق نفسه ولكن نحذف المحتوى فقط
    const { data: messagesToClean, error: fetchError } = await supabase
      .from('messages')
      .select('id, attachment')
      .not('attachment_content_url', 'is', null)
      .not('attachment', 'is', null);
    
    if (fetchError) {
      console.error('خطأ في جلب الرسائل للتنظيف:', fetchError);
      return {
        success: false,
        message: 'فشل في جلب الرسائل للتنظيف'
      };
    }
    
    if (messagesToClean && messagesToClean.length > 0) {
      for (const message of messagesToClean) {
        if (message.attachment?.content) {
          // إزالة المحتوى من المرفق مع الاحتفاظ بباقي البيانات
          const cleanedAttachment = {
            ...message.attachment,
            content: null
          };
          
          const { error: updateError } = await supabase
            .from('messages')
            .update({ attachment: cleanedAttachment })
            .eq('id', message.id);
          
          if (updateError) {
            console.error(`خطأ في تنظيف مرفق الرسالة ${message.id}:`, updateError);
          }
        }
      }
    }
    
    return {
      success: true,
      message: 'تم تنظيف البيانات القديمة بنجاح'
    };
  } catch (err) {
    console.error('خطأ في تنظيف البيانات القديمة:', err);
    return {
      success: false,
      message: `خطأ في تنظيف البيانات القديمة: ${err}`
    };
  }
}
