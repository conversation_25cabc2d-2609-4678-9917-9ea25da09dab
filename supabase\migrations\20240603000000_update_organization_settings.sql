-- تحديث جدول إعدادات المنظمة
-- التأكد من وجود جميع الأعمدة المطلوبة

-- إضافة الأعمدة إذا لم تكن موجودة
DO $$
BEGIN
    -- التحقق من وجود عمود name وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'name') THEN
        ALTER TABLE organization_settings ADD COLUMN name TEXT;
    END IF;

    -- التحقق من وجود عمود logo وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'logo') THEN
        ALTER TABLE organization_settings ADD COLUMN logo TEXT;
    END IF;

    -- التحقق من وجود عمود right_logo وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'right_logo') THEN
        ALTER TABLE organization_settings ADD COLUMN right_logo TEXT;
    END IF;

    -- التحقق من وجود عمود left_logo وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'left_logo') THEN
        ALTER TABLE organization_settings ADD COLUMN left_logo TEXT;
    END IF;

    -- التحقق من وجود عمود default_stamp وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'default_stamp') THEN
        ALTER TABLE organization_settings ADD COLUMN default_stamp TEXT;
    END IF;

    -- التحقق من وجود عمود address وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'address') THEN
        ALTER TABLE organization_settings ADD COLUMN address TEXT;
    END IF;

    -- التحقق من وجود عمود phone وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'phone') THEN
        ALTER TABLE organization_settings ADD COLUMN phone TEXT;
    END IF;

    -- التحقق من وجود عمود email وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'email') THEN
        ALTER TABLE organization_settings ADD COLUMN email TEXT;
    END IF;

    -- التحقق من وجود عمود website وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'website') THEN
        ALTER TABLE organization_settings ADD COLUMN website TEXT;
    END IF;

    -- التحقق من وجود عمود footer_text وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'footer_text') THEN
        ALTER TABLE organization_settings ADD COLUMN footer_text TEXT;
    END IF;

    -- التحقق من وجود عمود header_text وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'header_text') THEN
        ALTER TABLE organization_settings ADD COLUMN header_text TEXT;
    END IF;

    -- التحقق من وجود عمود created_at وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'created_at') THEN
        ALTER TABLE organization_settings ADD COLUMN created_at TIMESTAMPTZ DEFAULT NOW();
    END IF;

    -- التحقق من وجود عمود updated_at وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'updated_at') THEN
        ALTER TABLE organization_settings ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();
    END IF;
END $$;

-- إضافة سجل افتراضي إذا لم يكن هناك سجلات
INSERT INTO organization_settings (
    name,
    logo,
    right_logo,
    left_logo,
    default_stamp,
    address,
    phone,
    email,
    website,
    footer_text,
    header_text,
    created_at,
    updated_at
)
SELECT
    'دولة ليبيا',
    'https://drive.google.com/uc?export=view&id=1Kz_w_hrkQP9XWfxKOOI9xU7SkHWgdA0H',
    'https://drive.google.com/uc?export=view&id=1Kz_w_hrkQP9XWfxKOOI9xU7SkHWgdA0H',
    'https://drive.google.com/uc?export=view&id=1Kz_w_hrkQP9XWfxKOOI9xU7SkHWgdA0H',
    'https://drive.google.com/uc?export=view&id=1Kz_w_hrkQP9XWfxKOOI9xU7SkHWgdA0H',
    'طرابلس - ليبيا',
    '+218 21 1234567',
    '<EMAIL>',
    'www.moj.gov.ly',
    'جميع الحقوق محفوظة © {year} وزارة العدل - دولة ليبيا',
    'وزارة العدل - المراسلات الرسمية',
    NOW(),
    NOW()
WHERE NOT EXISTS (SELECT 1 FROM organization_settings);
