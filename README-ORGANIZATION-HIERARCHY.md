# تعديل الهيكل التنظيمي

تم تنفيذ تعديلات على الهيكل التنظيمي لربط المكاتب والفروع بشكل صحيح، وتحسين طريقة عرض الهيكل التنظيمي في واجهة المستخدم.

## التغييرات التي تم تنفيذها:

### 1. إنشاء وظيفة RPC لتحديث الهيكل التنظيمي

تم إنشاء وظيفة `update_organization_hierarchy` في قاعدة البيانات لتحديث العلاقات بين الوحدات التنظيمية، وخاصة:

- ربط "ادارة المحاماه العامة" كإدارة رئيسية
- ربط "مكتب المحاماه سبها" كفرع تابع لإدارة المحاماة العامة
- ربط "مكتب المحاماه العامة الشاطئ" كمكتب تابع لفرع سبها

### 2. تحسين طريقة عرض الهيكل التنظيمي

- تم تعديل وظيفة `buildUnitTree` لترتيب الوحدات حسب نوعها (وزارة، إدارة، فرع، قسم، مكتب)
- تم تعديل وظيفة `renderUnitTree` لإظهار الوحدة الأم لكل وحدة
- تم إضافة تنسيق أفضل للهيكل التنظيمي في واجهة المستخدم

### 3. إضافة وظائف للتحقق من صحة الهيكل التنظيمي

- تم إنشاء وظيفة `validate_unit_hierarchy` للتحقق من صحة العلاقات بين الوحدات
- تم إنشاء وظيفة `get_organization_hierarchy` لعرض الهيكل التنظيمي بشكل متدرج

### 4. إضافة زر لتحديث الهيكل التنظيمي

تم إضافة زر "تحديث الهيكل التنظيمي" في صفحة الهيكل التنظيمي، يقوم بتنفيذ وظيفة `update_organization_hierarchy` وإعادة تحميل الهيكل التنظيمي.

## كيفية تطبيق التغييرات:

1. قم بتنفيذ ملف `src\lib\db\update_organization_hierarchy.sql` في قاعدة البيانات Supabase باستخدام SQL Editor
2. قم بتحديث الهيكل التنظيمي من خلال الضغط على زر "تحديث الهيكل التنظيمي" في صفحة الهيكل التنظيمي

## الهيكل التنظيمي الجديد:

```
ادارة المحاماه العامة (إدارة)
  └── مكتب المحاماه سبها (فرع)
        └── مكتب المحاماه العامة الشاطئ (مكتب)
```

## ملاحظات هامة:

1. يجب أن يكون نوع الوحدة متوافقاً مع موقعها في الهيكل التنظيمي:
   - الإدارة: يمكن أن تكون تابعة للوزارة أو بدون وحدة أم
   - الفرع: يمكن أن يكون تابعاً للإدارة أو الوزارة
   - القسم: يمكن أن يكون تابعاً للإدارة أو الفرع
   - المكتب: يمكن أن يكون تابعاً للإدارة أو الفرع أو القسم

2. يمكن للمشرفين فقط تعديل الهيكل التنظيمي وإضافة وحدات جديدة

3. يتم استخدام الهيكل التنظيمي لتحديد صلاحيات الوصول إلى المستندات، حيث:
   - يمكن للمستخدم رؤية المستندات الخاصة بوحدته فقط
   - يمكن للمدير رؤية المستندات الخاصة بوحدته والوحدات التابعة لها
   - يمكن للمشرف رؤية جميع المستندات
