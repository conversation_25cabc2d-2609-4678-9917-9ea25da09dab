@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

@import 'tailwindcss';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }

  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-white text-gray-900 transition-colors duration-300;
    font-family: 'Tajawal', sans-serif;
  }

  .dark,
  .dark body {
    @apply bg-gray-900 text-gray-100;
  }

  /* تم نقل أنماط الوضع المظلم إلى ملف منفصل */

  /* RTL Support */
  .rtl {
    direction: rtl;
    text-align: right;
  }

  /* تنسيقات إضافية */
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }

  /* تنسيق الروابط */
  a {
    @apply transition-colors duration-200;
  }

  /* تنسيق الأزرار */
  button, .btn {
    @apply transition-all duration-200;
  }

  /* تنسيق البطاقات */
  .card {
    @apply rounded-lg border shadow-sm transition-all duration-200 hover:shadow-md;
  }

  /* تنسيق الجداول */
  table {
    @apply w-full border-collapse;
  }

  th {
    @apply font-bold text-right p-3;
  }

  td {
    @apply p-3 border-t;
  }

  tr:hover {
    @apply bg-gray-100/50;
  }

  /* تنسيق النماذج */
  input, textarea, select {
    @apply rounded-md border border-gray-200 bg-white px-3 py-2 text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  label {
    @apply text-sm font-medium mb-1 block;
  }
}
