<script lang="ts">
  import { page } from '$app/stores';
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';

  type Message = {
    id: string;
    subject: string;
    content: string;
    sender_id: string;
    receiver_id: string | null;
    recipient_id: string | null;
    receiver_unit_id: string | null;
    status: string;
    is_read: boolean;
    parent_id: string | null;
    created_at: string;
    updated_at: string;
    sender?: {
      full_name: string;
      email: string;
    };
    receiver?: {
      full_name: string;
      email: string;
    };
    receiver_unit?: {
      name: string;
    };
    replies?: Message[];
  };

  let message: Message | null = null;
  let loading = true;
  let error = '';
  let currentUserId = '';
  let replyContent = '';
  let showReplyForm = false;

  onMount(async () => {
    // تسجيل قراءة الرسالة
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      currentUserId = user.id;

      // تحديث حالة الرسالة إلى "مقروءة" إذا كان المستخدم هو المستقبل
      const { data: messageData } = await supabase
        .from('messages')
        .select('receiver_id, recipient_id, receiver_unit_id, is_read')
        .eq('id', $page.params.id)
        .single();

      if (messageData) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('unit_id')
          .eq('id', user.id)
          .single();

        // تحديث الحالة إلى "مقروءة" إذا كان المستخدم هو المستقبل أو ينتمي إلى الوحدة المستقبلة
        if (
          (messageData.receiver_id === user.id ||
           messageData.recipient_id === user.id ||
           (messageData.receiver_unit_id && messageData.receiver_unit_id === profile?.unit_id)) &&
          !messageData.is_read
        ) {
          await supabase
            .from('messages')
            .update({ is_read: true, updated_at: new Date().toISOString() })
            .eq('id', $page.params.id);
        }
      }
    }

    await fetchMessageData();
  });

  async function fetchMessageData() {
    try {
      loading = true;

      // جلب بيانات الرسالة
      const { data, error: messageError } = await supabase
        .from('messages')
        .select(`
          *,
          sender:sender_id(full_name, email)
        `)
        .eq('id', $page.params.id)
        .single();

      if (messageError) {
        console.error('خطأ في جلب بيانات الرسالة:', messageError);
        throw messageError;
      }

      message = data;

      // جلب بيانات المستقبل (مستخدم)
      if (message && message.recipient_id) {
        const { data: receiverData, error: receiverError } = await supabase
          .from('profiles')
          .select('full_name, email')
          .eq('id', message.recipient_id)
          .single();

        if (!receiverError && receiverData && message) {
          message.receiver = receiverData;
        }
      }

      // جلب بيانات المستقبل (وحدة تنظيمية)
      if (message && message.receiver_unit_id) {
        const { data: unitData, error: unitError } = await supabase
          .from('units')
          .select('name')
          .eq('id', message.receiver_unit_id)
          .single();

        if (!unitError && unitData && message) {
          message.receiver_unit = unitData;
        }
      }

      // جلب الردود على الرسالة
      const { data: replies, error: repliesError } = await supabase
        .from('messages')
        .select(`
          *,
          sender:sender_id(full_name, email)
        `)
        .eq('parent_id', $page.params.id)
        .order('created_at', { ascending: true });

      if (repliesError) {
        console.error('خطأ في جلب الردود:', repliesError);
        throw repliesError;
      }

      if (message && replies) {
        message.replies = replies;
      }
    } catch (err) {
      console.error('خطأ في جلب بيانات الرسالة:', err);
      error = 'حدث خطأ أثناء جلب بيانات الرسالة';
    } finally {
      loading = false;
    }
  }

  async function handleSendReply() {
    if (!replyContent.trim()) {
      alert('يرجى إدخال محتوى الرد');
      return;
    }

    if (!message || !currentUserId) {
      alert('حدث خطأ أثناء إرسال الرد');
      return;
    }

    try {
      // إرسال الرد
      const { data, error: replyError } = await supabase
        .from('messages')
        .insert([
          {
            subject: `رد: ${message.subject}`,
            content: replyContent,
            sender_id: currentUserId,
            recipient_id: message.sender_id, // استخدام recipient_id
            receiver_id: message.sender_id, // استخدام receiver_id أيضاً للتوافق
            is_read: false,
            parent_id: message.id
          }
        ])
        .select(`
          *,
          sender:sender_id(full_name, email)
        `);

      if (replyError) {
        throw replyError;
      }

      // تحديث حالة الرسالة الأصلية إلى "تم الرد"
      const { error: updateError } = await supabase
        .from('messages')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('id', message.id);

      if (updateError) {
        throw updateError;
      }

      // تحديث الواجهة
      if (data && message.replies) {
        message.replies = [...message.replies, data[0]];
        message.is_read = true;
      }

      // إعادة تعيين نموذج الرد
      replyContent = '';
      showReplyForm = false;
    } catch (err) {
      console.error('خطأ في إرسال الرد:', err);
      alert('حدث خطأ أثناء إرسال الرد');
    }
  }

  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  }

  function getStatusText(is_read: boolean) {
    return is_read ? 'تمت القراءة' : 'لم تتم القراءة';
  }

  function getStatusColor(is_read: boolean) {
    return is_read ? 'bg-green-500 text-white' : 'bg-primary text-primary-foreground';
  }
</script>

<div>
  <div class="mb-6">
    <a
      href="/dashboard/messages"
      class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="m15 18-6-6 6-6"/></svg>
      العودة إلى المراسلات
    </a>
  </div>

  {#if loading}
    <div class="flex justify-center items-center h-64">
      <p class="text-lg">جاري التحميل...</p>
    </div>
  {:else if error}
    <div class="bg-destructive/20 p-4 rounded-md text-destructive">
      <p>{error}</p>
    </div>
  {:else if message}
    <div class="bg-card rounded-lg shadow overflow-hidden mb-6">
      <div class="p-6 border-b">
        <div class="flex justify-between items-start">
          <div>
            <h1 class="text-2xl font-bold mb-2">{message.subject}</h1>
            <div class="flex flex-wrap gap-4 text-sm text-muted-foreground">
              <div>
                <span class="font-medium">من:</span> {message.sender?.full_name || 'غير معروف'}
              </div>
              <div>
                <span class="font-medium">إلى:</span> {message.receiver?.full_name || message.receiver_unit?.name || 'غير معروف'}
              </div>
              <div>
                <span class="font-medium">التاريخ:</span> {formatDate(message.created_at)}
              </div>
              <div>
                <span class="font-medium">الحالة:</span>
                <span class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 {getStatusColor(message.is_read)}">
                  {getStatusText(message.is_read)}
                </span>
              </div>
            </div>
          </div>

          {#if message.sender_id !== currentUserId && !showReplyForm}
            <button
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
              on:click={() => showReplyForm = true}
            >
              الرد
            </button>
          {/if}
        </div>
      </div>

      <div class="p-6 border-b">
        <div class="whitespace-pre-wrap">{message.content}</div>
      </div>

      <!-- تم إزالة قسم المستند المرفق لأنه غير موجود في قاعدة البيانات -->

      {#if showReplyForm}
        <div class="p-6 border-b bg-muted/20">
          <h3 class="font-bold mb-4">الرد على الرسالة</h3>

          <div class="space-y-4">
            <textarea
              bind:value={replyContent}
              placeholder="اكتب ردك هنا..."
              class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              rows="4"
            ></textarea>

            <div class="flex justify-end gap-2">
              <button
                class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
                on:click={() => showReplyForm = false}
              >
                إلغاء
              </button>
              <button
                class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                on:click={handleSendReply}
              >
                إرسال الرد
              </button>
            </div>
          </div>
        </div>
      {/if}

      {#if message.replies && message.replies.length > 0}
        <div class="p-6">
          <h3 class="font-bold mb-4">الردود ({message.replies.length})</h3>

          <div class="space-y-6">
            {#each message.replies as reply}
              <div class="border rounded-md p-4">
                <div class="flex justify-between items-center mb-2">
                  <div class="font-medium">{reply.sender?.full_name || 'غير معروف'}</div>
                  <div class="text-sm text-muted-foreground">{formatDate(reply.created_at)}</div>
                </div>
                <div class="whitespace-pre-wrap">{reply.content}</div>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  {:else}
    <div class="bg-destructive/20 p-4 rounded-md text-destructive">
      <p>لم يتم العثور على الرسالة</p>
    </div>
  {/if}
</div>
