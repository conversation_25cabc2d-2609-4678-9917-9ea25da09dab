-- ملف إعداد قاعدة البيانات الكاملة
-- يقوم هذا الملف بإنشاء جميع الجداول والعلاقات اللازمة لنظام الأرشفة الإلكتروني

-- التأكد من وجود امتداد uuid-ossp
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- حذف الجداول إذا كانت موجودة (بترتيب عكسي للاعتمادية)
DROP TABLE IF EXISTS role_permissions;
DROP TABLE IF EXISTS user_permissions;
DROP TABLE IF EXISTS permissions;
DROP TABLE IF EXISTS document_readers;
DROP TABLE IF EXISTS document_attachments;
DROP TABLE IF EXISTS documents;
DROP TABLE IF EXISTS message_attachments;
DROP TABLE IF EXISTS chat_messages;
DROP TABLE IF EXISTS chat_channels;
DROP TABLE IF EXISTS broadcast_readers;
DROP TABLE IF EXISTS broadcast_recipients;
DROP TABLE IF EXISTS broadcasts;
DROP TABLE IF EXISTS messages;
DROP TABLE IF EXISTS audit_logs;
DROP TABLE IF EXISTS profiles;
DROP TABLE IF EXISTS units;
DROP TABLE IF EXISTS roles;

-- 1. إنشاء جدول الأدوار (roles)
CREATE TABLE roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. إنشاء جدول الوحدات التنظيمية (units)
CREATE TABLE units (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL UNIQUE,
  type VARCHAR(50) NOT NULL, -- إدارة، فرع، مكتب، قسم
  description TEXT,
  parent_id UUID REFERENCES units(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. إنشاء جدول الملفات الشخصية (profiles)
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users ON DELETE CASCADE,
  full_name TEXT,
  email TEXT,
  role VARCHAR(50),
  role_id UUID REFERENCES roles(id),
  unit_id UUID REFERENCES units(id),
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. إنشاء جدول سجلات النظام (audit_logs)
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id),
  action VARCHAR(50) NOT NULL,
  entity_type VARCHAR(50) NOT NULL,
  entity_id UUID,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. إنشاء جدول الصلاحيات (permissions)
CREATE TABLE permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  resource VARCHAR(50) NOT NULL,
  action VARCHAR(50) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(resource, action)
);

-- 6. إنشاء جدول العلاقة بين الأدوار والصلاحيات (role_permissions)
CREATE TABLE role_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role_id, permission_id)
);

-- 7. إنشاء جدول العلاقة بين المستخدمين والصلاحيات (user_permissions)
CREATE TABLE user_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, permission_id)
);

-- 8. إنشاء جدول المراسلات (messages)
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sender_id UUID NOT NULL REFERENCES profiles(id),
  recipient_id UUID NOT NULL REFERENCES profiles(id),
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 9. إنشاء جدول التعميمات (broadcasts)
CREATE TABLE broadcasts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sender_id UUID NOT NULL REFERENCES profiles(id),
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. إنشاء جدول مستلمي التعميمات (broadcast_recipients)
CREATE TABLE broadcast_recipients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  broadcast_id UUID NOT NULL REFERENCES broadcasts(id) ON DELETE CASCADE,
  recipient_id UUID NOT NULL REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(broadcast_id, recipient_id)
);

-- 11. إنشاء جدول قراء التعميمات (broadcast_readers)
CREATE TABLE broadcast_readers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  broadcast_id UUID NOT NULL REFERENCES broadcasts(id) ON DELETE CASCADE,
  reader_id UUID NOT NULL REFERENCES profiles(id),
  read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(broadcast_id, reader_id)
);

-- 12. إنشاء جدول قنوات الدردشة (chat_channels)
CREATE TABLE chat_channels (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT,
  description TEXT,
  is_group BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 13. إنشاء جدول رسائل الدردشة (chat_messages)
CREATE TABLE chat_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  channel_id UUID NOT NULL REFERENCES chat_channels(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES profiles(id),
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 14. إنشاء جدول مرفقات الرسائل (message_attachments)
CREATE TABLE message_attachments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_type TEXT,
  file_size INTEGER,
  file_path TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 15. إنشاء جدول المستندات (documents)
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  content TEXT,
  status VARCHAR(50) DEFAULT 'draft',
  created_by UUID NOT NULL REFERENCES profiles(id),
  unit_id UUID REFERENCES units(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 16. إنشاء جدول مرفقات المستندات (document_attachments)
CREATE TABLE document_attachments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_type TEXT,
  file_size INTEGER,
  file_path TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 17. إنشاء جدول قراء المستندات (document_readers)
CREATE TABLE document_readers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  reader_id UUID NOT NULL REFERENCES profiles(id),
  read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(document_id, reader_id)
);

-- إضافة الأدوار الأساسية
INSERT INTO roles (name, description) 
VALUES ('admin', 'مدير النظام مع كامل الصلاحيات');

INSERT INTO roles (name, description) 
VALUES ('manager', 'مدير مع صلاحيات إدارية محدودة');

INSERT INTO roles (name, description) 
VALUES ('user', 'مستخدم عادي مع صلاحيات محدودة');

-- إضافة الوحدات التنظيمية الأساسية
INSERT INTO units (name, type, description) 
VALUES ('الإدارة العامة', 'إدارة', 'الإدارة العامة للمؤسسة');

INSERT INTO units (name, type, description) 
VALUES ('إدارة الموارد البشرية', 'إدارة', 'إدارة شؤون الموظفين والتوظيف');

INSERT INTO units (name, type, description) 
VALUES ('إدارة تقنية المعلومات', 'إدارة', 'إدارة البنية التحتية التقنية والدعم الفني');

-- إضافة صلاحيات المستندات
INSERT INTO permissions (name, description, resource, action)
VALUES ('documents:create', 'إنشاء مستندات جديدة', 'documents', 'create');

INSERT INTO permissions (name, description, resource, action)
VALUES ('documents:read', 'قراءة المستندات', 'documents', 'read');

INSERT INTO permissions (name, description, resource, action)
VALUES ('documents:update', 'تحديث المستندات', 'documents', 'update');

INSERT INTO permissions (name, description, resource, action)
VALUES ('documents:delete', 'حذف المستندات', 'documents', 'delete');

-- صلاحيات المستخدمين
INSERT INTO permissions (name, description, resource, action)
VALUES ('users:create', 'إنشاء مستخدمين جدد', 'users', 'create');

INSERT INTO permissions (name, description, resource, action)
VALUES ('users:read', 'قراءة بيانات المستخدمين', 'users', 'read');

INSERT INTO permissions (name, description, resource, action)
VALUES ('users:update', 'تحديث بيانات المستخدمين', 'users', 'update');

INSERT INTO permissions (name, description, resource, action)
VALUES ('users:delete', 'حذف المستخدمين', 'users', 'delete');

-- صلاحيات المراسلات
INSERT INTO permissions (name, description, resource, action)
VALUES ('messages:create', 'إنشاء مراسلات جديدة', 'messages', 'create');

INSERT INTO permissions (name, description, resource, action)
VALUES ('messages:read', 'قراءة المراسلات', 'messages', 'read');

INSERT INTO permissions (name, description, resource, action)
VALUES ('messages:update', 'تحديث المراسلات', 'messages', 'update');

INSERT INTO permissions (name, description, resource, action)
VALUES ('messages:delete', 'حذف المراسلات', 'messages', 'delete');

-- صلاحيات التعميمات
INSERT INTO permissions (name, description, resource, action)
VALUES ('broadcasts:create', 'إنشاء تعميمات جديدة', 'broadcasts', 'create');

INSERT INTO permissions (name, description, resource, action)
VALUES ('broadcasts:read', 'قراءة التعميمات', 'broadcasts', 'read');

INSERT INTO permissions (name, description, resource, action)
VALUES ('broadcasts:update', 'تحديث التعميمات', 'broadcasts', 'update');

INSERT INTO permissions (name, description, resource, action)
VALUES ('broadcasts:delete', 'حذف التعميمات', 'broadcasts', 'delete');

-- صلاحيات الهيكل التنظيمي
INSERT INTO permissions (name, description, resource, action)
VALUES ('organization:create', 'إنشاء وحدات تنظيمية جديدة', 'organization', 'create');

INSERT INTO permissions (name, description, resource, action)
VALUES ('organization:read', 'قراءة الهيكل التنظيمي', 'organization', 'read');

INSERT INTO permissions (name, description, resource, action)
VALUES ('organization:update', 'تحديث الهيكل التنظيمي', 'organization', 'update');

INSERT INTO permissions (name, description, resource, action)
VALUES ('organization:delete', 'حذف وحدات من الهيكل التنظيمي', 'organization', 'delete');

-- صلاحيات النظام
INSERT INTO permissions (name, description, resource, action)
VALUES ('system:settings', 'تعديل إعدادات النظام', 'system', 'settings');

INSERT INTO permissions (name, description, resource, action)
VALUES ('system:logs', 'عرض سجلات النظام', 'system', 'logs');
