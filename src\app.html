<!doctype html>
<html lang="ar" dir="rtl">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="stylesheet" href="%sveltekit.assets%/lib/styles/print.css" media="print" />
		%sveltekit.head%
		<script>
			// تطبيق الوضع المظلم عند تحميل الصفحة
			(function() {
				try {
					const savedTheme = localStorage.getItem('theme');
					if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
						document.documentElement.classList.add('dark');
						document.body.classList.add('dark');

						// تطبيق الوضع المظلم على جميع العناصر
						setTimeout(() => {
							document.querySelectorAll('.bg-white').forEach(el => {
								el.classList.add('dark-bg');
							});

							document.querySelectorAll('.bg-card').forEach(el => {
								el.classList.add('dark-card');
							});
						}, 100);
					} else {
						document.documentElement.classList.remove('dark');
						document.body.classList.remove('dark');
					}
				} catch (e) {
					console.error('Error applying theme:', e);
				}
			})();
		</script>
		<style>
			.dark {
				background-color: #111827;
				color: #f3f4f6;
			}

			.dark body {
				background-color: #111827;
				color: #f3f4f6;
			}
		</style>
	</head>
	<body data-sveltekit-preload-data="hover" class="rtl">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
