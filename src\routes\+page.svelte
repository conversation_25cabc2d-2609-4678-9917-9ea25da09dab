<script lang="ts">
  // تحديد الوقت الحالي للترحيب
  let greeting = '';
  const hour = new Date().getHours();

  if (hour >= 5 && hour < 12) {
    greeting = 'صباح الخير';
  } else if (hour >= 12 && hour < 17) {
    greeting = 'مساء الخير';
  } else {
    greeting = 'مساء الخير';
  }

  // ميزات النظام
  const features = [
    {
      title: 'إدارة المستندات',
      description: 'أرشفة وتنظيم وتصنيف جميع المستندات بطريقة سهلة وفعالة',
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><path d="M16 13H8"/><path d="M16 17H8"/><path d="M10 9H8"/></svg>`
    },
    {
      title: 'المراسلات الداخلية',
      description: 'نظام مراسلات داخلي متكامل لتسهيل التواصل بين الإدارات والأقسام',
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 5H3v14h18V5z"/><path d="m3 5 9 9 9-9"/></svg>`
    },
    {
      title: 'الهيكل التنظيمي',
      description: 'إدارة الهيكل التنظيمي للمؤسسة بطريقة مرنة وسهلة',
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 3H3v7h18V3z"/><path d="M21 14h-5v7h5v-7z"/><path d="M8 14H3v7h5v-7z"/><path d="M14.5 14.5h-5v7h5v-7z"/></svg>`
    },
    {
      title: 'إدارة المستخدمين',
      description: 'نظام صلاحيات متكامل لإدارة المستخدمين والصلاحيات',
      icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>`
    }
  ];
</script>

<div class="min-h-screen flex flex-col">
  <!-- Hero Section -->
  <div class="bg-gradient-to-b from-primary/10 to-background py-16">
    <div class="container mx-auto px-4">
      <div class="flex flex-col md:flex-row items-center justify-between gap-8">
        <div class="md:w-1/2 text-center md:text-right">
          <h1 class="text-3xl md:text-5xl font-bold mb-4">
            <span class="text-primary">{greeting}!</span>
            <br />
            نظام الأرشفة الإلكترونية
          </h1>
          <p class="text-xl text-muted-foreground mb-8">
            نظام متكامل لإدارة المستندات والمراسلات الداخلية بكفاءة عالية
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
            <a
              href="/login"
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-12 px-6 py-2"
            >
              تسجيل الدخول
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/><polyline points="10 17 15 12 10 7"/><line x1="15" y1="12" x2="3" y2="12"/></svg>
            </a>
          </div>
        </div>
        <div class="md:w-1/2">
          <div class="bg-card rounded-lg shadow-lg p-6 border border-border/50">
            <div class="text-center p-8">
              <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto text-primary mb-4"><path d="M16 6h3a1 1 0 0 1 1 1v11a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2V5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v1Z"/><path d="M11 8H7a1 1 0 0 0-1 1v10a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V9a1 1 0 0 0-1-1Z"/></svg>
              <h2 class="text-2xl font-bold mb-2">نظام الأرشفة الإلكترونية</h2>
              <p class="text-muted-foreground">
                تنظيم وإدارة المستندات والمراسلات بكفاءة عالية
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Features Section -->
  <div class="py-16 bg-muted/30">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold mb-4">مميزات النظام</h2>
        <p class="text-muted-foreground max-w-2xl mx-auto">
          يوفر نظام الأرشفة الإلكترونية مجموعة متكاملة من الميزات لتسهيل إدارة المستندات والمراسلات الداخلية
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {#each features as feature}
          <div class="bg-card rounded-lg shadow-md p-6 border border-border/50 transition-all duration-300 hover:shadow-lg hover:border-primary/50">
            <div class="text-primary mb-4" style="height: 48px; width: 48px;">
              {@html feature.icon}
            </div>
            <h3 class="text-xl font-bold mb-2">{feature.title}</h3>
            <p class="text-muted-foreground">{feature.description}</p>
          </div>
        {/each}
      </div>
    </div>
  </div>

  <!-- CTA Section -->
  <div class="py-16 bg-primary/5">
    <div class="container mx-auto px-4">
      <div class="bg-card rounded-lg shadow-lg p-8 border border-border/50 text-center">
        <h2 class="text-3xl font-bold mb-4">ابدأ استخدام النظام الآن</h2>
        <p class="text-muted-foreground mb-8 max-w-2xl mx-auto">
          قم بتسجيل الدخول للوصول إلى نظام الأرشفة الإلكترونية واستفد من جميع الميزات المتاحة
        </p>
        <a
          href="/login"
          class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-12 px-8 py-2"
        >
          تسجيل الدخول
        </a>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="py-8 bg-muted/20 border-t border-border/50">
    <div class="container mx-auto px-4 text-center">
      <p class="text-muted-foreground">
        &copy; {new Date().getFullYear()} نظام الأرشفة الإلكترونية. جميع الحقوق محفوظة.
      </p>
    </div>
  </footer>
</div>
