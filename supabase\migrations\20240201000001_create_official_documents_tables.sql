-- إنشاء جداول الوثائق الرسمية

-- إنشاء نوع enum لحالة الوثيقة
CREATE TYPE public.document_status AS ENUM (
  'draft',      -- مسودة
  'pending',    -- قيد الانتظار
  'approved',   -- معتمدة
  'rejected',   -- مرفوضة
  'archived'    -- مؤرشفة
);

-- إنشاء نوع enum لنوع الوثيقة
CREATE TYPE public.document_type AS ENUM (
  'letter',     -- خطاب
  'memo',       -- مذكرة
  'report',     -- تقرير
  'decision',   -- قرار
  'certificate', -- شهادة
  'contract',   -- عقد
  'other'       -- أخرى
);

-- إنشاء جدول الوثائق الرسمية
CREATE TABLE IF NOT EXISTS public.official_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  reference_number TEXT,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  document_type public.document_type NOT NULL DEFAULT 'letter',
  status public.document_status NOT NULL DEFAULT 'draft',
  
  -- معلومات المنشئ والوحدة
  creator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  unit_id UUID REFERENCES public.units(id) ON DELETE SET NULL,
  
  -- معلومات المستلم
  recipient_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  recipient_unit_id UUID REFERENCES public.units(id) ON DELETE SET NULL,
  recipient_external TEXT, -- للمستلمين الخارجيين
  
  -- الشعارات والأختام
  unit_logo TEXT,
  unit_stamp TEXT,
  right_logo TEXT,
  left_logo TEXT,
  
  -- التوقيع الإلكتروني
  is_signed BOOLEAN DEFAULT FALSE,
  signature JSONB,
  password_protected BOOLEAN DEFAULT FALSE,
  
  -- الملفات المرفقة
  attachments JSONB,
  
  -- الأوقات
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  approved_at TIMESTAMP WITH TIME ZONE,
  
  -- معلومات إضافية
  metadata JSONB
);

-- إنشاء جدول تاريخ الوثائق
CREATE TABLE IF NOT EXISTS public.document_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES public.official_documents(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  action TEXT NOT NULL, -- مثل: إنشاء، تعديل، اعتماد، رفض، أرشفة
  status public.document_status,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول تعليقات الوثائق
CREATE TABLE IF NOT EXISTS public.document_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES public.official_documents(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول مشاركة الوثائق
CREATE TABLE IF NOT EXISTS public.document_shares (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES public.official_documents(id) ON DELETE CASCADE,
  shared_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  shared_with UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  shared_with_unit UUID REFERENCES public.units(id) ON DELETE CASCADE,
  shared_with_email TEXT,
  access_level TEXT NOT NULL, -- مثل: قراءة، تعديل، اعتماد
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول قوالب الوثائق
CREATE TABLE IF NOT EXISTS public.document_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  content TEXT NOT NULL,
  document_type public.document_type NOT NULL DEFAULT 'letter',
  creator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  unit_id UUID REFERENCES public.units(id) ON DELETE SET NULL,
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول إعدادات المنظمة
CREATE TABLE IF NOT EXISTS public.organization_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  logo TEXT,
  right_logo TEXT,
  left_logo TEXT,
  default_stamp TEXT,
  address TEXT,
  phone TEXT,
  email TEXT,
  website TEXT,
  footer_text TEXT,
  header_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة أعمدة للوحدات التنظيمية
ALTER TABLE public.units
ADD COLUMN IF NOT EXISTS logo TEXT,
ADD COLUMN IF NOT EXISTS stamp TEXT,
ADD COLUMN IF NOT EXISTS right_logo TEXT,
ADD COLUMN IF NOT EXISTS left_logo TEXT,
ADD COLUMN IF NOT EXISTS reference_prefix TEXT,
ADD COLUMN IF NOT EXISTS address TEXT,
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS email TEXT;
