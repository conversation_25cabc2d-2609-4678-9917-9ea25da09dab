# دليل التشخيص السريع لمشكلة التوقيع

## المشكلة
بعد توقيع المستند، لا يزال المرفق يعرض "في انتظار التوقيع الإلكتروني" بدلاً من التوقيع الفعلي.

## خطوات التشخيص السريع

### 1. فتح Developer Console
- اضغط `F12` في المتصفح
- انتقل إلى تبويب `Console`

### 2. تشغيل التوقيع
- قم بتوقيع المستند
- راقب الرسائل في Console

### 3. البحث عن الرسائل المهمة

#### أ. رسائل التشخيص الأساسية:
```
🔍 بدء تشخيص مشكلة التوقيع...
🔍 تشخيص محتوى المرفق قبل التحديث...
🚀 تشغيل الاختبار السريع...
```

#### ب. رسائل البحث عن النصوص:
```
البحث 1 - "في انتظار التوقيع الإلكتروني": ✅ موجود / ❌ غير موجود
البحث 2 - "سيتم عرض التوقيع الإلكتروني هنا": ✅ موجود / ❌ غير موجود
البحث 3 - "background-color: #fffbeb": ✅ موجود / ❌ غير موجود
```

#### ج. رسائل اختبار الأنماط:
```
✅ تم العثور على مطابقة بالنمط 1: 1 مطابقة
أو
❌ لم يتم العثور على مطابقات بأي نمط
```

#### د. رسائل الاستبدال:
```
✅ تم الاستبدال بالنمط 1:
- طول المحتوى قبل الاستبدال: 15420
- طول المحتوى بعد الاستبدال: 16850
- تم الاستبدال: true
```

### 4. تحليل النتائج

#### إذا كان "غير موجود" لجميع النصوص:
**المشكلة**: المحتوى لا يحتوي على مساحة التوقيع المؤقتة
**الحل**: 
1. تحقق من إنشاء المستند الأصلي
2. تأكد من أن مساحة التوقيع تم إضافتها عند الإنشاء

#### إذا كان "موجود" لكن "لم يتم العثور على مطابقات":
**المشكلة**: النمط المستخدم للبحث لا يطابق HTML الفعلي
**الحل**: 
1. راجع السياق المحيط بالنص في Console
2. قارن مع الأنماط المستخدمة في الكود

#### إذا كان "تم الاستبدال: false":
**المشكلة**: فشل في استبدال النص
**الحل**:
1. تحقق من صحة النمط
2. راجع HTML المحيط بالنص

#### إذا كان "تم الاستبدال: true" لكن التوقيع لا يظهر:
**المشكلة**: فشل في حفظ المحتوى المحدث
**الحل**:
1. تحقق من رسائل حفظ Storage
2. راجع صلاحيات Storage
3. تأكد من تحديث قاعدة البيانات

### 5. الحلول السريعة

#### أ. إعادة تحميل الصفحة:
```javascript
// في Browser Console
window.location.reload();
```

#### ب. مسح Cache:
- اضغط `Ctrl + Shift + R` (Windows/Linux)
- اضغط `Cmd + Shift + R` (Mac)

#### ج. اختبار Storage يدوياً:
```javascript
// في Browser Console
const { data, error } = await supabase.storage
  .from('saadabujnah')
  .list('attachments', { limit: 10 });
console.log('Storage files:', data);
```

#### د. اختبار جلب المحتوى:
```javascript
// في Browser Console (استبدل PATH بالمسار الفعلي)
const { data, error } = await supabase.storage
  .from('saadabujnah')
  .download('PATH');
console.log('Downloaded content:', data);
```

### 6. أنماط البحث المستخدمة

#### النمط 1 (الأصلي):
```regex
/<div style="background-color: #fffbeb;[^>]*>[\s\S]*?في انتظار التوقيع الإلكتروني[\s\S]*?<\/div>\s*<\/div>/g
```

#### النمط 2 (المرن):
```regex
/<div[^>]*background-color:\s*#fffbeb[^>]*>[\s\S]*?في انتظار التوقيع الإلكتروني[\s\S]*?<\/div>/g
```

#### النمط 3 (الكامل):
```regex
/<div style="background-color: #fffbeb; padding: 20px; border: 2px solid #f59e0b; border-radius: 8px; text-align: center;">[\s\S]*?سيتم عرض التوقيع الإلكتروني هنا بعد إتمام عملية التوقيع[\s\S]*?<\/div>/g
```

### 7. HTML المتوقع لمساحة التوقيع

#### مساحة التوقيع المؤقتة:
```html
<div style="background-color: #fffbeb; padding: 20px; border: 2px solid #f59e0b; border-radius: 8px; text-align: center;">
  <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px; gap: 10px;">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="12" y1="8" x2="12" y2="12"></line>
      <line x1="12" y1="16" x2="12.01" y2="16"></line>
    </svg>
    <h4 style="color: #92400e; margin: 0; font-size: 18px; font-weight: bold;">في انتظار التوقيع الإلكتروني</h4>
  </div>
  <p style="color: #78350f; margin: 0; font-size: 14px;">سيتم عرض التوقيع الإلكتروني هنا بعد إتمام عملية التوقيع</p>
</div>
```

#### التوقيع الفعلي:
```html
<div style="background-color: #f0fdf4; padding: 20px; border: 2px solid #10b981; border-radius: 8px; text-align: center;">
  <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px; gap: 10px;">
    <h4 style="color: #065f46; margin: 0; font-size: 18px; font-weight: bold;">توقيع إلكتروني معتمد</h4>
  </div>
  <!-- بيانات التوقيع -->
</div>
```

### 8. اختبارات إضافية

#### أ. اختبار قاعدة البيانات:
```sql
-- في Supabase SQL Editor
SELECT 
  m.id,
  m.attachment->>'title' as title,
  m.attachment_content_url,
  LENGTH(m.attachment->>'content') as content_length_db
FROM messages m 
WHERE m.attachment->>'signature_request_id' = 'SIGNATURE_REQUEST_ID';
```

#### ب. اختبار Storage:
```sql
-- في Supabase SQL Editor
SELECT name, created_at, updated_at 
FROM storage.objects 
WHERE bucket_id = 'saadabujnah' 
AND name LIKE '%attachments%'
ORDER BY created_at DESC 
LIMIT 10;
```

### 9. نصائح للمطورين

#### أ. إضافة تسجيل مخصص:
```javascript
// في الكود
console.log('🔍 Custom Debug:', {
  messageId: message.id,
  hasAttachment: !!message.attachment,
  hasStorageUrl: !!message.attachment_content_url,
  contentLength: content?.length || 0
});
```

#### ب. حفظ المحتوى للفحص:
```javascript
// في Browser Console
localStorage.setItem('debugContent', content);
// للاسترجاع لاحقاً
const savedContent = localStorage.getItem('debugContent');
```

#### ج. اختبار الاستبدال يدوياً:
```javascript
// في Browser Console
const testContent = 'محتوى يحتوي على في انتظار التوقيع الإلكتروني';
const result = testContent.replace(/في انتظار التوقيع الإلكتروني/g, 'توقيع جديد');
console.log('نتيجة الاستبدال:', result);
```

### 10. الدعم والمساعدة

إذا استمرت المشكلة:
1. جمع جميع رسائل Console
2. أخذ لقطة شاشة للمشكلة
3. تسجيل خطوات إعادة إنتاج المشكلة
4. مراجعة logs قاعدة البيانات
5. التحقق من Storage permissions
