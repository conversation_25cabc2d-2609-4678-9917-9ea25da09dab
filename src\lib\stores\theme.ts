import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// إنشاء مخزن للوضع المظلم
export const isDarkMode = writable<boolean>(false);

// تهيئة المخزن عند تحميل التطبيق
export function initTheme() {
  if (browser) {
    // تحقق من تفضيل المستخدم المحفوظ
    const savedTheme = localStorage.getItem('theme');

    if (savedTheme === 'dark') {
      enableDarkMode();
    } else if (savedTheme === 'light') {
      disableDarkMode();
    } else {
      // تحقق من تفضيل النظام
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (prefersDark) {
        enableDarkMode();
      } else {
        disableDarkMode();
      }
    }

    // إضافة مستمع للتغييرات في وضع النظام
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        if (e.matches) {
          enableDarkMode();
        } else {
          disableDarkMode();
        }
      }
    });
  }
}

// تبديل الوضع المظلم
export function toggleTheme() {
  if (browser) {
    let currentValue: boolean;
    isDarkMode.update(value => {
      currentValue = value;
      return value;
    });

    if (currentValue) {
      disableDarkMode();
    } else {
      enableDarkMode();
    }
  }
}

// تفعيل الوضع المظلم
export function enableDarkMode() {
  if (browser) {
    document.documentElement.classList.add('dark');
    document.body.classList.add('dark');
    localStorage.setItem('theme', 'dark');
    isDarkMode.set(true);

    // تطبيق الوضع المظلم على جميع العناصر
    document.querySelectorAll('.bg-white').forEach(el => {
      el.classList.add('dark-bg');
    });

    document.querySelectorAll('.bg-card').forEach(el => {
      el.classList.add('dark-card');
    });
  }
}

// تعطيل الوضع المظلم
export function disableDarkMode() {
  if (browser) {
    document.documentElement.classList.remove('dark');
    document.body.classList.remove('dark');
    localStorage.setItem('theme', 'light');
    isDarkMode.set(false);

    // إزالة الوضع المظلم من جميع العناصر
    document.querySelectorAll('.dark-bg').forEach(el => {
      el.classList.remove('dark-bg');
    });

    document.querySelectorAll('.dark-card').forEach(el => {
      el.classList.remove('dark-card');
    });
  }
}
