-- ملف إعداد قاعدة البيانات الكاملة
-- يقوم هذا الملف بإنشاء جميع الجداول والعلاقات اللازمة لنظام الأرشفة الإلكتروني

-- التأكد من وجود امتداد uuid-ossp
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 0. التحقق من وجود جدول roles وإنشائه إذا لم يكن موجودًا
DO $$
BEGIN
  -- التحقق من وجود جدول roles
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'roles'
  ) THEN
    -- إنشاء جدول الأدوار
    CREATE TABLE roles (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      name VARCHAR(50) NOT NULL UNIQUE,
      description TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- إضافة الأدوار الأساسية
    INSERT INTO roles (name, description)
    VALUES ('admin', 'مدير النظام مع كامل الصلاحيات');

    INSERT INTO roles (name, description)
    VALUES ('manager', 'مدير مع صلاحيات إدارية محدودة');

    INSERT INTO roles (name, description)
    VALUES ('user', 'مستخدم عادي مع صلاحيات محدودة');
  END IF;

  -- التحقق من وجود جدول permissions
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'permissions'
  ) THEN
    -- إنشاء جدول الصلاحيات
    CREATE TABLE permissions (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      name VARCHAR(100) NOT NULL UNIQUE,
      description TEXT,
      resource VARCHAR(50),
      action VARCHAR(50),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- إضافة قيد فريد على resource و action
    ALTER TABLE permissions ADD CONSTRAINT permissions_resource_action_key UNIQUE(resource, action);
  ELSE
    -- التحقق من وجود عمود resource وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'permissions'
      AND column_name = 'resource'
    ) THEN
      ALTER TABLE permissions ADD COLUMN resource VARCHAR(50);
    END IF;

    -- التحقق من وجود عمود action وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'permissions'
      AND column_name = 'action'
    ) THEN
      ALTER TABLE permissions ADD COLUMN action VARCHAR(50);
    END IF;

    -- التحقق من وجود قيد فريد على resource و action وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM pg_constraint WHERE conname = 'permissions_resource_action_key'
    ) THEN
      ALTER TABLE permissions ADD CONSTRAINT permissions_resource_action_key UNIQUE(resource, action);
    END IF;
  END IF;

  -- التحقق من وجود جدول role_permissions
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'role_permissions'
  ) THEN
    -- إنشاء جدول العلاقة بين الأدوار والصلاحيات
    CREATE TABLE role_permissions (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
      permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(role_id, permission_id)
    );
  ELSE
    -- التحقق من وجود عمود role_id وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'role_permissions'
      AND column_name = 'role_id'
    ) THEN
      ALTER TABLE role_permissions ADD COLUMN role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE;
    END IF;

    -- التحقق من وجود عمود permission_id وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'role_permissions'
      AND column_name = 'permission_id'
    ) THEN
      ALTER TABLE role_permissions ADD COLUMN permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE;
    END IF;

    -- التحقق من وجود قيد فريد على role_id و permission_id وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM pg_constraint
      WHERE conrelid = 'role_permissions'::regclass
      AND contype = 'u'
      AND array_to_string(conkey, ',') =
          array_to_string((SELECT array_agg(attnum)
                          FROM pg_attribute
                          WHERE attrelid = 'role_permissions'::regclass
                          AND attname IN ('role_id', 'permission_id')), ',')
    ) THEN
      BEGIN
        ALTER TABLE role_permissions ADD CONSTRAINT role_permissions_role_id_permission_id_key UNIQUE(role_id, permission_id);
      EXCEPTION WHEN OTHERS THEN
        -- تجاهل الخطأ إذا كان هناك مشكلة في إضافة القيد
        RAISE NOTICE 'Could not add unique constraint to role_permissions: %', SQLERRM;
      END;
    END IF;
  END IF;
END $$;

-- 1. التحقق من وجود جدول units
DO $$
BEGIN
  -- التحقق من وجود الجدول
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'units'
  ) THEN
    -- إنشاء جدول الوحدات التنظيمية
    CREATE TABLE units (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      name VARCHAR(100) NOT NULL,
      type VARCHAR(50) NOT NULL, -- إدارة، فرع، مكتب، قسم
      description TEXT,
      parent_id UUID REFERENCES units(id),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- إضافة قيد فريد على اسم الوحدة
    ALTER TABLE units ADD CONSTRAINT units_name_unique UNIQUE (name);
  ELSE
    -- التحقق من وجود عمود description وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'units'
      AND column_name = 'description'
    ) THEN
      ALTER TABLE units ADD COLUMN description TEXT;
    END IF;

    -- التحقق من وجود عمود type وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'units'
      AND column_name = 'type'
    ) THEN
      ALTER TABLE units ADD COLUMN type VARCHAR(50) NOT NULL DEFAULT 'إدارة';
    END IF;

    -- التحقق من وجود عمود parent_id وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'units'
      AND column_name = 'parent_id'
    ) THEN
      ALTER TABLE units ADD COLUMN parent_id UUID REFERENCES units(id);
    END IF;

    -- التحقق من وجود عمود created_at وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'units'
      AND column_name = 'created_at'
    ) THEN
      ALTER TABLE units ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;

    -- التحقق من وجود عمود updated_at وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'units'
      AND column_name = 'updated_at'
    ) THEN
      ALTER TABLE units ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;

    -- التحقق من وجود قيد فريد على اسم الوحدة وإضافته إذا لم يكن موجودًا
    IF NOT EXISTS (
      SELECT FROM pg_constraint WHERE conname = 'units_name_unique'
    ) THEN
      ALTER TABLE units ADD CONSTRAINT units_name_unique UNIQUE (name);
    END IF;
  END IF;
END $$;

-- 2. إضافة عمود unit_id إلى جدول profiles إذا لم يكن موجودًا
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'profiles'
    AND column_name = 'unit_id'
  ) THEN
    ALTER TABLE profiles ADD COLUMN unit_id UUID REFERENCES units(id);
  END IF;
END $$;

-- 3. إضافة بيانات أولية للوحدات التنظيمية
-- الإدارات الرئيسية
INSERT INTO units (name, type, description)
VALUES ('الإدارة العامة', 'إدارة', 'الإدارة العامة للمؤسسة')
ON CONFLICT (name) DO NOTHING;

INSERT INTO units (name, type, description)
VALUES ('إدارة الموارد البشرية', 'إدارة', 'إدارة شؤون الموظفين والتوظيف')
ON CONFLICT (name) DO NOTHING;

INSERT INTO units (name, type, description)
VALUES ('إدارة تقنية المعلومات', 'إدارة', 'إدارة البنية التحتية التقنية والدعم الفني')
ON CONFLICT (name) DO NOTHING;

INSERT INTO units (name, type, description)
VALUES ('إدارة الشؤون المالية', 'إدارة', 'إدارة الميزانية والمحاسبة')
ON CONFLICT (name) DO NOTHING;

INSERT INTO units (name, type, description)
VALUES ('إدارة المشاريع', 'إدارة', 'إدارة تخطيط وتنفيذ المشاريع')
ON CONFLICT (name) DO NOTHING;

-- إضافة الفروع
DO $$
DECLARE
  hr_id UUID;
  it_id UUID;
  finance_id UUID;
  projects_id UUID;
BEGIN
  -- الحصول على معرفات الإدارات
  SELECT id INTO hr_id FROM units WHERE name = 'إدارة الموارد البشرية';
  SELECT id INTO it_id FROM units WHERE name = 'إدارة تقنية المعلومات';
  SELECT id INTO finance_id FROM units WHERE name = 'إدارة الشؤون المالية';
  SELECT id INTO projects_id FROM units WHERE name = 'إدارة المشاريع';

  -- إضافة فروع إدارة الموارد البشرية
  IF hr_id IS NOT NULL THEN
    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم التوظيف', 'قسم', 'قسم استقطاب وتوظيف الكفاءات', hr_id)
    ON CONFLICT (name) DO NOTHING;

    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم التدريب والتطوير', 'قسم', 'قسم تدريب وتطوير الموظفين', hr_id)
    ON CONFLICT (name) DO NOTHING;

    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم شؤون الموظفين', 'قسم', 'قسم متابعة شؤون الموظفين', hr_id)
    ON CONFLICT (name) DO NOTHING;
  END IF;

  -- إضافة فروع إدارة تقنية المعلومات
  IF it_id IS NOT NULL THEN
    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم البنية التحتية', 'قسم', 'قسم إدارة البنية التحتية التقنية', it_id)
    ON CONFLICT (name) DO NOTHING;

    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم تطوير البرمجيات', 'قسم', 'قسم تطوير وصيانة البرمجيات', it_id)
    ON CONFLICT (name) DO NOTHING;

    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم الدعم الفني', 'قسم', 'قسم تقديم الدعم الفني للمستخدمين', it_id)
    ON CONFLICT (name) DO NOTHING;

    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم أمن المعلومات', 'قسم', 'قسم حماية وأمن المعلومات', it_id)
    ON CONFLICT (name) DO NOTHING;
  END IF;

  -- إضافة فروع إدارة الشؤون المالية
  IF finance_id IS NOT NULL THEN
    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم المحاسبة', 'قسم', 'قسم المحاسبة والتدقيق المالي', finance_id)
    ON CONFLICT (name) DO NOTHING;

    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم الميزانية', 'قسم', 'قسم إعداد ومتابعة الميزانية', finance_id)
    ON CONFLICT (name) DO NOTHING;

    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم المشتريات', 'قسم', 'قسم إدارة المشتريات والعقود', finance_id)
    ON CONFLICT (name) DO NOTHING;
  END IF;

  -- إضافة فروع إدارة المشاريع
  IF projects_id IS NOT NULL THEN
    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم تخطيط المشاريع', 'قسم', 'قسم تخطيط وجدولة المشاريع', projects_id)
    ON CONFLICT (name) DO NOTHING;

    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم تنفيذ المشاريع', 'قسم', 'قسم متابعة تنفيذ المشاريع', projects_id)
    ON CONFLICT (name) DO NOTHING;

    INSERT INTO units (name, type, description, parent_id)
    VALUES ('قسم ضمان الجودة', 'قسم', 'قسم ضمان جودة المشاريع', projects_id)
    ON CONFLICT (name) DO NOTHING;
  END IF;
END $$;

-- 4. إضافة المكاتب الفرعية
INSERT INTO units (name, type, description)
VALUES ('مكتب الرياض', 'مكتب', 'المكتب الفرعي في مدينة الرياض')
ON CONFLICT (name) DO NOTHING;

INSERT INTO units (name, type, description)
VALUES ('مكتب جدة', 'مكتب', 'المكتب الفرعي في مدينة جدة')
ON CONFLICT (name) DO NOTHING;

INSERT INTO units (name, type, description)
VALUES ('مكتب الدمام', 'مكتب', 'المكتب الفرعي في مدينة الدمام')
ON CONFLICT (name) DO NOTHING;

-- 5. إضافة صلاحيات إدارة الوحدات التنظيمية
INSERT INTO permissions (name, description, resource, action)
VALUES ('organization:create', 'إنشاء وحدات تنظيمية جديدة', 'organization', 'create')
ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action)
VALUES ('organization:read', 'عرض الهيكل التنظيمي', 'organization', 'read')
ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action)
VALUES ('organization:update', 'تعديل الوحدات التنظيمية', 'organization', 'update')
ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action)
VALUES ('organization:delete', 'حذف الوحدات التنظيمية', 'organization', 'delete')
ON CONFLICT (name) DO NOTHING;

-- 6. ربط صلاحيات الوحدات التنظيمية بدور المدير
DO $$
DECLARE
  admin_role_id UUID;
  manager_role_id UUID;
  perm_id UUID;
  perm_cursor CURSOR FOR
    SELECT id FROM permissions WHERE resource = 'organization';
  perm_cursor_manager CURSOR FOR
    SELECT id FROM permissions
    WHERE resource = 'organization' AND action IN ('read', 'update');
BEGIN
  -- الحصول على معرف دور المدير
  SELECT id INTO admin_role_id FROM roles WHERE name = 'admin';
  SELECT id INTO manager_role_id FROM roles WHERE name = 'manager';

  -- ربط صلاحيات الوحدات التنظيمية بدور المدير
  IF admin_role_id IS NOT NULL THEN
    -- إضافة جميع صلاحيات الوحدات التنظيمية لدور المدير
    OPEN perm_cursor;
    LOOP
      FETCH perm_cursor INTO perm_id;
      EXIT WHEN NOT FOUND;

      BEGIN
        INSERT INTO role_permissions (role_id, permission_id)
        VALUES (admin_role_id, perm_id);
      EXCEPTION WHEN OTHERS THEN
        -- تجاهل الأخطاء (مثل تكرار المفاتيح)
        RAISE NOTICE 'Could not insert permission for admin role: %', SQLERRM;
      END;
    END LOOP;
    CLOSE perm_cursor;
  END IF;

  -- ربط صلاحيات محددة بدور المدير
  IF manager_role_id IS NOT NULL THEN
    -- إضافة صلاحيات قراءة وتحديث الوحدات التنظيمية لدور المدير
    OPEN perm_cursor_manager;
    LOOP
      FETCH perm_cursor_manager INTO perm_id;
      EXIT WHEN NOT FOUND;

      BEGIN
        INSERT INTO role_permissions (role_id, permission_id)
        VALUES (manager_role_id, perm_id);
      EXCEPTION WHEN OTHERS THEN
        -- تجاهل الأخطاء (مثل تكرار المفاتيح)
        RAISE NOTICE 'Could not insert permission for manager role: %', SQLERRM;
      END;
    END LOOP;
    CLOSE perm_cursor_manager;
  END IF;
END $$;
