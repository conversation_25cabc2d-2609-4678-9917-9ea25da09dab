# إصلاح مشكلة عرض تفاصيل الرسائل

تم إصلاح مشكلة "حدث خطأ أثناء جلب بيانات الرسالة" التي تظهر عند محاولة عرض تفاصيل الرسالة.

## المشكلة

كان المستخدمون يواجهون خطأ عند محاولة عرض تفاصيل الرسالة، حيث تظهر رسالة "حدث خطأ أثناء جلب بيانات الرسالة" دون تفاصيل إضافية.

## الحل

تم اتخاذ نهج جديد لحل المشكلة من خلال:

1. إنشاء صفحة جديدة لعرض تفاصيل الرسالة بشكل مبسط ومباشر
2. إنشاء صفحة توجيه للانتقال إلى الصفحة الجديدة
3. تعديل صفحة المراسلات لاستخدام الصفحة الجديدة
4. إصلاح سياسات الأمان لجدول المراسلات

## التغييرات التي تم تنفيذها:

### 1. إنشاء صفحة جديدة لعرض تفاصيل الرسالة

تم إنشاء صفحة جديدة في المسار `src\routes\dashboard\messages\view\[id]\+page.svelte` تقوم بما يلي:

- جلب بيانات الرسالة بشكل مباشر من قاعدة البيانات
- جلب بيانات المرسل والمستقبل بشكل منفصل
- تحديث حالة القراءة للرسالة
- جلب الردود على الرسالة
- عرض محتوى الرسالة بشكل بسيط وواضح
- توفير إمكانية الرد على الرسالة

### 2. إنشاء صفحة توجيه

تم إنشاء صفحة توجيه في المسار `src\routes\dashboard\messages\redirect\[id]\+page.svelte` تقوم بتوجيه المستخدم إلى الصفحة الجديدة.

### 3. تعديل صفحة المراسلات

تم تعديل صفحة المراسلات في المسار `src\routes\dashboard\messages\+page.svelte` لاستخدام الصفحة الجديدة عند النقر على الرسالة.

### 4. إصلاح سياسات الأمان

تم إنشاء ملف SQL في المسار `src\lib\db\fix_messages_security.sql` يقوم بإصلاح سياسات الأمان لجدول المراسلات:

- تعطيل سياسات الأمان الحالية
- حذف السياسات الحالية
- إنشاء سياسات جديدة للقراءة والإنشاء والتحديث والحذف
- إعادة تفعيل سياسات الأمان
- تحديث ذاكرة التخزين المؤقت للمخطط

## كيفية تطبيق الحل:

1. قم بتنفيذ ملف `src\lib\db\fix_messages_security.sql` في قاعدة البيانات Supabase باستخدام SQL Editor
2. قم بتحديث التطبيق بالملفات الجديدة

## كيفية اختبار الحل:

1. انتقل إلى صفحة المراسلات الداخلية
2. انقر على إحدى الرسائل
3. يجب أن يتم عرض تفاصيل الرسالة بشكل صحيح
4. يمكنك الرد على الرسالة إذا كنت لست المرسل

## ملاحظات هامة:

1. تم الاحتفاظ بالصفحة القديمة لعرض تفاصيل الرسالة في المسار `src\routes\dashboard\messages\[id]\+page.svelte` للتوافق مع الروابط القديمة
2. يمكن حذف الصفحة القديمة بعد التأكد من عمل الصفحة الجديدة بشكل صحيح
3. تم تصميم الصفحة الجديدة بشكل بسيط وواضح لتسهيل الصيانة والتطوير في المستقبل
