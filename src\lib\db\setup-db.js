// ملف لإعداد قاعدة البيانات وإنشاء الجداول والأدوار والصلاحيات
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// إعداد عميل Supabase
const supabaseUrl = 'https://bgbzirxgewwidgybccxq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJnYnppcnhnZXd3aWRneWJjY3hxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTc1MjA0NzYsImV4cCI6MjAzMzA5NjQ3Nn0.Yd-ZVKwKMQZYBjUxmz-Iy6dJOKjXdGDRyQdZPM-4QYE';
const supabase = createClient(supabaseUrl, supabaseKey);

// قراءة ملف SQL
const schemaPath = path.resolve('./src/lib/db/schema.sql');
const schemaSql = fs.readFileSync(schemaPath, 'utf8');

// تقسيم الاستعلامات SQL
const queries = schemaSql
  .split(';')
  .map(query => query.trim())
  .filter(query => query.length > 0);

// تنفيذ الاستعلامات
async function executeQueries() {
  console.log('بدء تنفيذ استعلامات SQL...');

  for (let i = 0; i < queries.length; i++) {
    const query = queries[i];
    console.log(`تنفيذ الاستعلام ${i + 1}/${queries.length}`);

    try {
      const { error } = await supabase.rpc('exec_sql', { query: query + ';' });

      if (error) {
        console.error(`خطأ في تنفيذ الاستعلام ${i + 1}:`, error);
      }
    } catch (err) {
      console.error(`استثناء في تنفيذ الاستعلام ${i + 1}:`, err);
    }
  }

  console.log('اكتمل تنفيذ استعلامات SQL');
}

// تنفيذ الاستعلامات
executeQueries().catch(err => {
  console.error('خطأ في تنفيذ الاستعلامات:', err);
});
