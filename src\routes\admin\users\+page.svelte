<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib/supabase';
  import { PermissionService } from '$lib/services/permissionService';
  import { RoleType, Resource, Action } from '$lib/types/permissions';

  let isAdmin = false;
  let loading = true;
  let users = [];
  let filteredUsers = [];
  let searchTerm = '';
  let roles = [];
  let units = [];

  // نموذج إضافة مستخدم جديد
  let showAddForm = false;
  let newUser = {
    email: '',
    password: '',
    full_name: '',
    role_id: '',
    unit_id: '',
    units: [] // وحدات متعددة
  };

  // نموذج تعديل مستخدم
  let showEditForm = false;
  let editingUser = null;

  // وحدات المستخدم المحدد
  let selectedUserUnits = [];

  onMount(async () => {
    try {
      // الحصول على المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        goto('/login');
        return;
      }

      // التحقق من صلاحيات المستخدم
      isAdmin = await PermissionService.checkRole(user.id, RoleType.ADMIN);

      if (!isAdmin) {
        // التحقق من صلاحيات المدير
        const isManager = await PermissionService.checkRole(user.id, RoleType.MANAGER);
        const hasPermission = await PermissionService.checkPermission(
          user.id,
          Resource.USERS,
          Action.READ
        );

        if (!isManager && !hasPermission) {
          goto('/dashboard');
          return;
        }
      }

      // جلب المستخدمين والأدوار والوحدات
      await Promise.all([
        loadUsers(),
        loadRoles(),
        loadUnits()
      ]);
    } catch (error) {
      console.error('Error loading users page:', error);
    } finally {
      loading = false;
    }
  });

  async function loadUsers() {
    try {
      // جلب المستخدمين مع الوحدة الأساسية
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          email,
          full_name,
          role,
          role_id,
          roles:role_id (
            name,
            description
          ),
          unit_id,
          units:unit_id (
            name,
            type
          ),
          created_at
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // جلب الوحدات المتعددة لكل مستخدم
      const usersWithUnits = await Promise.all(data.map(async (user) => {
        // جلب وحدات المستخدم
        const { data: userUnits, error: unitsError } = await supabase
          .from('user_units')
          .select(`
            unit_id,
            is_primary,
            units:unit_id (
              id,
              name,
              type
            )
          `)
          .eq('user_id', user.id);

        if (unitsError) {
          console.error(`Error loading units for user ${user.id}:`, unitsError);
          return { ...user, userUnits: [] };
        }

        return { ...user, userUnits: userUnits || [] };
      }));

      users = usersWithUnits || [];
      filteredUsers = [...users];
    } catch (error) {
      console.error('Error loading users:', error);
    }
  }

  async function loadRoles() {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('name');

      if (error) throw error;

      roles = data || [];
    } catch (error) {
      console.error('Error loading roles:', error);
    }
  }

  async function loadUnits() {
    try {
      const { data, error } = await supabase
        .from('units')
        .select('*')
        .order('name');

      if (error) throw error;

      units = data || [];
    } catch (error) {
      console.error('Error loading units:', error);
    }
  }

  function filterUsers() {
    filteredUsers = users.filter(user => {
      const matchesSearch = searchTerm === '' ||
        (user.full_name && user.full_name.includes(searchTerm)) ||
        (user.email && user.email.includes(searchTerm));

      return matchesSearch;
    });
  }

  $: {
    if (users.length > 0) {
      filterUsers();
    }
  }

  async function handleAddUser() {
    try {
      // 1. إنشاء المستخدم في نظام المصادقة
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: newUser.email,
        password: newUser.password,
        options: {
          data: {
            full_name: newUser.full_name
          }
        }
      });

      if (authError) throw authError;

      if (authData && authData.user) {
        // 2. تحديث معلومات المستخدم في جدول profiles
        const { error: profileError } = await supabase
          .from('profiles')
          .update({
            full_name: newUser.full_name,
            role_id: newUser.role_id,
            unit_id: newUser.unit_id // الوحدة الأساسية
          })
          .eq('id', authData.user.id);

        if (profileError) throw profileError;

        // 3. إضافة الوحدات المتعددة إذا كانت موجودة
        if (newUser.units && newUser.units.length > 0) {
          const userUnits = newUser.units.map(unitId => ({
            user_id: authData.user.id,
            unit_id: unitId,
            is_primary: unitId === newUser.unit_id // تعيين الوحدة الأساسية
          }));

          const { error: unitsError } = await supabase
            .from('user_units')
            .insert(userUnits);

          if (unitsError) {
            console.error('Error adding user units:', unitsError);
          }
        }

        // إعادة تحميل المستخدمين
        await loadUsers();

        // إعادة تعيين النموذج
        newUser = {
          email: '',
          password: '',
          full_name: '',
          role_id: '',
          unit_id: '',
          units: []
        };

        showAddForm = false;
      }
    } catch (error: any) {
      console.error('Error adding user:', error);
      alert(`حدث خطأ أثناء إنشاء المستخدم: ${error.message}`);
    }
  }

  async function openEditForm(user) {
    // تحميل وحدات المستخدم
    selectedUserUnits = user.userUnits || [];

    editingUser = {
      id: user.id,
      email: user.email,
      full_name: user.full_name,
      role_id: user.role_id,
      unit_id: user.unit_id,
      units: selectedUserUnits.map(uu => uu.unit_id)
    };

    showEditForm = true;
  }

  async function handleEditUser() {
    try {
      if (!editingUser || !editingUser.id) return;

      // 1. تحديث معلومات المستخدم الأساسية
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: editingUser.full_name,
          role_id: editingUser.role_id,
          unit_id: editingUser.unit_id // الوحدة الأساسية
        })
        .eq('id', editingUser.id);

      if (error) throw error;

      // 2. حذف جميع وحدات المستخدم الحالية
      const { error: deleteError } = await supabase
        .from('user_units')
        .delete()
        .eq('user_id', editingUser.id);

      if (deleteError) {
        console.error('Error deleting user units:', deleteError);
      }

      // 3. إضافة الوحدات المحددة
      if (editingUser.units && editingUser.units.length > 0) {
        const userUnits = editingUser.units.map(unitId => ({
          user_id: editingUser.id,
          unit_id: unitId,
          is_primary: unitId === editingUser.unit_id // تعيين الوحدة الأساسية
        }));

        const { error: insertError } = await supabase
          .from('user_units')
          .insert(userUnits);

        if (insertError) {
          console.error('Error inserting user units:', insertError);
        }
      }

      // تحديث القائمة
      await loadUsers();

      showEditForm = false;
      editingUser = null;
    } catch (error) {
      console.error('Error updating user:', error);
      alert('حدث خطأ أثناء تحديث المستخدم');
    }
  }

  async function handleResetPassword(userId, email) {
    if (!confirm(`هل أنت متأكد من إرسال رابط إعادة تعيين كلمة المرور إلى ${email}؟`)) return;

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);

      if (error) throw error;

      alert(`تم إرسال رابط إعادة تعيين كلمة المرور إلى ${email}`);
    } catch (error) {
      console.error('Error resetting password:', error);
      alert(`حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور: ${error.message}`);
    }
  }

  function getRoleName(roleId) {
    const role = roles.find(r => r.id === roleId);
    return role ? role.name : '-';
  }

  function getUnitName(unitId) {
    const unit = units.find(u => u.id === unitId);
    return unit ? unit.name : '-';
  }
</script>

<svelte:head>
  <title>إدارة المستخدمين</title>
</svelte:head>

<div class="container mx-auto p-4 rtl" dir="rtl">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">إدارة المستخدمين</h1>

    <button
      class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
      on:click={() => showAddForm = true}
    >
      إضافة مستخدم جديد
    </button>
  </div>

  {#if loading}
    <div class="flex justify-center items-center h-64">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  {:else}
    <!-- أدوات البحث -->
    <div class="bg-white p-4 rounded-lg shadow-md mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">بحث</label>
          <input
            id="search"
            type="text"
            bind:value={searchTerm}
            placeholder="ابحث عن مستخدم..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
      </div>
    </div>

    <!-- جدول المستخدمين -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      {#if filteredUsers.length === 0}
        <div class="p-6 text-center text-gray-500">
          لا يوجد مستخدمين مطابقين للبحث
        </div>
      {:else}
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البريد الإلكتروني</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الدور</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوحدة</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {#each filteredUsers as user}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="font-medium text-gray-900">{user.full_name || '-'}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{user.email || '-'}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {#if user.roles}
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        {user.roles.name}
                      </span>
                    {:else}
                      <span class="text-sm text-gray-500">-</span>
                    {/if}
                  </td>
                  <td class="px-6 py-4">
                    {#if user.units}
                      <div class="text-sm text-gray-900 font-medium">{user.units.name} (أساسية)</div>
                    {:else}
                      <div class="text-sm text-gray-500">-</div>
                    {/if}

                    {#if user.userUnits && user.userUnits.length > 0}
                      <div class="mt-1">
                        {#each user.userUnits.filter(uu => uu.unit_id !== user.unit_id) as unitItem}
                          <span class="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full mr-1 mb-1">
                            {unitItem.units.name}
                          </span>
                        {/each}
                      </div>
                    {/if}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {new Date(user.created_at).toLocaleDateString('ar-SA')}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-left">
                    <button
                      class="text-indigo-600 hover:text-indigo-900 ml-3"
                      on:click={() => openEditForm(user)}
                    >
                      تعديل
                    </button>
                    <button
                      class="text-orange-600 hover:text-orange-900"
                      on:click={() => handleResetPassword(user.id, user.email)}
                    >
                      إعادة تعيين كلمة المرور
                    </button>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {/if}
    </div>
  {/if}

  <!-- نموذج إضافة مستخدم جديد -->
  {#if showAddForm}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4">إضافة مستخدم جديد</h2>

        <div class="mb-4">
          <label for="email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
          <input
            id="email"
            type="email"
            bind:value={newUser.email}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>

        <div class="mb-4">
          <label for="password" class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور</label>
          <input
            id="password"
            type="password"
            bind:value={newUser.password}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>

        <div class="mb-4">
          <label for="full_name" class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل</label>
          <input
            id="full_name"
            type="text"
            bind:value={newUser.full_name}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>

        <div class="mb-4">
          <label for="role" class="block text-sm font-medium text-gray-700 mb-1">الدور</label>
          <select
            id="role"
            bind:value={newUser.role_id}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">اختر دورًا</option>
            {#each roles as role}
              <option value={role.id}>{role.name}</option>
            {/each}
          </select>
        </div>

        <div class="mb-4">
          <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">الوحدة الأساسية</label>
          <select
            id="unit"
            bind:value={newUser.unit_id}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">اختر وحدة</option>
            {#each units as unit}
              <option value={unit.id}>{unit.name} ({unit.type})</option>
            {/each}
          </select>
          <p class="text-xs text-gray-500 mt-1">هذه هي الوحدة الأساسية للمستخدم</p>
        </div>

        <div class="mb-4">
          <label for="multiple-units" class="block text-sm font-medium text-gray-700 mb-1">الوحدات المتاحة للمستخدم</label>
          <div id="multiple-units" class="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
            {#each units as unit}
              <div class="flex items-center mb-2">
                <input
                  type="checkbox"
                  id={`new-unit-${unit.id}`}
                  value={unit.id}
                  checked={newUser.units.includes(unit.id)}
                  on:change={(e) => {
                    const target = e.target as HTMLInputElement;
                    if (target.checked) {
                      // إضافة الوحدة إلى القائمة
                      newUser.units = [...newUser.units, unit.id];
                    } else {
                      // إزالة الوحدة من القائمة
                      newUser.units = newUser.units.filter((id: string) => id !== unit.id);
                    }
                  }}
                  class="ml-2"
                />
                <label for={`new-unit-${unit.id}`} class="text-sm">{unit.name} ({unit.type})</label>
              </div>
            {/each}
          </div>
          <p class="text-xs text-gray-500 mt-1">يمكن للمستخدم الوصول إلى جميع الوحدات المحددة</p>
        </div>

        <div class="flex justify-end gap-2">
          <button
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            on:click={() => showAddForm = false}
          >
            إلغاء
          </button>
          <button
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            on:click={handleAddUser}
          >
            إضافة
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- نموذج تعديل مستخدم -->
  {#if showEditForm && editingUser}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4">تعديل مستخدم</h2>

        <div class="mb-4">
          <label for="edit-email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
          <input
            id="edit-email"
            type="email"
            value={editingUser.email}
            class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
            disabled
          />
          <p class="text-xs text-gray-500 mt-1">لا يمكن تغيير البريد الإلكتروني</p>
        </div>

        <div class="mb-4">
          <label for="edit-full_name" class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل</label>
          <input
            id="edit-full_name"
            type="text"
            bind:value={editingUser.full_name}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>

        <div class="mb-4">
          <label for="edit-role" class="block text-sm font-medium text-gray-700 mb-1">الدور</label>
          <select
            id="edit-role"
            bind:value={editingUser.role_id}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">بدون دور</option>
            {#each roles as role}
              <option value={role.id}>{role.name}</option>
            {/each}
          </select>
        </div>

        <div class="mb-4">
          <label for="edit-unit" class="block text-sm font-medium text-gray-700 mb-1">الوحدة الأساسية</label>
          <select
            id="edit-unit"
            bind:value={editingUser.unit_id}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">بدون وحدة</option>
            {#each units as unit}
              <option value={unit.id}>{unit.name} ({unit.type})</option>
            {/each}
          </select>
          <p class="text-xs text-gray-500 mt-1">هذه هي الوحدة الأساسية للمستخدم</p>
        </div>

        <div class="mb-4">
          <label for="edit-multiple-units" class="block text-sm font-medium text-gray-700 mb-1">الوحدات المتاحة للمستخدم</label>
          <div id="edit-multiple-units" class="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
            {#each units as unit}
              <div class="flex items-center mb-2">
                <input
                  type="checkbox"
                  id={`unit-${unit.id}`}
                  value={unit.id}
                  checked={editingUser.units.includes(unit.id)}
                  on:change={(e) => {
                    const target = e.target as HTMLInputElement;
                    if (target.checked) {
                      // إضافة الوحدة إلى القائمة
                      editingUser.units = [...editingUser.units, unit.id];
                    } else {
                      // إزالة الوحدة من القائمة
                      editingUser.units = editingUser.units.filter((id: string) => id !== unit.id);
                    }
                  }}
                  class="ml-2"
                />
                <label for={`unit-${unit.id}`} class="text-sm">{unit.name} ({unit.type})</label>
              </div>
            {/each}
          </div>
          <p class="text-xs text-gray-500 mt-1">يمكن للمستخدم الوصول إلى جميع الوحدات المحددة</p>
        </div>

        <div class="flex justify-end gap-2">
          <button
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            on:click={() => showEditForm = false}
          >
            إلغاء
          </button>
          <button
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            on:click={handleEditUser}
          >
            حفظ التغييرات
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .rtl {
    direction: rtl;
    text-align: right;
  }
</style>
