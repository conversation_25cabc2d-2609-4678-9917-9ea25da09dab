<script lang="ts">
  import { onMount } from 'svelte';
  import { supabase } from '$lib/supabase';
  import { 
    migrateDocumentsToStorage, 
    migrateAttachmentsToStorage, 
    migrateAllToStorage,
    cleanupOldData 
  } from '$lib/scripts/migrateToStorage';

  let isAdmin = false;
  let loading = false;
  let migrationResults: any = null;
  let migrationStep = '';
  let progress = 0;

  // إحصائيات قاعدة البيانات
  let stats = {
    documentsWithContent: 0,
    documentsWithUrl: 0,
    messagesWithAttachments: 0,
    messagesWithAttachmentUrl: 0
  };

  onMount(async () => {
    // التحقق من صلاحيات المشرف
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      window.location.href = '/auth/login';
      return;
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || profile.role !== 'admin') {
      alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
      window.location.href = '/dashboard';
      return;
    }

    isAdmin = true;
    await loadStats();
  });

  async function loadStats() {
    try {
      // إحصائيات المستندات
      const { count: documentsWithContent } = await supabase
        .from('documents')
        .select('*', { count: 'exact', head: true })
        .not('content', 'is', null)
        .is('content_url', null);

      const { count: documentsWithUrl } = await supabase
        .from('documents')
        .select('*', { count: 'exact', head: true })
        .not('content_url', 'is', null);

      // إحصائيات المرفقات
      const { count: messagesWithAttachments } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .not('attachment', 'is', null)
        .is('attachment_content_url', null);

      const { count: messagesWithAttachmentUrl } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .not('attachment_content_url', 'is', null);

      stats = {
        documentsWithContent: documentsWithContent || 0,
        documentsWithUrl: documentsWithUrl || 0,
        messagesWithAttachments: messagesWithAttachments || 0,
        messagesWithAttachmentUrl: messagesWithAttachmentUrl || 0
      };
    } catch (err) {
      console.error('خطأ في جلب الإحصائيات:', err);
    }
  }

  async function runMigration(type: 'documents' | 'attachments' | 'all') {
    loading = true;
    migrationResults = null;
    progress = 0;

    try {
      let result;
      
      switch (type) {
        case 'documents':
          migrationStep = 'نقل المستندات إلى Storage...';
          progress = 25;
          result = await migrateDocumentsToStorage();
          progress = 100;
          break;
          
        case 'attachments':
          migrationStep = 'نقل المرفقات إلى Storage...';
          progress = 25;
          result = await migrateAttachmentsToStorage();
          progress = 100;
          break;
          
        case 'all':
          migrationStep = 'نقل المستندات إلى Storage...';
          progress = 20;
          result = await migrateAllToStorage();
          progress = 100;
          break;
      }

      migrationResults = result;
      migrationStep = 'اكتمل النقل';
      
      // إعادة تحميل الإحصائيات
      await loadStats();
    } catch (err) {
      console.error('خطأ في النقل:', err);
      migrationResults = {
        success: false,
        message: `خطأ في النقل: ${err}`
      };
    } finally {
      loading = false;
      progress = 0;
      migrationStep = '';
    }
  }

  async function runCleanup() {
    if (!confirm('هل أنت متأكد من تنظيف البيانات القديمة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      return;
    }

    loading = true;
    migrationStep = 'تنظيف البيانات القديمة...';
    progress = 50;

    try {
      const result = await cleanupOldData();
      migrationResults = result;
      migrationStep = 'اكتمل التنظيف';
      progress = 100;
      
      // إعادة تحميل الإحصائيات
      await loadStats();
    } catch (err) {
      console.error('خطأ في التنظيف:', err);
      migrationResults = {
        success: false,
        message: `خطأ في التنظيف: ${err}`
      };
    } finally {
      loading = false;
      progress = 0;
      migrationStep = '';
    }
  }
</script>

<svelte:head>
  <title>نقل البيانات إلى Storage</title>
</svelte:head>

{#if !isAdmin}
  <div class="flex justify-center items-center h-64">
    <p class="text-lg">جاري التحقق من الصلاحيات...</p>
  </div>
{:else}
  <div class="container mx-auto p-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">نقل البيانات إلى Supabase Storage</h1>
      <p class="text-gray-600">نقل محتوى المستندات والمرفقات من قاعدة البيانات إلى التخزين السحابي لتحسين الأداء</p>
    </div>

    <!-- الإحصائيات -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-blue-800 mb-2">المستندات في قاعدة البيانات</h3>
        <p class="text-2xl font-bold text-blue-600">{stats.documentsWithContent}</p>
        <p class="text-sm text-blue-600">مستند يحتاج إلى نقل</p>
      </div>

      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-green-800 mb-2">المستندات في Storage</h3>
        <p class="text-2xl font-bold text-green-600">{stats.documentsWithUrl}</p>
        <p class="text-sm text-green-600">مستند تم نقله</p>
      </div>

      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-orange-800 mb-2">المرفقات في قاعدة البيانات</h3>
        <p class="text-2xl font-bold text-orange-600">{stats.messagesWithAttachments}</p>
        <p class="text-sm text-orange-600">مرفق يحتاج إلى نقل</p>
      </div>

      <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-purple-800 mb-2">المرفقات في Storage</h3>
        <p class="text-2xl font-bold text-purple-600">{stats.messagesWithAttachmentUrl}</p>
        <p class="text-sm text-purple-600">مرفق تم نقله</p>
      </div>
    </div>

    <!-- أزرار النقل -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 class="text-xl font-bold mb-4">عمليات النقل</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <button
          class="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white py-3 px-4 rounded-lg font-medium transition-colors"
          disabled={loading}
          on:click={() => runMigration('documents')}
        >
          نقل المستندات فقط
        </button>

        <button
          class="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-400 text-white py-3 px-4 rounded-lg font-medium transition-colors"
          disabled={loading}
          on:click={() => runMigration('attachments')}
        >
          نقل المرفقات فقط
        </button>

        <button
          class="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white py-3 px-4 rounded-lg font-medium transition-colors"
          disabled={loading}
          on:click={() => runMigration('all')}
        >
          نقل جميع البيانات
        </button>

        <button
          class="bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white py-3 px-4 rounded-lg font-medium transition-colors"
          disabled={loading}
          on:click={runCleanup}
        >
          تنظيف البيانات القديمة
        </button>
      </div>
    </div>

    <!-- شريط التقدم -->
    {#if loading}
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">جاري المعالجة...</h3>
        <div class="mb-2">
          <div class="flex justify-between text-sm">
            <span>{migrationStep}</span>
            <span>{progress}%</span>
          </div>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: {progress}%"></div>
        </div>
      </div>
    {/if}

    <!-- نتائج النقل -->
    {#if migrationResults}
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold mb-4">نتائج العملية</h3>
        
        <div class="mb-4 p-4 rounded-lg {migrationResults.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}">
          <div class="flex items-center">
            {#if migrationResults.success}
              <svg class="w-5 h-5 text-green-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-green-800 font-medium">نجحت العملية</span>
            {:else}
              <svg class="w-5 h-5 text-red-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              <span class="text-red-800 font-medium">فشلت العملية</span>
            {/if}
          </div>
          <p class="mt-2 text-sm {migrationResults.success ? 'text-green-700' : 'text-red-700'}">{migrationResults.message}</p>
        </div>

        {#if migrationResults.details}
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="font-medium mb-2">تفاصيل العملية:</h4>
            <pre class="text-sm text-gray-700 whitespace-pre-wrap">{JSON.stringify(migrationResults.details, null, 2)}</pre>
          </div>
        {/if}
      </div>
    {/if}

    <!-- تعليمات -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-6">
      <h3 class="text-lg font-semibold text-yellow-800 mb-2">تعليمات مهمة</h3>
      <ul class="text-sm text-yellow-700 space-y-1">
        <li>• تأكد من عمل نسخة احتياطية قبل بدء عملية النقل</li>
        <li>• ابدأ بنقل المستندات أو المرفقات منفصلة للاختبار</li>
        <li>• استخدم "نقل جميع البيانات" فقط بعد التأكد من نجاح الاختبارات</li>
        <li>• لا تقم بتنظيف البيانات القديمة إلا بعد التأكد من نجاح النقل</li>
        <li>• يمكن تشغيل العملية عدة مرات بأمان</li>
      </ul>
    </div>
  </div>
{/if}
