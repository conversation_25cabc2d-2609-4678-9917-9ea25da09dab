# تنفيذ الهيكل التنظيمي للمستندات

تم تنفيذ الهيكل التنظيمي للمستندات بحيث يمكن لكل وحدة تنظيمية (إدارة، قسم، وحدة) رؤية المستندات الخاصة بها والمستندات الخاصة بالوحدات التابعة لها.

## الخطوات التي تم تنفيذها:

1. إنشاء وظائف SQL لاسترجاع الوحدات التابعة لوحدة معينة
2. إنشاء خدمة للوحدات التنظيمية
3. إنشاء خدمة للمستندات
4. تعديل صفحة المستندات لإضافة فلتر الوحدات التنظيمية

## تنفيذ وظائف SQL

يجب تنفيذ وظائف SQL التالية في قاعدة البيانات Supabase باستخدام SQL Editor:

```sql
-- وظيفة لاسترجاع جميع الوحدات التابعة لوحدة معينة (بشكل متدرج)
CREATE OR REPLACE FUNCTION get_subordinate_units(parent_unit_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  type TEXT,
  description TEXT,
  parent_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  level INT
) AS $$
WITH RECURSIVE subordinates AS (
  -- الوحدة الأساسية
  SELECT 
    u.id, 
    u.name, 
    u.type, 
    u.description, 
    u.parent_id, 
    u.created_at, 
    u.updated_at,
    0 AS level
  FROM units u
  WHERE u.id = parent_unit_id
  
  UNION ALL
  
  -- الوحدات التابعة بشكل متدرج
  SELECT 
    u.id, 
    u.name, 
    u.type, 
    u.description, 
    u.parent_id, 
    u.created_at, 
    u.updated_at,
    s.level + 1
  FROM units u
  JOIN subordinates s ON u.parent_id = s.id
)
SELECT * FROM subordinates;
$$ LANGUAGE SQL;

-- وظيفة لاسترجاع جميع الوحدات التابعة لوحدة معينة (بشكل متدرج) مع الوحدة نفسها
CREATE OR REPLACE FUNCTION get_unit_and_subordinates(unit_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  type TEXT,
  description TEXT,
  parent_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  level INT
) AS $$
WITH RECURSIVE subordinates AS (
  -- الوحدة الأساسية
  SELECT 
    u.id, 
    u.name, 
    u.type, 
    u.description, 
    u.parent_id, 
    u.created_at, 
    u.updated_at,
    0 AS level
  FROM units u
  WHERE u.id = unit_id
  
  UNION ALL
  
  -- الوحدات التابعة بشكل متدرج
  SELECT 
    u.id, 
    u.name, 
    u.type, 
    u.description, 
    u.parent_id, 
    u.created_at, 
    u.updated_at,
    s.level + 1
  FROM units u
  JOIN subordinates s ON u.parent_id = s.id
)
SELECT * FROM subordinates;
$$ LANGUAGE SQL;
```

## كيفية استخدام الميزة الجديدة

1. انتقل إلى صفحة المستندات
2. استخدم فلتر "الوحدة" لاختيار الوحدة التنظيمية
3. حدد خيار "شامل الوحدات التابعة" لعرض مستندات الوحدة والوحدات التابعة لها
4. قم بإلغاء تحديد الخيار لعرض مستندات الوحدة فقط

## ملاحظات

- يتم تحديد الوحدة التنظيمية للمستخدم تلقائياً عند تحميل الصفحة
- يمكن للمستخدم تغيير الوحدة التنظيمية المحددة لعرض مستندات وحدات أخرى (إذا كان لديه صلاحية)
- يتم عرض الوحدة التنظيمية المرتبطة بكل مستند في جدول المستندات
