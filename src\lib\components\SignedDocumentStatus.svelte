<script lang="ts">
  export let status: string;
  export let compact: boolean = false;

  // تحديد لون وأيقونة الحالة
  let statusColor: string;
  let statusIcon: string;
  let statusText: string;

  $: {
    switch (status) {
      case 'pending_signature':
        statusColor = 'bg-yellow-100 text-yellow-800 border-yellow-200';
        statusIcon = 'M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10';
        statusText = 'في انتظار التوقيع';
        break;
      case 'signed':
        statusColor = 'bg-blue-100 text-blue-800 border-blue-200';
        statusIcon = 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
        statusText = 'تم التوقيع';
        break;
      case 'sent':
        statusColor = 'bg-indigo-100 text-indigo-800 border-indigo-200';
        statusIcon = 'M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5';
        statusText = 'تم الإرسال';
        break;
      case 'under_review':
        statusColor = 'bg-purple-100 text-purple-800 border-purple-200';
        statusIcon = 'M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9zm3.75 11.625a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z';
        statusText = 'قيد المراجعة';
        break;
      case 'approved':
        statusColor = 'bg-green-100 text-green-800 border-green-200';
        statusIcon = 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
        statusText = 'تمت الموافقة';
        break;
      case 'rejected':
        statusColor = 'bg-red-100 text-red-800 border-red-200';
        statusIcon = 'M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
        statusText = 'تم الرفض';
        break;
      case 'returned_for_edit':
        statusColor = 'bg-orange-100 text-orange-800 border-orange-200';
        statusIcon = 'M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99';
        statusText = 'تم الإرجاع للتعديل';
        break;
      default:
        statusColor = 'bg-gray-100 text-gray-800 border-gray-200';
        statusIcon = 'M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z';
        statusText = 'غير معروف';
    }
  }
</script>

{#if compact}
  <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {statusColor} border">
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 ml-1">
      <path stroke-linecap="round" stroke-linejoin="round" d={statusIcon} />
    </svg>
    {statusText}
  </div>
{:else}
  <div class="flex items-center p-2 rounded-md {statusColor} border">
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 ml-2">
      <path stroke-linecap="round" stroke-linejoin="round" d={statusIcon} />
    </svg>
    <span class="font-medium">{statusText}</span>
  </div>
{/if}
