-- تغيير اسم العمود logo_text إلى logo

-- التحقق من وجود عمود logo_text وتغيير اسمه إلى logo
DO $$
BEGIN
    -- التحقق من وجود عمود logo_text
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'logo_text') THEN
        -- التحقق من عدم وجود عمود logo (لتجنب التعارض)
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'organization_settings' AND column_name = 'logo') THEN
            -- تغيير اسم العمود
            ALTER TABLE organization_settings RENAME COLUMN logo_text TO logo;
        ELSE
            -- إذا كان هناك عمود logo موجود بالفعل، قم بنسخ قيمة logo_text إلى logo
            UPDATE organization_settings SET logo = logo_text;
            -- ثم قم بحذف عمود logo_text
            ALTER TABLE organization_settings DROP COLUMN logo_text;
        END IF;
    END IF;
END $$;
