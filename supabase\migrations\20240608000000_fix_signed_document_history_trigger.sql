-- إصلاح trigger تاريخ المستندات الموقعة

-- حذف trigger القديم
DROP TRIGGER IF EXISTS update_signed_document_history ON public.signed_documents;

-- إعادة إنشاء دالة trigger مع إصلاح مشكلة user_id
CREATE OR REPLACE FUNCTION public.update_signed_document_history()
RETURNS TRIGGER AS $$
BEGIN
  -- إدراج سجل في تاريخ المستندات الموقعة
  INSERT INTO public.signed_document_history (
    signed_document_id,
    user_id,
    action,
    status,
    details
  ) VALUES (
    NEW.id,
    -- تحديد المستخدم بناءً على نوع العملية والحالة
    CASE
      WHEN TG_OP = 'INSERT' THEN NEW.creator_id
      WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        CASE
          WHEN NEW.status = 'signed' THEN NEW.signer_id
          WHEN NEW.status = 'rejected' THEN NEW.signer_id
          WHEN NEW.status = 'revision_requested' THEN NEW.signer_id
          WHEN NEW.status = 'under_review' THEN NEW.signer_id
          WHEN NEW.status = 'approved' THEN NEW.signer_id
          WHEN NEW.status = 'returned_for_edit' THEN NEW.signer_id
          ELSE COALESCE(auth.uid(), NEW.creator_id, NEW.signer_id)
        END
      ELSE COALESCE(auth.uid(), NEW.creator_id, NEW.signer_id)
    END,
    -- تحديد نوع الإجراء
    CASE
      WHEN TG_OP = 'INSERT' THEN 'create'
      WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        CASE
          WHEN NEW.status = 'pending_signature' THEN 'request_signature'
          WHEN NEW.status = 'signed' THEN 'sign'
          WHEN NEW.status = 'sent' THEN 'send'
          WHEN NEW.status = 'under_review' THEN 'review'
          WHEN NEW.status = 'approved' THEN 'approve'
          WHEN NEW.status = 'rejected' THEN 'reject'
          WHEN NEW.status = 'revision_requested' THEN 'request_revision'
          WHEN NEW.status = 'returned_for_edit' THEN 'return_for_edit'
          ELSE 'update'
        END
      ELSE 'update'
    END,
    NEW.status,
    jsonb_build_object(
      'document_id', NEW.document_id,
      'reference_number', NEW.reference_number,
      'rejection_reason', NEW.rejection_reason,
      'revision_comments', NEW.revision_comments,
      'old_status', CASE WHEN TG_OP = 'UPDATE' THEN OLD.status ELSE NULL END
    )
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إعادة إنشاء trigger
CREATE TRIGGER update_signed_document_history
AFTER INSERT OR UPDATE ON public.signed_documents
FOR EACH ROW
EXECUTE FUNCTION public.update_signed_document_history();

-- التأكد من أن جدول signed_document_history يحتوي على الأعمدة المطلوبة
DO $$
BEGIN
  -- التحقق من وجود عمود details
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'signed_document_history'
    AND column_name = 'details'
  ) THEN
    ALTER TABLE public.signed_document_history ADD COLUMN details JSONB;
  END IF;
END $$;

-- إضافة تعليق للدالة
COMMENT ON FUNCTION public.update_signed_document_history() IS 'دالة لتحديث تاريخ المستندات الموقعة مع معالجة مشكلة user_id المفقود';

-- تحديث ذاكرة التخزين المؤقت للمخطط
NOTIFY pgrst, 'reload schema';
