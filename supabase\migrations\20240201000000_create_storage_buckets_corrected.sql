-- إنشاء مخازن التخزين (Storage Buckets)

-- إنشاء مخزن للشعارات
INSERT INTO storage.buckets (id, name, public)
VALUES ('logos', 'Logos', true)
ON CONFLICT (id) DO NOTHING;

-- إنشاء مخزن للأختام
INSERT INTO storage.buckets (id, name, public)
VALUES ('stamps', 'Stamps', true)
ON CONFLICT (id) DO NOTHING;

-- إنشاء مخزن saadabujnah
INSERT INTO storage.buckets (id, name, public)
VALUES ('saadabujnah', 'Saadabujnah', true)
ON CONFLICT (id) DO NOTHING;

-- إضافة سياسات الوصول للقراءة العامة للشعارات
CREATE POLICY "Logos Public Read Policy"
ON storage.objects
FOR SELECT
USING (bucket_id = 'logos');

-- إضافة سياسات الوصول للقراءة العامة للأختام
CREATE POLICY "Stamps Public Read Policy"
ON storage.objects
FOR SELECT
USING (bucket_id = 'stamps');

-- إضافة سياسات الوصول للقراءة العامة لمجلد saadabujnah
CREATE POLICY "Saadabujnah Public Read Policy"
ON storage.objects
FOR SELECT
USING (bucket_id = 'saadabujnah');

-- إضافة سياسات الوصول للكتابة للمستخدمين المصادق عليهم للشعارات
CREATE POLICY "Logos Authenticated Insert Policy"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'logos'
  AND auth.role() = 'authenticated'
);

-- إضافة سياسات الوصول للكتابة للمستخدمين المصادق عليهم للأختام
CREATE POLICY "Stamps Authenticated Insert Policy"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'stamps'
  AND auth.role() = 'authenticated'
);

-- إضافة سياسات الوصول للكتابة للمستخدمين المصادق عليهم لمجلد saadabujnah
CREATE POLICY "Saadabujnah Authenticated Insert Policy"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'saadabujnah'
  AND auth.role() = 'authenticated'
);

-- إضافة سياسات الوصول للتعديل للمستخدمين المصادق عليهم للشعارات
CREATE POLICY "Logos Authenticated Update Policy"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'logos'
  AND auth.role() = 'authenticated'
);

-- إضافة سياسات الوصول للتعديل للمستخدمين المصادق عليهم للأختام
CREATE POLICY "Stamps Authenticated Update Policy"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'stamps'
  AND auth.role() = 'authenticated'
);

-- إضافة سياسات الوصول للتعديل للمستخدمين المصادق عليهم لمجلد saadabujnah
CREATE POLICY "Saadabujnah Authenticated Update Policy"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'saadabujnah'
  AND auth.role() = 'authenticated'
);

-- إضافة سياسات الوصول للحذف للمستخدمين المصادق عليهم للشعارات
CREATE POLICY "Logos Authenticated Delete Policy"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'logos'
  AND auth.role() = 'authenticated'
);

-- إضافة سياسات الوصول للحذف للمستخدمين المصادق عليهم للأختام
CREATE POLICY "Stamps Authenticated Delete Policy"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'stamps'
  AND auth.role() = 'authenticated'
);

-- إضافة سياسات الوصول للحذف للمستخدمين المصادق عليهم لمجلد saadabujnah
CREATE POLICY "Saadabujnah Authenticated Delete Policy"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'saadabujnah'
  AND auth.role() = 'authenticated'
);
