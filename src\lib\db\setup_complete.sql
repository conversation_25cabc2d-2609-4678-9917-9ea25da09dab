-- حذف الجداول إذا كانت موجودة (بترتيب عكسي للاعتمادية)
DROP TABLE IF EXISTS role_permissions;
DROP TABLE IF EXISTS permissions;
DROP TABLE IF EXISTS roles;

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الأدوار (roles)
CREATE TABLE IF NOT EXISTS roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الصلاحيات (permissions)
CREATE TABLE IF NOT EXISTS permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  resource VARCHAR(50) NOT NULL,
  action VARCHAR(50) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(resource, action)
);

-- 3. إنشاء جدول العلاقة بين الأدوار والصلاحيات (role_permissions)
CREATE TABLE IF NOT EXISTS role_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role_id, permission_id)
);

-- 4. تحديث جدول المستخدمين (profiles) لربطه بالأدوار
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS role_id UUID REFERENCES roles(id);

-- 5. إضافة بيانات أولية للأدوار
INSERT INTO roles (name, description) VALUES
('admin', 'مدير النظام مع كامل الصلاحيات') ON CONFLICT (name) DO NOTHING;

INSERT INTO roles (name, description) VALUES
('manager', 'مدير مع صلاحيات إدارية محدودة') ON CONFLICT (name) DO NOTHING;

INSERT INTO roles (name, description) VALUES
('user', 'مستخدم عادي مع صلاحيات محدودة') ON CONFLICT (name) DO NOTHING;

-- 6. إضافة بيانات أولية للصلاحيات
-- صلاحيات المستندات
INSERT INTO permissions (name, description, resource, action) VALUES
('documents:create', 'إنشاء مستندات جديدة', 'documents', 'create') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('documents:read', 'قراءة المستندات', 'documents', 'read') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('documents:update', 'تحديث المستندات', 'documents', 'update') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('documents:delete', 'حذف المستندات', 'documents', 'delete') ON CONFLICT (name) DO NOTHING;

-- صلاحيات المستخدمين
INSERT INTO permissions (name, description, resource, action) VALUES
('users:create', 'إنشاء مستخدمين جدد', 'users', 'create') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('users:read', 'قراءة بيانات المستخدمين', 'users', 'read') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('users:update', 'تحديث بيانات المستخدمين', 'users', 'update') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('users:delete', 'حذف المستخدمين', 'users', 'delete') ON CONFLICT (name) DO NOTHING;

-- صلاحيات المراسلات
INSERT INTO permissions (name, description, resource, action) VALUES
('messages:create', 'إنشاء مراسلات جديدة', 'messages', 'create') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('messages:read', 'قراءة المراسلات', 'messages', 'read') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('messages:update', 'تحديث المراسلات', 'messages', 'update') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('messages:delete', 'حذف المراسلات', 'messages', 'delete') ON CONFLICT (name) DO NOTHING;

-- صلاحيات التعميمات
INSERT INTO permissions (name, description, resource, action) VALUES
('broadcasts:create', 'إنشاء تعميمات جديدة', 'broadcasts', 'create') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('broadcasts:read', 'قراءة التعميمات', 'broadcasts', 'read') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('broadcasts:update', 'تحديث التعميمات', 'broadcasts', 'update') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('broadcasts:delete', 'حذف التعميمات', 'broadcasts', 'delete') ON CONFLICT (name) DO NOTHING;

-- صلاحيات الهيكل التنظيمي
INSERT INTO permissions (name, description, resource, action) VALUES
('organization:create', 'إنشاء وحدات تنظيمية جديدة', 'organization', 'create') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('organization:read', 'قراءة الهيكل التنظيمي', 'organization', 'read') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('organization:update', 'تحديث الهيكل التنظيمي', 'organization', 'update') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('organization:delete', 'حذف وحدات من الهيكل التنظيمي', 'organization', 'delete') ON CONFLICT (name) DO NOTHING;

-- صلاحيات النظام
INSERT INTO permissions (name, description, resource, action) VALUES
('system:settings', 'تعديل إعدادات النظام', 'system', 'settings') ON CONFLICT (name) DO NOTHING;

INSERT INTO permissions (name, description, resource, action) VALUES
('system:logs', 'عرض سجلات النظام', 'system', 'logs') ON CONFLICT (name) DO NOTHING;

-- 7. التحقق من هيكل جدول role_permissions
DO $$
DECLARE
  role_id_exists BOOLEAN;
  permission_id_exists BOOLEAN;
BEGIN
  -- التحقق من وجود العمود role_id
  SELECT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'role_permissions' AND column_name = 'role_id'
  ) INTO role_id_exists;

  -- التحقق من وجود العمود permission_id
  SELECT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'role_permissions' AND column_name = 'permission_id'
  ) INTO permission_id_exists;

  -- إذا كان العمودان غير موجودين، قم بإعادة إنشاء الجدول
  IF NOT (role_id_exists AND permission_id_exists) THEN
    DROP TABLE IF EXISTS role_permissions;

    CREATE TABLE role_permissions (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
      permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(role_id, permission_id)
    );
  END IF;
END $$;

-- 8. ربط الصلاحيات بدور المدير (admin)
DO $$
DECLARE
  admin_role_id UUID;
  perm_id UUID;
  perm_cursor CURSOR FOR SELECT id FROM permissions;
BEGIN
  -- الحصول على معرف دور المدير
  SELECT id INTO admin_role_id FROM roles WHERE name = 'admin';

  IF admin_role_id IS NOT NULL THEN
    -- إضافة جميع الصلاحيات لدور المدير
    OPEN perm_cursor;
    LOOP
      FETCH perm_cursor INTO perm_id;
      EXIT WHEN NOT FOUND;

      BEGIN
        INSERT INTO role_permissions (role_id, permission_id)
        VALUES (admin_role_id, perm_id);
      EXCEPTION WHEN OTHERS THEN
        -- تجاهل الأخطاء (مثل تكرار المفاتيح)
        NULL;
      END;
    END LOOP;
    CLOSE perm_cursor;
  END IF;
END $$;

-- 9. ربط الصلاحيات بدور المدير (manager)
DO $$
DECLARE
  manager_role_id UUID;
  perm_id UUID;
  perm_cursor CURSOR FOR
    SELECT id FROM permissions
    WHERE resource IN ('documents', 'messages', 'broadcasts', 'organization')
    AND action IN ('create', 'read', 'update')
    UNION
    SELECT id FROM permissions
    WHERE resource = 'users' AND action = 'read';
BEGIN
  -- الحصول على معرف دور المدير
  SELECT id INTO manager_role_id FROM roles WHERE name = 'manager';

  IF manager_role_id IS NOT NULL THEN
    -- إضافة صلاحيات محددة لدور المدير
    OPEN perm_cursor;
    LOOP
      FETCH perm_cursor INTO perm_id;
      EXIT WHEN NOT FOUND;

      BEGIN
        INSERT INTO role_permissions (role_id, permission_id)
        VALUES (manager_role_id, perm_id);
      EXCEPTION WHEN OTHERS THEN
        -- تجاهل الأخطاء (مثل تكرار المفاتيح)
        NULL;
      END;
    END LOOP;
    CLOSE perm_cursor;
  END IF;
END $$;

-- 10. ربط الصلاحيات بدور المستخدم العادي (user)
DO $$
DECLARE
  user_role_id UUID;
  perm_id UUID;
  perm_cursor CURSOR FOR
    SELECT id FROM permissions
    WHERE (resource IN ('documents', 'messages') AND action IN ('create', 'read'))
    OR (resource IN ('broadcasts', 'organization') AND action = 'read');
BEGIN
  -- الحصول على معرف دور المستخدم
  SELECT id INTO user_role_id FROM roles WHERE name = 'user';

  IF user_role_id IS NOT NULL THEN
    -- إضافة صلاحيات محددة لدور المستخدم
    OPEN perm_cursor;
    LOOP
      FETCH perm_cursor INTO perm_id;
      EXIT WHEN NOT FOUND;

      BEGIN
        INSERT INTO role_permissions (role_id, permission_id)
        VALUES (user_role_id, perm_id);
      EXCEPTION WHEN OTHERS THEN
        -- تجاهل الأخطاء (مثل تكرار المفاتيح)
        NULL;
      END;
    END LOOP;
    CLOSE perm_cursor;
  END IF;
END $$;
