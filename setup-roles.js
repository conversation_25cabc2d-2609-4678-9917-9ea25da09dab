// ملف لإعداد الأدوار والصلاحيات في قاعدة البيانات
import { createClient } from '@supabase/supabase-js';

// إعداد عميل Supabase
const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';
const supabase = createClient(supabaseUrl, supabaseKey);

// تعريف الأدوار
const roles = [
  { name: 'admin', description: 'مدير النظام مع كامل الصلاحيات' },
  { name: 'manager', description: 'مدير مع صلاحيات إدارية محدودة' },
  { name: 'user', description: 'مستخدم عادي مع صلاحيات محدودة' }
];

// تعريف الصلاحيات
const permissions = [
  // صلاحيات المستندات
  { name: 'documents:create', description: 'إنشاء مستندات جديدة', resource: 'documents', action: 'create' },
  { name: 'documents:read', description: 'قراءة المستندات', resource: 'documents', action: 'read' },
  { name: 'documents:update', description: 'تحديث المستندات', resource: 'documents', action: 'update' },
  { name: 'documents:delete', description: 'حذف المستندات', resource: 'documents', action: 'delete' },

  // صلاحيات المستخدمين
  { name: 'users:create', description: 'إنشاء مستخدمين جدد', resource: 'users', action: 'create' },
  { name: 'users:read', description: 'قراءة بيانات المستخدمين', resource: 'users', action: 'read' },
  { name: 'users:update', description: 'تحديث بيانات المستخدمين', resource: 'users', action: 'update' },
  { name: 'users:delete', description: 'حذف المستخدمين', resource: 'users', action: 'delete' },

  // صلاحيات المراسلات
  { name: 'messages:create', description: 'إنشاء مراسلات جديدة', resource: 'messages', action: 'create' },
  { name: 'messages:read', description: 'قراءة المراسلات', resource: 'messages', action: 'read' },
  { name: 'messages:update', description: 'تحديث المراسلات', resource: 'messages', action: 'update' },
  { name: 'messages:delete', description: 'حذف المراسلات', resource: 'messages', action: 'delete' },

  // صلاحيات التعميمات
  { name: 'broadcasts:create', description: 'إنشاء تعميمات جديدة', resource: 'broadcasts', action: 'create' },
  { name: 'broadcasts:read', description: 'قراءة التعميمات', resource: 'broadcasts', action: 'read' },
  { name: 'broadcasts:update', description: 'تحديث التعميمات', resource: 'broadcasts', action: 'update' },
  { name: 'broadcasts:delete', description: 'حذف التعميمات', resource: 'broadcasts', action: 'delete' },

  // صلاحيات الهيكل التنظيمي
  { name: 'organization:create', description: 'إنشاء وحدات تنظيمية جديدة', resource: 'organization', action: 'create' },
  { name: 'organization:read', description: 'قراءة الهيكل التنظيمي', resource: 'organization', action: 'read' },
  { name: 'organization:update', description: 'تحديث الهيكل التنظيمي', resource: 'organization', action: 'update' },
  { name: 'organization:delete', description: 'حذف وحدات من الهيكل التنظيمي', resource: 'organization', action: 'delete' },

  // صلاحيات النظام
  { name: 'system:settings', description: 'تعديل إعدادات النظام', resource: 'system', action: 'settings' },
  { name: 'system:logs', description: 'عرض سجلات النظام', resource: 'system', action: 'logs' }
];

// إنشاء الجداول
async function createTables() {
  console.log('إنشاء جداول الأدوار والصلاحيات...');

  try {
    // إنشاء جدول الأدوار
    const createRolesTable = `
      CREATE TABLE IF NOT EXISTS roles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    const { error: rolesTableError } = await supabase.rpc('exec_sql', { sql: createRolesTable });
    if (rolesTableError) {
      console.error('خطأ في إنشاء جدول الأدوار:', rolesTableError);
    } else {
      console.log('تم إنشاء جدول الأدوار بنجاح');
    }

    // إنشاء جدول الصلاحيات
    const createPermissionsTable = `
      CREATE TABLE IF NOT EXISTS permissions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        resource VARCHAR(50) NOT NULL,
        action VARCHAR(50) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(resource, action)
      );
    `;

    const { error: permissionsTableError } = await supabase.rpc('exec_sql', { sql: createPermissionsTable });
    if (permissionsTableError) {
      console.error('خطأ في إنشاء جدول الصلاحيات:', permissionsTableError);
    } else {
      console.log('تم إنشاء جدول الصلاحيات بنجاح');
    }

    // إنشاء جدول العلاقة بين الأدوار والصلاحيات
    const createRolePermissionsTable = `
      CREATE TABLE IF NOT EXISTS role_permissions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
        permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(role_id, permission_id)
      );
    `;

    const { error: rolePermissionsTableError } = await supabase.rpc('exec_sql', { sql: createRolePermissionsTable });
    if (rolePermissionsTableError) {
      console.error('خطأ في إنشاء جدول العلاقة بين الأدوار والصلاحيات:', rolePermissionsTableError);
    } else {
      console.log('تم إنشاء جدول العلاقة بين الأدوار والصلاحيات بنجاح');
    }

    // تحديث جدول المستخدمين
    const updateProfilesTable = `
      ALTER TABLE profiles ADD COLUMN IF NOT EXISTS role_id UUID REFERENCES roles(id);
    `;

    const { error: updateProfilesError } = await supabase.rpc('exec_sql', { sql: updateProfilesTable });
    if (updateProfilesError) {
      console.error('خطأ في تحديث جدول المستخدمين:', updateProfilesError);
    } else {
      console.log('تم تحديث جدول المستخدمين بنجاح');
    }
  } catch (error) {
    console.error('خطأ في إنشاء الجداول:', error);
  }
}

// إضافة الأدوار
async function addRoles() {
  console.log('إضافة الأدوار...');

  for (const role of roles) {
    const { data, error } = await supabase
      .from('roles')
      .insert(role)
      .select();

    if (error) {
      if (error.code === '23505') { // رمز الخطأ للتكرار
        console.log(`الدور ${role.name} موجود بالفعل`);
      } else {
        console.error(`خطأ في إضافة الدور ${role.name}:`, error);
      }
    } else {
      console.log(`تم إضافة الدور ${role.name} بنجاح`);
    }
  }
}

// إضافة الصلاحيات
async function addPermissions() {
  console.log('إضافة الصلاحيات...');

  for (const permission of permissions) {
    const { data, error } = await supabase
      .from('permissions')
      .insert(permission)
      .select();

    if (error) {
      if (error.code === '23505') { // رمز الخطأ للتكرار
        console.log(`الصلاحية ${permission.name} موجودة بالفعل`);
      } else {
        console.error(`خطأ في إضافة الصلاحية ${permission.name}:`, error);
      }
    } else {
      console.log(`تم إضافة الصلاحية ${permission.name} بنجاح`);
    }
  }
}

// ربط الصلاحيات بالأدوار
async function linkRolePermissions() {
  console.log('ربط الصلاحيات بالأدوار...');

  // الحصول على الأدوار
  const { data: rolesData, error: rolesError } = await supabase
    .from('roles')
    .select('id, name');

  if (rolesError) {
    console.error('خطأ في الحصول على الأدوار:', rolesError);
    return;
  }

  // الحصول على الصلاحيات
  const { data: permissionsData, error: permissionsError } = await supabase
    .from('permissions')
    .select('id, resource, action');

  if (permissionsError) {
    console.error('خطأ في الحصول على الصلاحيات:', permissionsError);
    return;
  }

  // ربط الصلاحيات بدور المدير (admin)
  const adminRole = rolesData.find(role => role.name === 'admin');
  if (adminRole) {
    for (const permission of permissionsData) {
      const { error } = await supabase
        .from('role_permissions')
        .insert({
          role_id: adminRole.id,
          permission_id: permission.id
        });

      if (error && error.code !== '23505') {
        console.error(`خطأ في ربط الصلاحية بدور المدير:`, error);
      }
    }
    console.log('تم ربط جميع الصلاحيات بدور المدير بنجاح');
  }

  // ربط الصلاحيات بدور المدير (manager)
  const managerRole = rolesData.find(role => role.name === 'manager');
  if (managerRole) {
    for (const permission of permissionsData) {
      // إضافة صلاحيات محددة لدور المدير
      if (
        (permission.resource === 'documents' && ['create', 'read', 'update'].includes(permission.action)) ||
        (permission.resource === 'messages' && ['create', 'read', 'update'].includes(permission.action)) ||
        (permission.resource === 'broadcasts' && ['create', 'read', 'update'].includes(permission.action)) ||
        (permission.resource === 'organization' && ['create', 'read', 'update'].includes(permission.action)) ||
        (permission.resource === 'users' && permission.action === 'read')
      ) {
        const { error } = await supabase
          .from('role_permissions')
          .insert({
            role_id: managerRole.id,
            permission_id: permission.id
          });

        if (error && error.code !== '23505') {
          console.error(`خطأ في ربط الصلاحية بدور المدير:`, error);
        }
      }
    }
    console.log('تم ربط الصلاحيات المحددة بدور المدير بنجاح');
  }

  // ربط الصلاحيات بدور المستخدم العادي (user)
  const userRole = rolesData.find(role => role.name === 'user');
  if (userRole) {
    for (const permission of permissionsData) {
      // إضافة صلاحيات محددة لدور المستخدم
      if (
        (permission.resource === 'documents' && ['create', 'read'].includes(permission.action)) ||
        (permission.resource === 'messages' && ['create', 'read'].includes(permission.action)) ||
        (permission.resource === 'broadcasts' && permission.action === 'read') ||
        (permission.resource === 'organization' && permission.action === 'read')
      ) {
        const { error } = await supabase
          .from('role_permissions')
          .insert({
            role_id: userRole.id,
            permission_id: permission.id
          });

        if (error && error.code !== '23505') {
          console.error(`خطأ في ربط الصلاحية بدور المستخدم:`, error);
        }
      }
    }
    console.log('تم ربط الصلاحيات المحددة بدور المستخدم بنجاح');
  }
}

// تنفيذ الإعداد
async function setup() {
  try {
    await createTables();
    await addRoles();
    await addPermissions();
    await linkRolePermissions();
    console.log('تم إعداد الأدوار والصلاحيات بنجاح');
  } catch (error) {
    console.error('خطأ في إعداد الأدوار والصلاحيات:', error);
  }
}

setup();
