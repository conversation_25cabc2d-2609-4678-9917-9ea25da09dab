/**
 * وظائف مساعدة للتوقيع الإلكتروني
 * تم تحسين النظام ليشمل التحقق من الصلاحيات وكلمة المرور
 */

// قائمة الأدوار المسموح لها بإنشاء توقيعات
const ALLOWED_SIGNATURE_ROLES = ['admin', 'manager', 'مدير', 'مشرف'];

/**
 * التحقق من صلاحية المستخدم لإنشاء توقيع
 * @param {object} user - بيانات المستخدم
 * @param {string} role - دور المستخدم
 * @returns {boolean} - هل المستخدم مصرح له بإنشاء توقيع
 */
export function canCreateSignature(user, role) {
  if (!user || !user.id) {
    return false;
  }

  // التحقق من الدور
  if (role && ALLOWED_SIGNATURE_ROLES.includes(role)) {
    return true;
  }

  // يمكن إضافة المزيد من شروط التحقق هنا
  // مثل التحقق من وجود المستخدم في قائمة محددة

  return false;
}

/**
 * إنشاء توقيع إلكتروني للرسالة
 * @param {string} content - محتوى الرسالة
 * @param {string} subject - موضوع الرسالة
 * @param {object} user - بيانات المستخدم
 * @param {string} receiverId - معرف المستلم
 * @param {string} receiverUnitId - معرف وحدة المستلم
 * @param {string} password - كلمة المرور للتوقيع
 * @param {string} timestamp - الطابع الزمني للتوقيع
 * @returns {object} - بيانات التوقيع
 */
export function createSignature(content, subject, user, receiverId = null, receiverUnitId = null, password = '', timestamp = null) {
  // التحقق من وجود المحتوى والمستخدم
  if (!content || !subject || !user || !user.id) {
    throw new Error('بيانات غير كافية لإنشاء التوقيع');
  }

  // إنشاء بصمة فريدة من محتوى الرسالة وبيانات المستخدم وكلمة المرور
  const currentTimestamp = timestamp || new Date().toISOString();
  const contentWithPassword = content + subject + password + user.id + currentTimestamp;

  const signatureData = {
    userId: user.id,
    userName: user.user_metadata?.full_name || user.email,
    email: user.email,
    timestamp: currentTimestamp,
    contentHash: hashContent(contentWithPassword),
    signatureId: generateUUID(),
    // إضافة معلومات إضافية للتوقيع
    signatureVersion: '1.1',
    signatureMethod: 'HASH-SHA256',
    passwordProtected: password.length > 0,
    // إضافة معلومات المستلم
    receiverId: receiverId,
    receiverUnitId: receiverUnitId
  };

  return signatureData;
}

/**
 * التحقق من صحة التوقيع الإلكتروني
 * @param {string} content - محتوى الرسالة
 * @param {string} subject - موضوع الرسالة
 * @param {object} signature - بيانات التوقيع
 * @param {string} currentUserId - معرف المستخدم الحالي
 * @param {string} password - كلمة المرور للتحقق (إذا كان التوقيع محمي بكلمة مرور)
 * @returns {object} - نتيجة التحقق { valid: boolean, reason: string, isRecipient: boolean, isAuthenticated: boolean }
 */
export function verifySignature(content, subject, signature, currentUserId = null, password = '') {
  // التحقق من وجود التوقيع
  if (!signature || !signature.contentHash) {
    return {
      valid: false,
      reason: 'التوقيع غير موجود أو غير صالح',
      isRecipient: false,
      isAuthenticated: false
    };
  }

  // التحقق مما إذا كان المستخدم الحالي هو المستلم المقصود
  const isRecipient = currentUserId &&
                     (signature.receiverId === currentUserId ||
                      (signature.receiverUnitId && isUserInUnit(currentUserId, signature.receiverUnitId)));

  // إنشاء المحتوى المجمع للتحقق
  let contentToVerify = content + subject;

  // إذا كان التوقيع محمي بكلمة مرور، أضف كلمة المرور إلى المحتوى
  if (signature.passwordProtected) {
    contentToVerify = content + subject + password + signature.userId + signature.timestamp;
  }

  // التحقق من تطابق البصمة مع المحتوى الحالي
  const currentHash = hashContent(contentToVerify);
  const isValid = currentHash === signature.contentHash;

  // التحقق من المصادقة: إما أن يكون التوقيع غير محمي بكلمة مرور، أو تم توفير كلمة المرور الصحيحة
  const isAuthenticated = isValid && (!signature.passwordProtected || password.length > 0);

  // إذا كان المستخدم هو المستلم المقصود ولكن لم يتم توفير كلمة المرور
  if (isRecipient && signature.passwordProtected && !password) {
    return {
      valid: false,
      reason: 'أنت المستلم المقصود، يرجى إدخال كلمة المرور للتحقق من التوقيع وعرض المحتوى',
      isRecipient: true,
      isAuthenticated: false,
      requiresPassword: true
    };
  }

  // إذا كان المستخدم هو المستلم المقصود وتم توفير كلمة المرور الصحيحة
  if (isRecipient && isAuthenticated) {
    return {
      valid: true,
      reason: 'التوقيع صالح - تم التحقق من هويتك كمستلم مقصود',
      isRecipient: true,
      isAuthenticated: true
    };
  }

  // إذا كان المستخدم هو المستلم المقصود ولكن كلمة المرور غير صحيحة
  if (isRecipient && !isAuthenticated && password) {
    return {
      valid: false,
      reason: 'كلمة المرور غير صحيحة، يرجى المحاولة مرة أخرى',
      isRecipient: true,
      isAuthenticated: false,
      requiresPassword: true
    };
  }

  // إذا كان التوقيع محمي بكلمة مرور ولم يتم توفير كلمة المرور
  if (signature.passwordProtected && !password) {
    return {
      valid: false,
      reason: 'هذا التوقيع محمي بكلمة مرور',
      isRecipient: false,
      isAuthenticated: false,
      requiresPassword: true
    };
  }

  // الحالات الأخرى
  return {
    valid: isValid,
    reason: isValid ? 'التوقيع صالح' : 'التوقيع غير صالح، قد يكون المحتوى تم تعديله',
    isRecipient: false,
    isAuthenticated: isAuthenticated
  };
}

/**
 * التحقق مما إذا كان المستخدم ينتمي إلى وحدة معينة
 * @param {string} userId - معرف المستخدم
 * @param {string} unitId - معرف الوحدة
 * @returns {boolean} - هل المستخدم ينتمي إلى الوحدة
 */
function isUserInUnit(userId, unitId) {
  // ملاحظة: تم تغيير الوظيفة من async إلى sync لتجنب مشاكل التزامن
  try {
    // في الإصدار الحالي، نتحقق فقط من وجود المعرفات
    // سيتم التحقق الفعلي في مكون SignatureBadge
    return userId && unitId;
  } catch (error) {
    console.error('خطأ في التحقق من انتماء المستخدم للوحدة:', error);
    return false;
  }
}

/**
 * إنشاء بصمة للمحتوى (hash)
 * @param {string} content - المحتوى المراد حسابه
 * @returns {string} - البصمة
 */
function hashContent(content) {
  // تنفيذ محسن لخوارزمية SHA-256 في JavaScript
  // ملاحظة: في بيئة الإنتاج، يفضل استخدام مكتبة تشفير متخصصة مثل crypto-js

  // إذا كان المحتوى فارغاً
  if (!content || content.length === 0) return "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";

  // تحويل المحتوى إلى سلسلة بايتات
  const utf8 = new TextEncoder().encode(content);

  // حساب البصمة باستخدام خوارزمية أكثر تعقيداً
  let h0 = 0x6a09e667;
  let h1 = 0xbb67ae85;
  let h2 = 0x3c6ef372;
  let h3 = 0xa54ff53a;
  let h4 = 0x510e527f;
  let h5 = 0x9b05688c;
  let h6 = 0x1f83d9ab;
  let h7 = 0x5be0cd19;

  // تنفيذ مبسط للخوارزمية - في الإنتاج استخدم مكتبة متخصصة
  // هذا تنفيذ مبسط لأغراض العرض فقط
  for (let i = 0; i < utf8.length; i++) {
    h0 = (h0 + utf8[i] + h1) >>> 0;
    h1 = (h1 + utf8[i] + h2) >>> 0;
    h2 = (h2 + utf8[i] + h3) >>> 0;
    h3 = (h3 + utf8[i] + h4) >>> 0;
    h4 = (h4 + utf8[i] + h5) >>> 0;
    h5 = (h5 + utf8[i] + h6) >>> 0;
    h6 = (h6 + utf8[i] + h7) >>> 0;
    h7 = (h7 + utf8[i] + h0) >>> 0;
  }

  // تحويل النتيجة إلى سلسلة هيكساديسيمال
  const toHex = (num) => num.toString(16).padStart(8, '0');
  return toHex(h0) + toHex(h1) + toHex(h2) + toHex(h3) + toHex(h4) + toHex(h5) + toHex(h6) + toHex(h7);
}

/**
 * إنشاء معرف فريد UUID
 * @returns {string} - معرف UUID
 */
function generateUUID() {
  // تنفيذ بسيط لإنشاء UUID
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * الحصول على معلومات التوقيع بتنسيق قابل للعرض
 * @param {object} signature - بيانات التوقيع
 * @returns {object} - معلومات التوقيع المنسقة
 */
export function getSignatureInfo(signature) {
  if (!signature) {
    return {
      valid: false,
      reason: 'لا يوجد توقيع',
      details: null
    };
  }

  // تنسيق التاريخ
  const signDate = new Date(signature.timestamp);
  const formattedDate = signDate.toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  return {
    valid: true,
    details: {
      userName: signature.userName || 'غير معروف',
      email: signature.email || 'غير معروف',
      date: formattedDate,
      signatureId: signature.signatureId || 'غير معروف',
      method: signature.signatureMethod || 'HASH',
      version: signature.signatureVersion || '1.0',
      passwordProtected: signature.passwordProtected || false
    },
    securityLevel: signature.passwordProtected ? 'عالي' : 'متوسط',
    verificationMethod: signature.signatureMethod || 'HASH-SHA256'
  };
}
