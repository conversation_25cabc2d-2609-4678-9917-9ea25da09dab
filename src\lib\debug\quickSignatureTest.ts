/**
 * اختبار سريع لتشخيص مشكلة التوقيع في المرفقات
 */

import { supabase } from '$lib/supabase';
import { DocumentStorageService } from '$lib/services/documentStorageService';

/**
 * اختبار سريع لمحتوى المرفق
 */
export async function quickAttachmentTest(signatureRequestId: string): Promise<void> {
  console.log('🚀 بدء الاختبار السريع للمرفق...');
  console.log('معرف طلب التوقيع:', signatureRequestId);

  try {
    // 1. البحث عن الرسالة
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('id, attachment, attachment_content_url')
      .contains('attachment', { signature_request_id: signatureRequestId })
      .limit(1);

    if (messagesError) {
      console.error('❌ خطأ في البحث عن الرسالة:', messagesError);
      return;
    }

    if (!messages || messages.length === 0) {
      console.error('❌ لم يتم العثور على رسالة');
      return;
    }

    const message = messages[0];
    console.log('✅ تم العثور على الرسالة:', message.id);

    // 2. جلب المحتوى
    let content = message.attachment?.content;
    let contentSource = 'database';

    if (!content && message.attachment_content_url) {
      console.log('☁️ جلب المحتوى من Storage...');
      content = await DocumentStorageService.getDocumentContent(message.attachment_content_url);
      contentSource = 'storage';
    }

    if (!content) {
      console.error('❌ لا يوجد محتوى');
      return;
    }

    console.log(`✅ تم جلب المحتوى من ${contentSource}`);
    console.log('طول المحتوى:', content.length);

    // 3. البحث عن النصوص المهمة
    const searchTerms = [
      'في انتظار التوقيع الإلكتروني',
      'سيتم عرض التوقيع الإلكتروني هنا',
      'توقيع إلكتروني معتمد',
      'background-color: #fffbeb',
      'background-color: #f0fdf4'
    ];

    console.log('\n🔍 البحث عن النصوص المهمة:');
    searchTerms.forEach((term, index) => {
      const found = content.includes(term);
      console.log(`${index + 1}. "${term}": ${found ? '✅ موجود' : '❌ غير موجود'}`);
      
      if (found) {
        const termIndex = content.indexOf(term);
        const start = Math.max(0, termIndex - 100);
        const end = Math.min(content.length, termIndex + term.length + 100);
        const context = content.substring(start, end);
        console.log(`   السياق: ...${context}...`);
      }
    });

    // 4. تحليل HTML
    console.log('\n🔍 تحليل HTML:');
    
    // البحث عن divs بألوان خلفية محددة
    const yellowDivs = content.match(/<div[^>]*background-color:\s*#fffbeb[^>]*>/g);
    const greenDivs = content.match(/<div[^>]*background-color:\s*#f0fdf4[^>]*>/g);
    
    console.log(`Divs صفراء (#fffbeb): ${yellowDivs ? yellowDivs.length : 0}`);
    console.log(`Divs خضراء (#f0fdf4): ${greenDivs ? greenDivs.length : 0}`);

    if (yellowDivs) {
      console.log('أول div أصفر:', yellowDivs[0]);
    }

    if (greenDivs) {
      console.log('أول div أخضر:', greenDivs[0]);
    }

    // 5. اختبار أنماط الاستبدال
    console.log('\n🧪 اختبار أنماط الاستبدال:');
    
    const testPatterns = [
      {
        name: 'النمط الأصلي',
        pattern: /<div style="background-color: #fffbeb;[^>]*>[\s\S]*?في انتظار التوقيع الإلكتروني[\s\S]*?<\/div>\s*<\/div>/g
      },
      {
        name: 'النمط المرن',
        pattern: /<div[^>]*background-color:\s*#fffbeb[^>]*>[\s\S]*?في انتظار التوقيع الإلكتروني[\s\S]*?<\/div>/g
      },
      {
        name: 'النمط الكامل',
        pattern: /<div style="background-color: #fffbeb; padding: 20px; border: 2px solid #f59e0b; border-radius: 8px; text-align: center;">[\s\S]*?سيتم عرض التوقيع الإلكتروني هنا بعد إتمام عملية التوقيع[\s\S]*?<\/div>/g
      }
    ];

    testPatterns.forEach((test, index) => {
      const matches = content.match(test.pattern);
      console.log(`${index + 1}. ${test.name}: ${matches ? `✅ ${matches.length} مطابقة` : '❌ لا توجد مطابقات'}`);
      
      if (matches) {
        console.log(`   أول مطابقة: ${matches[0].substring(0, 200)}...`);
      }
    });

    // 6. محاولة استبدال تجريبية
    console.log('\n🔄 محاولة استبدال تجريبية:');
    
    const testSignature = '<div style="background-color: #f0fdf4;">توقيع تجريبي</div>';
    let testContent = content;
    const originalLength = testContent.length;
    
    // جرب الاستبدال البسيط
    testContent = testContent.replace(/في انتظار التوقيع الإلكتروني/g, 'توقيع تجريبي');
    const newLength = testContent.length;
    
    console.log(`الاستبدال التجريبي: ${newLength !== originalLength ? '✅ نجح' : '❌ فشل'}`);
    console.log(`الطول الأصلي: ${originalLength}, الطول الجديد: ${newLength}`);

    // 7. عرض عينات من المحتوى
    console.log('\n📄 عينات من المحتوى:');
    console.log('البداية (500 حرف):');
    console.log(content.substring(0, 500));
    
    console.log('\nالنهاية (500 حرف):');
    console.log(content.substring(Math.max(0, content.length - 500)));

    // البحث عن منطقة التوقيع
    const signatureIndex = content.indexOf('في انتظار التوقيع الإلكتروني');
    if (signatureIndex >= 0) {
      console.log('\nمنطقة التوقيع (±300 حرف):');
      const start = Math.max(0, signatureIndex - 300);
      const end = Math.min(content.length, signatureIndex + 300);
      console.log(content.substring(start, end));
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار السريع:', error);
  }

  console.log('\n🏁 انتهى الاختبار السريع');
}

/**
 * اختبار إنشاء توقيع جديد
 */
export async function testSignatureCreation(): Promise<void> {
  console.log('🧪 اختبار إنشاء توقيع جديد...');

  const testSignatureHTML = `
    <div style="background-color: #f0fdf4; padding: 20px; border: 2px solid #10b981; border-radius: 8px; text-align: center;">
      <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px; gap: 10px;">
        <h4 style="color: #065f46; margin: 0; font-size: 18px; font-weight: bold;">توقيع إلكتروني معتمد</h4>
      </div>
      <div style="text-align: right; margin-bottom: 15px;">
        <div style="display: flex; margin-bottom: 8px; align-items: center;">
          <span style="font-weight: bold; color: #374151; min-width: 120px; margin-left: 10px;">الموقع:</span>
          <span style="color: #1f2937;">مستخدم تجريبي</span>
        </div>
        <div style="display: flex; margin-bottom: 8px; align-items: center;">
          <span style="font-weight: bold; color: #374151; min-width: 120px; margin-left: 10px;">تاريخ التوقيع:</span>
          <span style="color: #1f2937;">${new Date().toLocaleString('ar-SA')}</span>
        </div>
      </div>
      <div style="text-align: center;">
        <div style="display: inline-flex; align-items: center; gap: 8px; background: #10b981; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold;">
          <span>تم التحقق من صحة التوقيع الإلكتروني</span>
        </div>
      </div>
    </div>
  `;

  console.log('HTML التوقيع التجريبي:');
  console.log(testSignatureHTML);
  
  console.log('طول HTML التوقيع:', testSignatureHTML.length);
}

/**
 * تشغيل جميع الاختبارات
 */
export async function runAllTests(signatureRequestId: string): Promise<void> {
  console.log('🚀🚀🚀 بدء جميع الاختبارات 🚀🚀🚀');
  
  await quickAttachmentTest(signatureRequestId);
  await testSignatureCreation();
  
  console.log('🏁🏁🏁 انتهت جميع الاختبارات 🏁🏁🏁');
}
