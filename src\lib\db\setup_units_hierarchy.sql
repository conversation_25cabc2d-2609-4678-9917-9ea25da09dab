-- ملف إعداد الهيكل التنظيمي
-- يقوم هذا الملف بإضافة الوحدات التنظيمية الفرعية وربطها بالوحدات الأم

-- إضافة الفروع والأقسام
DO $$
DECLARE
  hr_id UUID;
  it_id UUID;
  general_id UUID;
BEGIN
  -- الحصول على معرفات الإدارات الرئيسية
  SELECT id INTO general_id FROM units WHERE name = 'الإدارة العامة';
  SELECT id INTO hr_id FROM units WHERE name = 'إدارة الموارد البشرية';
  SELECT id INTO it_id FROM units WHERE name = 'إدارة تقنية المعلومات';
  
  -- إضافة الإدارات الأخرى
  IF general_id IS NOT NULL THEN
    -- إضافة إدارة الشؤون المالية
    INSERT INTO units (name, type, description, parent_id) 
    VALUES ('إدارة الشؤون المالية', 'إدارة', 'إدارة الميزانية والمحاسبة', general_id)
    ON CONFLICT (name) DO NOTHING;
    
    -- إضافة إدارة المشاريع
    INSERT INTO units (name, type, description, parent_id) 
    VALUES ('إدارة المشاريع', 'إدارة', 'إدارة تخطيط وتنفيذ المشاريع', general_id)
    ON CONFLICT (name) DO NOTHING;
  END IF;
  
  -- إضافة أقسام إدارة الموارد البشرية
  IF hr_id IS NOT NULL THEN
    INSERT INTO units (name, type, description, parent_id) 
    VALUES ('قسم التوظيف', 'قسم', 'قسم استقطاب وتوظيف الكفاءات', hr_id)
    ON CONFLICT (name) DO NOTHING;
    
    INSERT INTO units (name, type, description, parent_id) 
    VALUES ('قسم التدريب والتطوير', 'قسم', 'قسم تدريب وتطوير الموظفين', hr_id)
    ON CONFLICT (name) DO NOTHING;
    
    INSERT INTO units (name, type, description, parent_id) 
    VALUES ('قسم شؤون الموظفين', 'قسم', 'قسم متابعة شؤون الموظفين', hr_id)
    ON CONFLICT (name) DO NOTHING;
  END IF;
  
  -- إضافة أقسام إدارة تقنية المعلومات
  IF it_id IS NOT NULL THEN
    INSERT INTO units (name, type, description, parent_id) 
    VALUES ('قسم البنية التحتية', 'قسم', 'قسم إدارة البنية التحتية التقنية', it_id)
    ON CONFLICT (name) DO NOTHING;
    
    INSERT INTO units (name, type, description, parent_id) 
    VALUES ('قسم تطوير البرمجيات', 'قسم', 'قسم تطوير وصيانة البرمجيات', it_id)
    ON CONFLICT (name) DO NOTHING;
    
    INSERT INTO units (name, type, description, parent_id) 
    VALUES ('قسم الدعم الفني', 'قسم', 'قسم تقديم الدعم الفني للمستخدمين', it_id)
    ON CONFLICT (name) DO NOTHING;
    
    INSERT INTO units (name, type, description, parent_id) 
    VALUES ('قسم أمن المعلومات', 'قسم', 'قسم حماية وأمن المعلومات', it_id)
    ON CONFLICT (name) DO NOTHING;
  END IF;
END $$;

-- إضافة المكاتب الفرعية
INSERT INTO units (name, type, description) 
VALUES ('مكتب الرياض', 'مكتب', 'المكتب الفرعي في مدينة الرياض')
ON CONFLICT (name) DO NOTHING;

INSERT INTO units (name, type, description) 
VALUES ('مكتب جدة', 'مكتب', 'المكتب الفرعي في مدينة جدة')
ON CONFLICT (name) DO NOTHING;

INSERT INTO units (name, type, description) 
VALUES ('مكتب الدمام', 'مكتب', 'المكتب الفرعي في مدينة الدمام')
ON CONFLICT (name) DO NOTHING;

-- ربط المكاتب الفرعية بالإدارة العامة
DO $$
DECLARE
  general_id UUID;
  riyadh_id UUID;
  jeddah_id UUID;
  dammam_id UUID;
BEGIN
  -- الحصول على معرفات الإدارة العامة والمكاتب
  SELECT id INTO general_id FROM units WHERE name = 'الإدارة العامة';
  SELECT id INTO riyadh_id FROM units WHERE name = 'مكتب الرياض';
  SELECT id INTO jeddah_id FROM units WHERE name = 'مكتب جدة';
  SELECT id INTO dammam_id FROM units WHERE name = 'مكتب الدمام';
  
  -- ربط المكاتب بالإدارة العامة
  IF general_id IS NOT NULL THEN
    IF riyadh_id IS NOT NULL THEN
      UPDATE units SET parent_id = general_id WHERE id = riyadh_id;
    END IF;
    
    IF jeddah_id IS NOT NULL THEN
      UPDATE units SET parent_id = general_id WHERE id = jeddah_id;
    END IF;
    
    IF dammam_id IS NOT NULL THEN
      UPDATE units SET parent_id = general_id WHERE id = dammam_id;
    END IF;
  END IF;
END $$;
