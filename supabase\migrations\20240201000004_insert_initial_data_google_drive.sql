-- إنشاء جدول إعدادات المنظمة إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.organization_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  logo TEXT,
  right_logo TEXT,
  left_logo TEXT,
  default_stamp TEXT,
  address TEXT,
  phone TEXT,
  email TEXT,
  website TEXT,
  footer_text TEXT,
  header_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إدخال إعدادات المنظمة الافتراضية مع روابط Google Drive
INSERT INTO public.organization_settings (
  name,
  logo,
  right_logo,
  left_logo,
  default_stamp,
  address,
  phone,
  email,
  website,
  footer_text,
  header_text
)
VALUES (
  'منظمة سعد أبو جناح',
  'https://drive.google.com/uc?export=view&id=YOUR_LOGO_ID',
  'https://drive.google.com/uc?export=view&id=YOUR_RIGHT_LOGO_ID',
  'https://drive.google.com/uc?export=view&id=YOUR_LEFT_LOGO_ID',
  'https://drive.google.com/uc?export=view&id=YOUR_STAMP_ID',
  'ليبيا - طرابلس',
  '+218 91 1234567',
  '<EMAIL>',
  'www.saadabujnah.ly',
  'جميع الحقوق محفوظة © {year} منظمة سعد أبو جناح',
  'منظمة سعد أبو جناح - المراسلات الرسمية'
)
ON CONFLICT (id) DO NOTHING;

-- التحقق من هيكل جدول permissions قبل إدخال البيانات
DO $$
DECLARE
  permissions_columns RECORD;
BEGIN
  -- الحصول على معلومات عن أعمدة جدول permissions
  FOR permissions_columns IN
    SELECT column_name
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'permissions'
  LOOP
    RAISE NOTICE 'Column in permissions table: %', permissions_columns.column_name;
  END LOOP;

  -- التحقق من وجود جدول permissions
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'permissions'
  ) THEN
    -- محاولة إدخال البيانات مع تحديد قيم لجميع الأعمدة المطلوبة
    BEGIN
      -- إذا كان الجدول يحتوي على أعمدة name, description, resource, action
      EXECUTE 'INSERT INTO public.permissions (name, description, resource, action)
      VALUES 
      (''create_official_document'', ''إنشاء وثيقة رسمية'', ''official_documents'', ''create''),
      (''approve_official_document'', ''اعتماد وثيقة رسمية'', ''official_documents'', ''approve''),
      (''manage_templates'', ''إدارة قوالب الوثائق'', ''document_templates'', ''manage'')
      ON CONFLICT (name) DO NOTHING';
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'Error inserting with resource and action: %', SQLERRM;
      
      -- محاولة أخرى بدون عمود action
      BEGIN
        EXECUTE 'INSERT INTO public.permissions (name, description, resource)
        VALUES 
        (''create_official_document'', ''إنشاء وثيقة رسمية'', ''official_documents''),
        (''approve_official_document'', ''اعتماد وثيقة رسمية'', ''official_documents''),
        (''manage_templates'', ''إدارة قوالب الوثائق'', ''document_templates'')
        ON CONFLICT (name) DO NOTHING';
      EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error inserting with resource only: %', SQLERRM;
        
        -- محاولة أخيرة بدون أعمدة إضافية
        BEGIN
          EXECUTE 'INSERT INTO public.permissions (name, description)
          VALUES 
          (''create_official_document'', ''إنشاء وثيقة رسمية''),
          (''approve_official_document'', ''اعتماد وثيقة رسمية''),
          (''manage_templates'', ''إدارة قوالب الوثائق'')
          ON CONFLICT (name) DO NOTHING';
        EXCEPTION WHEN OTHERS THEN
          RAISE NOTICE 'Error inserting with name and description only: %', SQLERRM;
        END;
      END;
    END;
  END IF;
END
$$;
