<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib/supabase';
  import { PermissionService } from '$lib/services/permissionService';
  import { RoleType, Resource, Action } from '$lib/types/permissions';

  let isAdmin = false;
  let loading = true;
  let units = [];
  let filteredUnits = [];
  let searchTerm = '';
  let unitTypes = ['إدارة', 'فرع', 'مكتب', 'قسم'];
  let selectedType = '';

  // نموذج إضافة وحدة جديدة
  let showAddForm = false;
  let newUnit = {
    name: '',
    type: 'إدارة',
    parent_id: null,
    description: ''
  };

  // نموذج تعديل وحدة
  let showEditForm = false;
  let editingUnit = null;

  onMount(async () => {
    try {
      // الحصول على المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        goto('/login');
        return;
      }

      // التحقق من صلاحيات المستخدم
      isAdmin = await PermissionService.checkRole(user.id, RoleType.ADMIN);

      if (!isAdmin) {
        // التحقق من صلاحيات المدير
        const isManager = await PermissionService.checkRole(user.id, RoleType.MANAGER);
        const hasPermission = await PermissionService.checkPermission(
          user.id,
          Resource.ORGANIZATION,
          Action.UPDATE
        );

        if (!isManager && !hasPermission) {
          goto('/dashboard');
          return;
        }
      }

      // جلب الوحدات التنظيمية
      await loadUnits();
    } catch (error) {
      console.error('Error loading units page:', error);
    } finally {
      loading = false;
    }
  });

  async function loadUnits() {
    try {
      const { data, error } = await supabase
        .from('units')
        .select(`
          id,
          name,
          type,
          description,
          parent_id,
          parent:parent_id (
            name
          )
        `)
        .order('type')
        .order('name');

      if (error) throw error;

      units = data || [];
      filteredUnits = [...units];
    } catch (error) {
      console.error('Error loading units:', error);
    }
  }

  function filterUnits() {
    filteredUnits = units.filter(unit => {
      const matchesSearch = searchTerm === '' ||
        unit.name.includes(searchTerm) ||
        (unit.description && unit.description.includes(searchTerm));

      const matchesType = selectedType === '' || unit.type === selectedType;

      return matchesSearch && matchesType;
    });
  }

  $: {
    if (units.length > 0) {
      filterUnits();
    }
  }

  async function handleAddUnit() {
    try {
      const { data, error } = await supabase
        .from('units')
        .insert([newUnit])
        .select();

      if (error) throw error;

      if (data && data.length > 0) {
        // إضافة الوحدة الجديدة إلى القائمة
        await loadUnits();

        // إعادة تعيين النموذج
        newUnit = {
          name: '',
          type: 'إدارة',
          parent_id: null,
          description: ''
        };

        showAddForm = false;
      }
    } catch (error) {
      console.error('Error adding unit:', error);
      alert('حدث خطأ أثناء إضافة الوحدة');
    }
  }

  function openEditForm(unit) {
    editingUnit = { ...unit };
    showEditForm = true;
  }

  async function handleEditUnit() {
    try {
      if (!editingUnit || !editingUnit.id) return;

      const { error } = await supabase
        .from('units')
        .update({
          name: editingUnit.name,
          type: editingUnit.type,
          parent_id: editingUnit.parent_id,
          description: editingUnit.description
        })
        .eq('id', editingUnit.id);

      if (error) throw error;

      // تحديث القائمة
      await loadUnits();

      showEditForm = false;
      editingUnit = null;
    } catch (error) {
      console.error('Error updating unit:', error);
      alert('حدث خطأ أثناء تحديث الوحدة');
    }
  }

  async function handleDeleteUnit(unitId) {
    if (!confirm('هل أنت متأكد من حذف هذه الوحدة؟')) return;

    try {
      // التحقق من وجود وحدات فرعية
      const { count, error: countError } = await supabase
        .from('units')
        .select('*', { count: 'exact', head: true })
        .eq('parent_id', unitId);

      if (countError) throw countError;

      if (count > 0) {
        alert('لا يمكن حذف هذه الوحدة لأنها تحتوي على وحدات فرعية');
        return;
      }

      // التحقق من وجود مستخدمين مرتبطين بالوحدة
      const { count: userCount, error: userCountError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .eq('unit_id', unitId);

      if (userCountError) throw userCountError;

      if (userCount > 0) {
        alert('لا يمكن حذف هذه الوحدة لأنها مرتبطة بمستخدمين');
        return;
      }

      // حذف الوحدة
      const { error } = await supabase
        .from('units')
        .delete()
        .eq('id', unitId);

      if (error) throw error;

      // تحديث القائمة
      await loadUnits();
    } catch (error) {
      console.error('Error deleting unit:', error);
      alert('حدث خطأ أثناء حذف الوحدة');
    }
  }
</script>

<svelte:head>
  <title>إدارة الوحدات التنظيمية</title>
</svelte:head>

<div class="container mx-auto p-4 rtl" dir="rtl">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">إدارة الوحدات التنظيمية</h1>

    <button
      class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
      on:click={() => showAddForm = true}
    >
      إضافة وحدة جديدة
    </button>
  </div>

  {#if loading}
    <div class="flex justify-center items-center h-64">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  {:else}
    <!-- أدوات البحث والتصفية -->
    <div class="bg-white p-4 rounded-lg shadow-md mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">بحث</label>
          <input
            id="search"
            type="text"
            bind:value={searchTerm}
            placeholder="ابحث عن وحدة..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>

        <div class="w-full md:w-1/4">
          <label for="type" class="block text-sm font-medium text-gray-700 mb-1">نوع الوحدة</label>
          <select
            id="type"
            bind:value={selectedType}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">جميع الأنواع</option>
            {#each unitTypes as type}
              <option value={type}>{type}</option>
            {/each}
          </select>
        </div>
      </div>
    </div>

    <!-- جدول الوحدات التنظيمية -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      {#if filteredUnits.length === 0}
        <div class="p-6 text-center text-gray-500">
          لا توجد وحدات تنظيمية مطابقة للبحث
        </div>
      {:else}
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوحدة الأم</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {#each filteredUnits as unit}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="font-medium text-gray-900">{unit.name}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      {unit.type}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {#if unit.parent}
                      <div class="text-sm text-gray-900">{unit.parent.name}</div>
                    {:else}
                      <div class="text-sm text-gray-500">-</div>
                    {/if}
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900">{unit.description || '-'}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-left">
                    <a
                      href={`/admin/units/${unit.id}/logos`}
                      class="text-green-600 hover:text-green-900 ml-3"
                    >
                      الشعارات والأختام
                    </a>
                    <button
                      class="text-indigo-600 hover:text-indigo-900 ml-3"
                      on:click={() => openEditForm(unit)}
                    >
                      تعديل
                    </button>
                    <button
                      class="text-red-600 hover:text-red-900"
                      on:click={() => handleDeleteUnit(unit.id)}
                    >
                      حذف
                    </button>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {/if}
    </div>
  {/if}

  <!-- نموذج إضافة وحدة جديدة -->
  {#if showAddForm}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4">إضافة وحدة تنظيمية جديدة</h2>

        <div class="mb-4">
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">اسم الوحدة</label>
          <input
            id="name"
            type="text"
            bind:value={newUnit.name}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>

        <div class="mb-4">
          <label for="type" class="block text-sm font-medium text-gray-700 mb-1">نوع الوحدة</label>
          <select
            id="type"
            bind:value={newUnit.type}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            {#each unitTypes as type}
              <option value={type}>{type}</option>
            {/each}
          </select>
        </div>

        <div class="mb-4">
          <label for="parent" class="block text-sm font-medium text-gray-700 mb-1">الوحدة الأم</label>
          <select
            id="parent"
            bind:value={newUnit.parent_id}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value={null}>بدون وحدة أم</option>
            {#each units as unit}
              <option value={unit.id}>{unit.name} ({unit.type})</option>
            {/each}
          </select>
        </div>

        <div class="mb-4">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
          <textarea
            id="description"
            bind:value={newUnit.description}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            rows="3"
          ></textarea>
        </div>

        <div class="flex justify-end gap-2">
          <button
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            on:click={() => showAddForm = false}
          >
            إلغاء
          </button>
          <button
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            on:click={handleAddUnit}
          >
            إضافة
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- نموذج تعديل وحدة -->
  {#if showEditForm && editingUnit}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4">تعديل وحدة تنظيمية</h2>

        <div class="mb-4">
          <label for="edit-name" class="block text-sm font-medium text-gray-700 mb-1">اسم الوحدة</label>
          <input
            id="edit-name"
            type="text"
            bind:value={editingUnit.name}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>

        <div class="mb-4">
          <label for="edit-type" class="block text-sm font-medium text-gray-700 mb-1">نوع الوحدة</label>
          <select
            id="edit-type"
            bind:value={editingUnit.type}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            {#each unitTypes as type}
              <option value={type}>{type}</option>
            {/each}
          </select>
        </div>

        <div class="mb-4">
          <label for="edit-parent" class="block text-sm font-medium text-gray-700 mb-1">الوحدة الأم</label>
          <select
            id="edit-parent"
            bind:value={editingUnit.parent_id}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value={null}>بدون وحدة أم</option>
            {#each units.filter(u => u.id !== editingUnit.id) as unit}
              <option value={unit.id}>{unit.name} ({unit.type})</option>
            {/each}
          </select>
        </div>

        <div class="mb-4">
          <label for="edit-description" class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
          <textarea
            id="edit-description"
            bind:value={editingUnit.description}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            rows="3"
          ></textarea>
        </div>

        <div class="flex justify-end gap-2">
          <button
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            on:click={() => showEditForm = false}
          >
            إلغاء
          </button>
          <button
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            on:click={handleEditUnit}
          >
            حفظ التغييرات
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .rtl {
    direction: rtl;
    text-align: right;
  }
</style>
