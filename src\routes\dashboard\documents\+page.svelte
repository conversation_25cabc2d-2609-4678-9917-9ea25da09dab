<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';
  import { DocumentService, type Document } from '$lib/services/documentService';
  import { UnitService, type Unit } from '$lib/services/unitService';

  let documents: Document[] = [];
  let filteredDocuments: Document[] = [];
  let userUnit: Unit | null = null;
  let currentUserId = '';
  let searchTerm = '';
  let documentTypeFilter = '';
  let dialogOpen = false;
  let loading = true;
  let error = '';
  let userRole = '';

  let newDocument = {
    title: '',
    document_number: '',
    document_date: new Date().toISOString().split('T')[0],
    document_type: '',
    sender: '',
    receiver: '',
    notes: ''
  };

  const documentTypes = [
    { value: 'صادر', label: 'صادر' },
    { value: 'وارد', label: 'وارد' },
    { value: 'مذكرة داخلية', label: 'مذكرة داخلية' },
    { value: 'تعميم', label: 'تعميم' }
  ];

  onMount(async () => {
    try {
      loading = true;

      // الحصول على المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        currentUserId = user.id;

        // الحصول على وحدة المستخدم ودوره
        const { data: profile } = await supabase
          .from('profiles')
          .select('unit_id, role_id')
          .eq('id', user.id)
          .single();

        if (profile && profile.role_id) {
          // التحقق من دور المستخدم
          const { data: role } = await supabase
            .from('roles')
            .select('name')
            .eq('id', profile.role_id)
            .single();

          if (role) {
            userRole = role.name;
          }

          // إذا كان المستخدم ينتمي لوحدة تنظيمية
          if (profile.unit_id) {
            userUnit = await UnitService.getUnitById(profile.unit_id);
          }
        }
      }

      // جلب المستندات
      await fetchDocuments();
    } catch (err) {
      console.error('Error initializing page:', err);
      error = 'حدث خطأ أثناء تحميل البيانات';
    } finally {
      loading = false;
    }
  });

  async function fetchDocuments() {
    try {
      console.log('Starting fetchDocuments...');
      loading = true;
      error = '';

      let fetchedDocuments: Document[] = [];

      // التحقق من دور المستخدم
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        error = 'يجب تسجيل الدخول لعرض المستندات';
        return;
      }

      console.log('Current user:', user.id);

      try {
        // محاولة استخدام الاستعلام المباشر أولاً
        console.log('Trying direct query...');
        const { data: directData, error: directError } = await supabase
          .from('documents')
          .select('*')
          .limit(20);

        if (directError) {
          console.error('Direct query error:', directError);
          throw directError;
        }

        if (directData && directData.length > 0) {
          console.log('Direct query successful, got', directData.length, 'documents');
          // تحويل البيانات إلى النوع المطلوب
          fetchedDocuments = directData.map(doc => ({
            ...doc,
            unit: doc.unit_id ? { id: doc.unit_id, name: 'وحدة' } : null
          }));

          documents = fetchedDocuments;
          applyFilters();
          return;
        }
      } catch (directErr) {
        console.error('Exception in direct query:', directErr);
        // استمر إلى الطريقة الأصلية
      }

      // الحصول على دور المستخدم ووحدته
      const { data: profile } = await supabase
        .from('profiles')
        .select('role_id, unit_id')
        .eq('id', user.id)
        .single();

      if (!profile) {
        error = 'لم يتم العثور على بيانات المستخدم';
        return;
      }

      // التحقق من دور المستخدم
      let isAdmin = false;
      let isManager = false;

      if (profile.role_id) {
        const { data: role } = await supabase
          .from('roles')
          .select('name')
          .eq('id', profile.role_id)
          .single();

        isAdmin = !!role && (role.name === 'admin' || role.name === 'مشرف');
        isManager = !!role && (role.name === 'manager' || role.name === 'مدير');
      }

      // التحقق من وحدة المستخدم
      if (!profile.unit_id && !isAdmin) {
        error = 'لا يمكنك عرض المستندات لأنك لا تنتمي لأي وحدة تنظيمية';
        return;
      }

      // تحديد المستندات التي يمكن للمستخدم الوصول إليها
      if (isAdmin) {
        // المشرف يمكنه الوصول إلى جميع المستندات
        fetchedDocuments = await DocumentService.getAllDocuments();
      } else if (isManager && profile.unit_id) {
        // المدير يمكنه الوصول إلى مستندات وحدته والوحدات التابعة لها
        fetchedDocuments = await DocumentService.getDocumentsByUnitAndSubordinates(profile.unit_id);
      } else if (profile.unit_id) {
        // المستخدم العادي يمكنه الوصول إلى مستندات وحدته فقط
        fetchedDocuments = await DocumentService.getDocumentsByUnitId(profile.unit_id);
      }

      console.log('Got documents using service:', fetchedDocuments.length);
      documents = fetchedDocuments;
      applyFilters();
    } catch (err: any) {
      console.error('Error fetching documents:', err);
      error = 'حدث خطأ أثناء جلب المستندات: ' + (err.message || 'خطأ غير معروف');
    } finally {
      loading = false;
      console.log('fetchDocuments completed. documents:', documents.length);
    }
  }

  function applyFilters() {
    filteredDocuments = documents.filter(doc => {
      const matchesSearch = searchTerm === '' ||
        doc.title.includes(searchTerm) ||
        doc.document_number?.includes(searchTerm) ||
        (doc.sender && doc.sender.includes(searchTerm)) ||
        (doc.receiver && doc.receiver.includes(searchTerm)) ||
        (doc.unit?.name && doc.unit.name.includes(searchTerm));

      const matchesType = documentTypeFilter === '' || doc.document_type === documentTypeFilter;

      return matchesSearch && matchesType;
    });
  }

  function handleSearch() {
    applyFilters();
  }

  function handleFilterChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    documentTypeFilter = target.value;
    applyFilters();
  }



  async function handleAddDocument() {
    if (!newDocument.title || !newDocument.document_type) {
      alert('يرجى إدخال عنوان ونوع المستند على الأقل');
      return;
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        alert('يجب تسجيل الدخول لإضافة مستند');
        return;
      }

      // الحصول على وحدة المستخدم ودوره
      const { data: profile } = await supabase
        .from('profiles')
        .select('unit_id, role_id')
        .eq('id', user.id)
        .single();

      if (!profile || !profile.unit_id) {
        alert('لا يمكنك إضافة مستند لأنك لا تنتمي لأي وحدة تنظيمية');
        return;
      }

      // استخدام وحدة المستخدم تلقائياً للمستند
      const documentUnitId = profile.unit_id;

      // إضافة المستند باستخدام خدمة المستندات
      const document = await DocumentService.addDocument({
        title: newDocument.title,
        document_number: newDocument.document_number,
        document_date: newDocument.document_date,
        document_type: newDocument.document_type,
        sender: newDocument.sender,
        receiver: newDocument.receiver,
        notes: newDocument.notes,
        created_by: user.id,
        unit_id: documentUnitId
      });

      if (!document) {
        alert('حدث خطأ أثناء إضافة المستند');
        return;
      }

      // إعادة تحميل المستندات
      await fetchDocuments();

      // إعادة تعيين النموذج
      newDocument = {
        title: '',
        document_number: '',
        document_date: new Date().toISOString().split('T')[0],
        document_type: '',
        sender: '',
        receiver: '',
        notes: ''
      };

      // إغلاق النافذة
      dialogOpen = false;
    } catch (error) {
      console.error('Error adding document:', error);
      alert('حدث خطأ أثناء إضافة المستند');
    }
  }

  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  }
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold">المستندات</h1>
      {#if userUnit}
        <p class="text-sm text-muted-foreground mt-1">
          {#if userRole === 'admin' || userRole === 'مشرف'}
            أنت مشرف ويمكنك الوصول إلى جميع المستندات
          {:else if userRole === 'manager' || userRole === 'مدير'}
            أنت مدير {userUnit.name} ويمكنك الوصول إلى مستندات وحدتك والوحدات التابعة لها
          {:else}
            أنت تعرض مستندات {userUnit.name}
          {/if}
        </p>
      {/if}
    </div>

    <div class="flex gap-2">
      <a
        href="/dashboard/documents/word-template"
        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-500 text-white hover:bg-blue-600 h-10 px-4 py-2"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><path d="M16 13H8"/><path d="M16 17H8"/><path d="M10 9H8"/></svg>
        محرر مستندات Word
      </a>
      <button
        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
        on:click={() => dialogOpen = true}
      >
        إضافة مستند جديد
      </button>
    </div>
  </div>

  {#if error}
    <div class="bg-destructive/10 border border-red-200/30 p-4 rounded-md text-destructive text-sm mb-6">
      <p>{error}</p>
    </div>
  {/if}

  {#if loading}
    <div class="flex items-center justify-center h-64">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary dark:border-blue-400"></div>
    </div>
  {:else}
    <!-- Search and Filter -->
    <div class="bg-card rounded-lg shadow p-6 mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
          <input
            placeholder="ابحث عن مستند..."
            bind:value={searchTerm}
            on:input={handleSearch}
            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>

        <div class="w-full md:w-64">
          <select
            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            on:change={handleFilterChange}
          >
            <option value="">جميع الأنواع</option>
            {#each documentTypes as type}
              <option value={type.value}>{type.label}</option>
            {/each}
          </select>
        </div>

        <!-- تم إزالة فلتر الوحدات التنظيمية لأن المستخدم يرى فقط مستندات وحدته -->
      </div>
    </div>
  {/if}

  <!-- Documents Table -->
  <div class="bg-card rounded-lg shadow overflow-hidden">
    <div class="p-6">
      <h2 class="text-xl font-bold">قائمة المستندات</h2>
      <p class="text-muted-foreground">عرض جميع المستندات في النظام</p>
    </div>

    {#if filteredDocuments.length === 0}
      <div class="text-center text-muted-foreground py-8">
        <p>لا توجد مستندات متطابقة مع البحث</p>
      </div>
    {:else}
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-muted">
            <tr>
              <th class="text-right p-3 font-medium">العنوان</th>
              <th class="text-right p-3 font-medium">الرقم</th>
              <th class="text-right p-3 font-medium">النوع</th>
              <th class="text-right p-3 font-medium">التاريخ</th>
              <th class="text-right p-3 font-medium">المرسل</th>
              <th class="text-right p-3 font-medium">المستقبل</th>
              <th class="text-right p-3 font-medium">الوحدة</th>
            </tr>
          </thead>
          <tbody>
            {#each filteredDocuments as document}
              <tr class="border-t hover:bg-muted/50">
                <td class="p-3 font-medium">
                  <a href="/dashboard/documents/{document.id}" class="hover:underline text-primary">
                    {document.title}
                  </a>
                </td>
                <td class="p-3">{document.document_number || '-'}</td>
                <td class="p-3">{document.document_type}</td>
                <td class="p-3">{formatDate(document.document_date)}</td>
                <td class="p-3">{document.sender || '-'}</td>
                <td class="p-3">{document.receiver || '-'}</td>
                <td class="p-3">{document.unit?.name || '-'}</td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>

  <!-- Add Document Dialog -->
  {#if dialogOpen}
    <div class="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="bg-card rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-auto">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">إضافة مستند جديد</h2>
            <button
              class="text-muted-foreground hover:text-foreground"
              on:click={() => dialogOpen = false}
            >
              ✕
            </button>
          </div>

          <div class="grid gap-4 py-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label for="title" class="block text-sm font-medium">عنوان المستند</label>
                <input
                  id="title"
                  bind:value={newDocument.title}
                  placeholder="أدخل عنوان المستند"
                  class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div class="space-y-2">
                <label for="document_number" class="block text-sm font-medium">رقم المستند</label>
                <input
                  id="document_number"
                  bind:value={newDocument.document_number}
                  placeholder="أدخل رقم المستند"
                  class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label for="document_date" class="block text-sm font-medium">تاريخ المستند</label>
                <input
                  id="document_date"
                  type="date"
                  bind:value={newDocument.document_date}
                  class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div class="space-y-2">
                <label for="document_type" class="block text-sm font-medium">نوع المستند</label>
                <select
                  id="document_type"
                  bind:value={newDocument.document_type}
                  class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="" disabled selected>اختر نوع المستند</option>
                  {#each documentTypes as type}
                    <option value={type.value}>{type.label}</option>
                  {/each}
                </select>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label for="sender" class="block text-sm font-medium">الجهة المرسلة</label>
                <input
                  id="sender"
                  bind:value={newDocument.sender}
                  placeholder="أدخل الجهة المرسلة"
                  class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>

              <div class="space-y-2">
                <label for="receiver" class="block text-sm font-medium">الجهة المستقبلة</label>
                <input
                  id="receiver"
                  bind:value={newDocument.receiver}
                  placeholder="أدخل الجهة المستقبلة"
                  class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>

            <div class="space-y-2">
              <label for="notes" class="block text-sm font-medium">ملاحظات</label>
              <textarea
                id="notes"
                bind:value={newDocument.notes}
                placeholder="أدخل ملاحظات (اختياري)"
                class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                rows="3"
              ></textarea>
            </div>
          </div>

          <div class="flex justify-end gap-2 mt-4">
            <button
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
              on:click={() => dialogOpen = false}
            >
              إلغاء
            </button>
            <button
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
              on:click={handleAddDocument}
            >
              إضافة
            </button>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>
