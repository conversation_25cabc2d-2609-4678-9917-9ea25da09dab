-- تعديل جدول profiles لإضافة عمود role_id وربطه بجدول roles

-- 1. التحقق من وجود عمود role_id
DO $$
DECLARE
  role_id_exists BOOLEAN;
BEGIN
  -- التحقق من وجود العمود role_id
  SELECT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'role_id'
  ) INTO role_id_exists;
  
  -- إذا كان العمود غير موجود، قم بإضافته
  IF NOT role_id_exists THEN
    ALTER TABLE profiles ADD COLUMN role_id UUID REFERENCES roles(id);
  END IF;
END $$;

-- 2. تحديث قيم role_id بناءً على قيم role الحالية
DO $$
DECLARE
  admin_role_id UUID;
  manager_role_id UUID;
  user_role_id UUID;
BEGIN
  -- الحصول على معرفات الأدوار
  SELECT id INTO admin_role_id FROM roles WHERE name = 'admin';
  SELECT id INTO manager_role_id FROM roles WHERE name = 'manager';
  SELECT id INTO user_role_id FROM roles WHERE name = 'user';
  
  -- تحديث المستخدمين الذين لديهم دور admin
  UPDATE profiles SET role_id = admin_role_id WHERE role = 'admin';
  
  -- تحديث المستخدمين الذين لديهم دور manager
  UPDATE profiles SET role_id = manager_role_id WHERE role = 'manager';
  
  -- تحديث المستخدمين الذين لديهم دور user أو أي قيمة أخرى
  UPDATE profiles SET role_id = user_role_id WHERE role = 'user' OR role_id IS NULL;
END $$;

-- 3. إنشاء دالة لتحديث role_id عند تحديث role
CREATE OR REPLACE FUNCTION update_role_id()
RETURNS TRIGGER AS $$
DECLARE
  role_id_val UUID;
BEGIN
  -- الحصول على معرف الدور المناسب
  SELECT id INTO role_id_val FROM roles WHERE name = NEW.role;
  
  -- تحديث role_id
  IF role_id_val IS NOT NULL THEN
    NEW.role_id := role_id_val;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. إنشاء trigger لتحديث role_id تلقائيًا عند تحديث role
DO $$
BEGIN
  -- التحقق من وجود الـ trigger
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'update_role_id_trigger'
  ) THEN
    CREATE TRIGGER update_role_id_trigger
    BEFORE INSERT OR UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_role_id();
  END IF;
END $$;

-- 5. إنشاء دالة لتحديث role عند تحديث role_id
CREATE OR REPLACE FUNCTION update_role()
RETURNS TRIGGER AS $$
DECLARE
  role_name_val TEXT;
BEGIN
  -- الحصول على اسم الدور المناسب
  SELECT name INTO role_name_val FROM roles WHERE id = NEW.role_id;
  
  -- تحديث role
  IF role_name_val IS NOT NULL THEN
    NEW.role := role_name_val;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. إنشاء trigger لتحديث role تلقائيًا عند تحديث role_id
DO $$
BEGIN
  -- التحقق من وجود الـ trigger
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'update_role_trigger'
  ) THEN
    CREATE TRIGGER update_role_trigger
    BEFORE UPDATE OF role_id ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_role();
  END IF;
END $$;
