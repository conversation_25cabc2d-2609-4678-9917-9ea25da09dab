-- إنشاء وظيفة للتحقق من وجود عمود في جدول

-- وظيفة للتحقق من وجود عمود في جدول
CREATE OR REPLACE FUNCTION check_column_exists(table_name text, column_name text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    column_exists boolean;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = check_column_exists.table_name
        AND column_name = check_column_exists.column_name
    ) INTO column_exists;
    
    RETURN column_exists;
END;
$$;

-- إضافة تعليق للوظيفة
COMMENT ON FUNCTION check_column_exists(text, text) IS 'التحقق من وجود عمود في جدول';

-- وظيفة لإضافة عمود parent_id إذا لم يكن موجوداً
CREATE OR REPLACE FUNCTION add_parent_id_column_if_not_exists()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- التحقق من وجود العمود
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'messages'
        AND column_name = 'parent_id'
    ) THEN
        -- إضافة العمود إذا لم يكن موجوداً
        ALTER TABLE messages ADD COLUMN parent_id UUID REFERENCES messages(id) NULL;
        
        -- إضافة فهرس للعمود
        CREATE INDEX IF NOT EXISTS idx_messages_parent_id ON messages(parent_id);
        
        -- إضافة تعليق للعمود
        COMMENT ON COLUMN messages.parent_id IS 'معرف الرسالة الأصلية التي تم الرد عليها';
    END IF;
END;
$$;

-- إضافة تعليق للوظيفة
COMMENT ON FUNCTION add_parent_id_column_if_not_exists() IS 'إضافة عمود parent_id إلى جدول messages إذا لم يكن موجوداً';

-- منح صلاحيات للوظائف
GRANT EXECUTE ON FUNCTION check_column_exists(text, text) TO authenticated;
GRANT EXECUTE ON FUNCTION add_parent_id_column_if_not_exists() TO authenticated;

-- تحديث ذاكرة التخزين المؤقت للمخطط
NOTIFY pgrst, 'reload schema';
