-- تنظيف النظام القديم للمستندات الموقعة
-- هذا الملف يحذف الجداول والوظائف التي لم تعد مطلوبة بعد التحديث للنظام الجديد

-- ملاحظة: تأكد من عمل نسخة احتياطية قبل تشغيل هذا الملف

-- 1. حذف الوظائف المتعلقة بالنظام القديم (اختياري - يمكن الاحتفاظ بها للاستخدام المستقبلي)
-- DROP FUNCTION IF EXISTS public.get_user_signed_documents(UUID, TEXT);
-- DROP FUNCTION IF EXISTS public.get_users_with_signature_permission();

-- 2. حذف المحفزات (Triggers) المتعلقة بالنظام القديم
DROP TRIGGER IF EXISTS update_signed_document_history ON public.signed_documents;
DROP TRIGGER IF EXISTS generate_signed_document_reference ON public.signed_documents;

-- 3. حذف الوظائف المساعدة للمحفزات
DROP FUNCTION IF EXISTS public.update_signed_document_history();
DROP FUNCTION IF EXISTS public.generate_signed_document_reference();

-- 4. تعليق: الجداول الأساسية سيتم الاحتفاظ بها لأن النظام الجديد يستخدمها
-- لكن مع تحديث الوظائف والمحفزات لتتناسب مع النظام الجديد

-- 5. إنشاء وظيفة محدثة لتحديث تاريخ المستندات الموقعة (للنظام الجديد)
CREATE OR REPLACE FUNCTION public.update_signed_document_history_new()
RETURNS TRIGGER AS $$
BEGIN
  -- إدراج سجل في تاريخ المستندات الموقعة للنظام الجديد
  INSERT INTO public.signed_document_history (
    signed_document_id,
    user_id,
    action,
    status,
    details
  ) VALUES (
    NEW.id,
    -- تحديد المستخدم بناءً على نوع العملية والحالة
    CASE
      WHEN TG_OP = 'INSERT' THEN NEW.creator_id
      WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        CASE
          WHEN NEW.status = 'signed' THEN NEW.signer_id
          WHEN NEW.status = 'rejected' THEN NEW.signer_id
          WHEN NEW.status = 'revision_requested' THEN NEW.signer_id
          ELSE COALESCE(auth.uid(), NEW.creator_id)
        END
      ELSE COALESCE(auth.uid(), NEW.creator_id)
    END,
    CASE
      WHEN TG_OP = 'INSERT' THEN 'create_signature_request'
      WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        CASE
          WHEN NEW.status = 'pending_signature' THEN 'request_signature'
          WHEN NEW.status = 'signed' THEN 'sign_document'
          WHEN NEW.status = 'rejected' THEN 'reject_document'
          WHEN NEW.status = 'revision_requested' THEN 'request_revision'
          ELSE 'update_status'
        END
      ELSE 'update'
    END,
    NEW.status::TEXT,
    jsonb_build_object(
      'old_status', CASE WHEN TG_OP = 'UPDATE' THEN OLD.status ELSE NULL END,
      'new_status', NEW.status,
      'signature_method', 'electronic_signature_in_content',
      'system_version', 'new_integrated_system',
      'timestamp', NOW()
    )
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. إنشاء محفز جديد للنظام المحدث
CREATE TRIGGER update_signed_document_history_new
AFTER INSERT OR UPDATE ON public.signed_documents
FOR EACH ROW
EXECUTE FUNCTION public.update_signed_document_history_new();

-- 7. إنشاء وظيفة محدثة لتوليد الرقم الإشاري (للنظام الجديد)
CREATE OR REPLACE FUNCTION public.generate_signed_document_reference_new()
RETURNS TRIGGER AS $$
BEGIN
  -- إذا كان المستند موقعاً ولا يوجد رقم إشاري، قم بإنشاء واحد
  IF NEW.status = 'signed' AND (NEW.reference_number IS NULL OR NEW.reference_number = '') THEN
    -- استخدام معرف التوقيع من النظام الجديد
    IF NEW.signature IS NOT NULL AND NEW.signature ? 'signatureId' THEN
      NEW.reference_number := public.generate_reference_number(NEW.signature->>'signatureId');
    ELSE
      -- إنشاء رقم إشاري بديل إذا لم يكن هناك معرف توقيع
      NEW.reference_number := public.generate_reference_number(NEW.id::TEXT);
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. إنشاء محفز جديد لتوليد الرقم الإشاري
CREATE TRIGGER generate_signed_document_reference_new
BEFORE UPDATE ON public.signed_documents
FOR EACH ROW
WHEN (NEW.status = 'signed' AND (NEW.reference_number IS NULL OR NEW.reference_number = ''))
EXECUTE FUNCTION public.generate_signed_document_reference_new();

-- 9. تحديث تعليقات الجداول لتوضيح النظام الجديد
COMMENT ON TABLE public.signed_documents IS 'جدول المستندات الموقعة - النظام المحدث مع التوقيع الإلكتروني المدمج في المحتوى';
COMMENT ON COLUMN public.signed_documents.signature IS 'بيانات التوقيع الإلكتروني المدمج في محتوى المستند';

-- 10. إضافة فهرس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_signed_documents_status_creator ON public.signed_documents(status, creator_id);
CREATE INDEX IF NOT EXISTS idx_signed_documents_status_signer ON public.signed_documents(status, signer_id);
CREATE INDEX IF NOT EXISTS idx_signed_documents_reference_number ON public.signed_documents(reference_number) WHERE reference_number IS NOT NULL;

-- 11. تحديث ذاكرة التخزين المؤقت
NOTIFY pgrst, 'reload schema';
