# إصلاح مشكلة الرد على الرسائل

تم إصلاح مشكلة "حدث خطأ أثناء إرسال الرد: Could not find the 'parent_id' column of 'messages' in the schema cache" التي تظهر عند محاولة الرد على رسالة.

## المشكلة

كان المستخدمون يواجهون خطأ عند محاولة الرد على رسالة، حيث تظهر رسالة "حدث خطأ أثناء إرسال الرد: Could not find the 'parent_id' column of 'messages' in the schema cache" بسبب عدم وجود عمود `parent_id` في جدول `messages`.

## الحل

تم اتخاذ نهج شامل لحل المشكلة من خلال:

1. إنشاء ملف SQL لإضافة عمود `parent_id` إلى جدول `messages`
2. إنشاء وظائف SQL للتحقق من وجود العمود وإضافته
3. تعديل وظيفة `handleSendReply` للتعامل مع حالة عدم وجود العمود

## التغييرات التي تم تنفيذها:

### 1. إنشاء ملف SQL لإضافة عمود `parent_id`

تم إنشاء ملف `src\lib\db\add_parent_id_column.sql` يقوم بإضافة عمود `parent_id` إلى جدول `messages` إذا لم يكن موجوداً.

### 2. إنشاء وظائف SQL للتحقق من وجود العمود وإضافته

تم إنشاء ملف `src\lib\db\create_column_check_functions.sql` يحتوي على وظيفتين:

- `check_column_exists`: للتحقق من وجود عمود في جدول
- `add_parent_id_column_if_not_exists`: لإضافة عمود `parent_id` إلى جدول `messages` إذا لم يكن موجوداً

### 3. تعديل وظيفة `handleSendReply`

تم تعديل وظيفة `handleSendReply` في ملف `src\routes\dashboard\messages\view\[id]\+page.svelte` للتعامل مع حالة عدم وجود العمود:

- إضافة محاولة لإضافة العمود إذا لم يكن موجوداً
- التحقق من وجود العمود قبل إضافة `parent_id` إلى بيانات الرد
- استخدام نوع `any` لمتغير `replyData` للتعامل مع الخصائص الديناميكية

## كيفية تطبيق الحل:

1. قم بتنفيذ ملف `src\lib\db\create_column_check_functions.sql` في قاعدة البيانات Supabase باستخدام SQL Editor
2. قم بتحديث التطبيق بالملفات الجديدة

## كيفية اختبار الحل:

1. انتقل إلى صفحة المراسلات الداخلية
2. افتح إحدى الرسائل
3. انقر على زر "الرد على الرسالة"
4. اكتب رداً واضغط على "إرسال الرد"
5. يجب أن يتم إرسال الرد بنجاح دون ظهور أي خطأ

## ملاحظات هامة:

1. إذا كان عمود `parent_id` غير موجود في قاعدة البيانات، سيتم إضافته تلقائياً عند محاولة الرد على رسالة
2. إذا فشلت إضافة العمود، سيتم تجاهل الخطأ والمتابعة بإرسال الرد بدون `parent_id`
3. يمكن تنفيذ ملف `src\lib\db\add_parent_id_column.sql` يدوياً لإضافة العمود مباشرة إلى قاعدة البيانات
