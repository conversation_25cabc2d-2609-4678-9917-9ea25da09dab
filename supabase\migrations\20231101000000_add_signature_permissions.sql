-- إنشاء جدول صلاحيات التوقيع
CREATE TABLE IF NOT EXISTS signature_permissions (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  can_sign BO<PERSON><PERSON><PERSON> DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  UNIQUE(user_id)
);

-- إضافة فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_signature_permissions_user_id ON signature_permissions(user_id);

-- إنشاء وظيفة للتحقق من صلاحية المستخدم للتوقيع
CREATE OR REPLACE FUNCTION check_user_can_sign(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_can_sign BOOLEAN;
  v_is_admin BOOLEAN;
BEGIN
  -- التحقق مما إذا كان المستخدم مشرفاً
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_user_id AND (role = 'admin' OR role = 'مشرف')
  ) INTO v_is_admin;
  
  -- إذا كان المستخدم مشرفاً، فلديه صلاحية التوقيع تلقائياً
  IF v_is_admin THEN
    RETURN TRUE;
  END IF;
  
  -- التحقق من صلاحية التوقيع في جدول الصلاحيات
  SELECT can_sign INTO v_can_sign
  FROM signature_permissions
  WHERE user_id = p_user_id;
  
  -- إرجاع النتيجة (false إذا لم يكن هناك سجل)
  RETURN COALESCE(v_can_sign, FALSE);
END;
$$;

-- إنشاء وظيفة لمنح صلاحية التوقيع
CREATE OR REPLACE FUNCTION grant_signature_permission(
  p_admin_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_admin BOOLEAN;
BEGIN
  -- التحقق من صلاحية المستخدم المشرف
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_admin_id AND (role = 'admin' OR role = 'مشرف')
  ) INTO v_is_admin;
  
  -- إذا لم يكن المستخدم مشرفاً، فلا يمكنه منح الصلاحية
  IF NOT v_is_admin THEN
    RETURN FALSE;
  END IF;
  
  -- إضافة أو تحديث صلاحية التوقيع
  INSERT INTO signature_permissions (user_id, can_sign, created_by)
  VALUES (p_user_id, TRUE, p_admin_id)
  ON CONFLICT (user_id)
  DO UPDATE SET
    can_sign = TRUE,
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$;

-- إنشاء وظيفة لإلغاء صلاحية التوقيع
CREATE OR REPLACE FUNCTION revoke_signature_permission(
  p_admin_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_admin BOOLEAN;
BEGIN
  -- التحقق من صلاحية المستخدم المشرف
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_admin_id AND (role = 'admin' OR role = 'مشرف')
  ) INTO v_is_admin;
  
  -- إذا لم يكن المستخدم مشرفاً، فلا يمكنه إلغاء الصلاحية
  IF NOT v_is_admin THEN
    RETURN FALSE;
  END IF;
  
  -- إضافة أو تحديث صلاحية التوقيع
  INSERT INTO signature_permissions (user_id, can_sign, created_by)
  VALUES (p_user_id, FALSE, p_admin_id)
  ON CONFLICT (user_id)
  DO UPDATE SET
    can_sign = FALSE,
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$;

-- إنشاء سياسة أمان للجدول
ALTER TABLE signature_permissions ENABLE ROW LEVEL SECURITY;

-- سياسة للمشرفين: يمكنهم رؤية وتعديل جميع السجلات
CREATE POLICY admin_all_signature_permissions ON signature_permissions
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid() AND (profiles.role = 'admin' OR profiles.role = 'مشرف')
    )
  );

-- سياسة للمستخدمين: يمكنهم رؤية سجلاتهم فقط
CREATE POLICY user_read_own_signature_permissions ON signature_permissions
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());
