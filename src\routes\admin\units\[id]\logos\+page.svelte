<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib/supabase';
  
  // متغيرات الصفحة
  let unitId = $page.params.id;
  let unitName = '';
  let unitLogo: string | null = null;
  let unitStamp: string | null = null;
  let rightLogo: string | null = null;
  let leftLogo: string | null = null;
  
  let unitLogoUrl = '';
  let unitStampUrl = '';
  let rightLogoUrl = '';
  let leftLogoUrl = '';
  
  let isLoading = true;
  let isSaving = false;
  let error: string | null = null;
  let success: string | null = null;
  
  let uploadingUnitLogo = false;
  let uploadingUnitStamp = false;
  let uploadingRightLogo = false;
  let uploadingLeftLogo = false;
  
  let currentUser: any = null;
  let isAdmin = false;
  
  // جلب بيانات الوحدة
  async function fetchUnitData() {
    try {
      isLoading = true;
      error = null;
      
      // جلب بيانات المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();
      currentUser = user;
      
      if (!user) {
        error = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة';
        return;
      }
      
      // التحقق من صلاحيات المستخدم
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
        
      if (userError) throw userError;
      
      // التحقق من أن المستخدم مدير
      isAdmin = userData?.role === 'admin';
      
      if (!isAdmin) {
        error = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
        return;
      }
      
      // جلب بيانات الوحدة
      const { data: unitData, error: unitError } = await supabase
        .from('units')
        .select('*')
        .eq('id', unitId)
        .single();
        
      if (unitError) throw unitError;
      
      if (unitData) {
        unitName = unitData.name || '';
        unitLogo = unitData.logo || null;
        unitStamp = unitData.stamp || null;
        rightLogo = unitData.right_logo || null;
        leftLogo = unitData.left_logo || null;
        
        // جلب روابط الصور
        await loadImages();
      }
    } catch (err: any) {
      console.error('Error fetching unit data:', err);
      error = 'حدث خطأ أثناء جلب بيانات الوحدة: ' + (err.message || err);
    } finally {
      isLoading = false;
    }
  }
  
  // تحميل الصور
  async function loadImages() {
    try {
      // تحميل شعار الوحدة
      if (unitLogo) {
        const { data: logoData, error: logoError } = await supabase
          .storage
          .from('logos')
          .download(unitLogo);
          
        if (!logoError && logoData) {
          unitLogoUrl = URL.createObjectURL(logoData);
        }
      }
      
      // تحميل ختم الوحدة
      if (unitStamp) {
        const { data: stampData, error: stampError } = await supabase
          .storage
          .from('stamps')
          .download(unitStamp);
          
        if (!stampError && stampData) {
          unitStampUrl = URL.createObjectURL(stampData);
        }
      }
      
      // تحميل الشعار الأيمن
      if (rightLogo) {
        const { data: rightLogoData, error: rightLogoError } = await supabase
          .storage
          .from('logos')
          .download(rightLogo);
          
        if (!rightLogoError && rightLogoData) {
          rightLogoUrl = URL.createObjectURL(rightLogoData);
        }
      }
      
      // تحميل الشعار الأيسر
      if (leftLogo) {
        const { data: leftLogoData, error: leftLogoError } = await supabase
          .storage
          .from('logos')
          .download(leftLogo);
          
        if (!leftLogoError && leftLogoData) {
          leftLogoUrl = URL.createObjectURL(leftLogoData);
        }
      }
    } catch (err) {
      console.error('Error loading images:', err);
    }
  }
  
  // رفع شعار الوحدة
  async function uploadUnitLogo(event: any) {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      uploadingUnitLogo = true;
      error = null;
      
      // إنشاء اسم فريد للملف
      const fileExt = file.name.split('.').pop();
      const fileName = `unit_logo_${unitId}_${Date.now()}.${fileExt}`;
      
      // رفع الملف
      const { data, error: uploadError } = await supabase
        .storage
        .from('logos')
        .upload(fileName, file, { upsert: true });
        
      if (uploadError) throw uploadError;
      
      // تحديث مسار الشعار
      unitLogo = fileName;
      
      // تحديث عنوان URL للعرض
      unitLogoUrl = URL.createObjectURL(file);
      
      success = 'تم رفع شعار الوحدة بنجاح';
    } catch (err: any) {
      console.error('Error uploading unit logo:', err);
      error = 'حدث خطأ أثناء رفع شعار الوحدة: ' + (err.message || err);
    } finally {
      uploadingUnitLogo = false;
    }
  }
  
  // رفع ختم الوحدة
  async function uploadUnitStamp(event: any) {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      uploadingUnitStamp = true;
      error = null;
      
      // إنشاء اسم فريد للملف
      const fileExt = file.name.split('.').pop();
      const fileName = `unit_stamp_${unitId}_${Date.now()}.${fileExt}`;
      
      // رفع الملف
      const { data, error: uploadError } = await supabase
        .storage
        .from('stamps')
        .upload(fileName, file, { upsert: true });
        
      if (uploadError) throw uploadError;
      
      // تحديث مسار الختم
      unitStamp = fileName;
      
      // تحديث عنوان URL للعرض
      unitStampUrl = URL.createObjectURL(file);
      
      success = 'تم رفع ختم الوحدة بنجاح';
    } catch (err: any) {
      console.error('Error uploading unit stamp:', err);
      error = 'حدث خطأ أثناء رفع ختم الوحدة: ' + (err.message || err);
    } finally {
      uploadingUnitStamp = false;
    }
  }
  
  // رفع الشعار الأيمن
  async function uploadRightLogo(event: any) {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      uploadingRightLogo = true;
      error = null;
      
      // إنشاء اسم فريد للملف
      const fileExt = file.name.split('.').pop();
      const fileName = `unit_right_logo_${unitId}_${Date.now()}.${fileExt}`;
      
      // رفع الملف
      const { data, error: uploadError } = await supabase
        .storage
        .from('logos')
        .upload(fileName, file, { upsert: true });
        
      if (uploadError) throw uploadError;
      
      // تحديث مسار الشعار
      rightLogo = fileName;
      
      // تحديث عنوان URL للعرض
      rightLogoUrl = URL.createObjectURL(file);
      
      success = 'تم رفع الشعار الأيمن بنجاح';
    } catch (err: any) {
      console.error('Error uploading right logo:', err);
      error = 'حدث خطأ أثناء رفع الشعار الأيمن: ' + (err.message || err);
    } finally {
      uploadingRightLogo = false;
    }
  }
  
  // رفع الشعار الأيسر
  async function uploadLeftLogo(event: any) {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      uploadingLeftLogo = true;
      error = null;
      
      // إنشاء اسم فريد للملف
      const fileExt = file.name.split('.').pop();
      const fileName = `unit_left_logo_${unitId}_${Date.now()}.${fileExt}`;
      
      // رفع الملف
      const { data, error: uploadError } = await supabase
        .storage
        .from('logos')
        .upload(fileName, file, { upsert: true });
        
      if (uploadError) throw uploadError;
      
      // تحديث مسار الشعار
      leftLogo = fileName;
      
      // تحديث عنوان URL للعرض
      leftLogoUrl = URL.createObjectURL(file);
      
      success = 'تم رفع الشعار الأيسر بنجاح';
    } catch (err: any) {
      console.error('Error uploading left logo:', err);
      error = 'حدث خطأ أثناء رفع الشعار الأيسر: ' + (err.message || err);
    } finally {
      uploadingLeftLogo = false;
    }
  }
  
  // حفظ بيانات الوحدة
  async function saveUnitData() {
    try {
      isSaving = true;
      error = null;
      success = null;
      
      // التحقق من صلاحيات المستخدم
      if (!isAdmin) {
        error = 'ليس لديك صلاحية لحفظ بيانات الوحدة';
        return;
      }
      
      // إعداد بيانات الوحدة
      const unitData = {
        logo: unitLogo,
        stamp: unitStamp,
        right_logo: rightLogo,
        left_logo: leftLogo,
        updated_at: new Date().toISOString()
      };
      
      // حفظ البيانات
      const { data, error: saveError } = await supabase
        .from('units')
        .update(unitData)
        .eq('id', unitId)
        .select();
        
      if (saveError) throw saveError;
      
      success = 'تم حفظ بيانات الوحدة بنجاح';
    } catch (err: any) {
      console.error('Error saving unit data:', err);
      error = 'حدث خطأ أثناء حفظ بيانات الوحدة: ' + (err.message || err);
    } finally {
      isSaving = false;
    }
  }
  
  // تحميل البيانات عند تحميل الصفحة
  onMount(fetchUnitData);
</script>

<svelte:head>
  <title>إدارة شعارات وأختام الوحدة</title>
</svelte:head>

<div class="container mx-auto p-4 rtl">
  <div class="mb-6 flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold mb-2">إدارة شعارات وأختام الوحدة</h1>
      <p class="text-gray-600">قم بتعديل شعارات وأختام الوحدة: {unitName}</p>
    </div>
    
    <a href="/admin/units" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded">
      العودة إلى قائمة الوحدات
    </a>
  </div>
  
  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      {error}
    </div>
  {/if}
  
  {#if success}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      {success}
    </div>
  {/if}
  
  {#if isLoading}
    <div class="flex justify-center items-center h-64">
      <div class="loader"></div>
    </div>
  {:else if !isAdmin}
    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
      <p>ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
    </div>
  {:else}
    <div class="bg-white p-6 rounded-lg shadow-md">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- شعار الوحدة -->
        <div class="border p-4 rounded-lg">
          <h3 class="font-bold mb-3">شعار الوحدة</h3>
          <div class="flex flex-col items-center">
            {#if unitLogoUrl}
              <div class="mb-3 w-32 h-32 flex items-center justify-center">
                <img src={unitLogoUrl} alt="شعار الوحدة" class="max-w-full max-h-full" />
              </div>
            {/if}
            
            <label for="unit-logo-upload" class="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
              {#if uploadingUnitLogo}
                <span>جاري الرفع...</span>
              {:else}
                <span>{unitLogoUrl ? 'تغيير الشعار' : 'رفع الشعار'}</span>
              {/if}
            </label>
            <input
              type="file"
              id="unit-logo-upload"
              accept="image/*"
              on:change={uploadUnitLogo}
              disabled={uploadingUnitLogo}
              class="hidden"
            />
          </div>
        </div>
        
        <!-- ختم الوحدة -->
        <div class="border p-4 rounded-lg">
          <h3 class="font-bold mb-3">ختم الوحدة</h3>
          <div class="flex flex-col items-center">
            {#if unitStampUrl}
              <div class="mb-3 w-32 h-32 flex items-center justify-center">
                <img src={unitStampUrl} alt="ختم الوحدة" class="max-w-full max-h-full" />
              </div>
            {/if}
            
            <label for="unit-stamp-upload" class="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
              {#if uploadingUnitStamp}
                <span>جاري الرفع...</span>
              {:else}
                <span>{unitStampUrl ? 'تغيير الختم' : 'رفع الختم'}</span>
              {/if}
            </label>
            <input
              type="file"
              id="unit-stamp-upload"
              accept="image/*"
              on:change={uploadUnitStamp}
              disabled={uploadingUnitStamp}
              class="hidden"
            />
          </div>
        </div>
        
        <!-- الشعار الأيمن -->
        <div class="border p-4 rounded-lg">
          <h3 class="font-bold mb-3">الشعار الأيمن</h3>
          <div class="flex flex-col items-center">
            {#if rightLogoUrl}
              <div class="mb-3 w-32 h-32 flex items-center justify-center">
                <img src={rightLogoUrl} alt="الشعار الأيمن" class="max-w-full max-h-full" />
              </div>
            {/if}
            
            <label for="right-logo-upload" class="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
              {#if uploadingRightLogo}
                <span>جاري الرفع...</span>
              {:else}
                <span>{rightLogoUrl ? 'تغيير الشعار' : 'رفع الشعار'}</span>
              {/if}
            </label>
            <input
              type="file"
              id="right-logo-upload"
              accept="image/*"
              on:change={uploadRightLogo}
              disabled={uploadingRightLogo}
              class="hidden"
            />
          </div>
        </div>
        
        <!-- الشعار الأيسر -->
        <div class="border p-4 rounded-lg">
          <h3 class="font-bold mb-3">الشعار الأيسر</h3>
          <div class="flex flex-col items-center">
            {#if leftLogoUrl}
              <div class="mb-3 w-32 h-32 flex items-center justify-center">
                <img src={leftLogoUrl} alt="الشعار الأيسر" class="max-w-full max-h-full" />
              </div>
            {/if}
            
            <label for="left-logo-upload" class="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
              {#if uploadingLeftLogo}
                <span>جاري الرفع...</span>
              {:else}
                <span>{leftLogoUrl ? 'تغيير الشعار' : 'رفع الشعار'}</span>
              {/if}
            </label>
            <input
              type="file"
              id="left-logo-upload"
              accept="image/*"
              on:change={uploadLeftLogo}
              disabled={uploadingLeftLogo}
              class="hidden"
            />
          </div>
        </div>
      </div>
      
      <div class="flex justify-end">
        <button
          type="button"
          class="bg-green-500 hover:bg-green-600 text-white py-2 px-6 rounded"
          on:click={saveUnitData}
          disabled={isSaving}
        >
          {isSaving ? 'جاري الحفظ...' : 'حفظ البيانات'}
        </button>
      </div>
    </div>
  {/if}
</div>

<style>
  .rtl {
    direction: rtl;
  }
  
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
