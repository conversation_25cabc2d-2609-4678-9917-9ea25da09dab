import { supabase } from '$lib/supabase';
import type { Permission, Role } from '$lib/types/permissions';
import { Action, Resource, RoleType } from '$lib/types/permissions';

/**
 * خدمة إدارة الصلاحيات والأدوار
 */
export class PermissionService {
  /**
   * التحقق من وجود جدول في قاعدة البيانات
   * @param tableName اسم الجدول
   */
  private static async tableExists(tableName: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_name', tableName)
        .eq('table_schema', 'public')
        .single();

      if (error || !data) {
        console.error(`Error checking if table ${tableName} exists:`, error);
        return false;
      }

      return true;
    } catch (err) {
      console.error(`Error in tableExists for ${tableName}:`, err);
      return false;
    }
  }

  /**
   * الحصول على جميع الأدوار
   */
  static async getAllRoles(): Promise<Role[]> {
    try {
      console.log('Fetching all roles...');

      // التحقق من وجود جدول الأدوار
      const tableExists = await this.tableExists('roles');
      if (!tableExists) {
        console.error('Roles table does not exist');
        return [];
      }

      console.log('Roles table exists, fetching data...');

      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching roles:', error);
        return [];
      }

      console.log(`Fetched ${data?.length || 0} roles`);

      if (!data || data.length === 0) {
        console.warn('No roles found in the database');
      } else {
        console.log('First role:', data[0]);
      }

      return data || [];
    } catch (err) {
      console.error('Error in getAllRoles:', err);
      return [];
    }
  }

  /**
   * الحصول على جميع الصلاحيات
   */
  static async getAllPermissions(): Promise<Permission[]> {
    try {
      // التحقق من وجود جدول الصلاحيات
      const tableExists = await this.tableExists('permissions');
      if (!tableExists) {
        console.error('Permissions table does not exist');
        return [];
      }

      const { data, error } = await supabase
        .from('permissions')
        .select('*')
        .order('resource')
        .order('action');

      if (error) {
        console.error('Error fetching permissions:', error);
        return [];
      }

      return data || [];
    } catch (err) {
      console.error('Error in getAllPermissions:', err);
      return [];
    }
  }

  /**
   * الحصول على صلاحيات دور معين
   * @param roleId معرف الدور
   */
  static async getRolePermissions(roleId: string): Promise<Permission[]> {
    try {
      // التحقق من وجود جدول العلاقة بين الأدوار والصلاحيات
      const tableExists = await this.tableExists('role_permissions');
      if (!tableExists) {
        console.error('Role_permissions table does not exist');
        return [];
      }

      const { data, error } = await supabase
        .from('role_permissions')
        .select(`
          permission_id,
          permissions:permission_id (*)
        `)
        .eq('role_id', roleId);

      if (error) {
        console.error('Error fetching role permissions:', error);
        return [];
      }

      if (!data || data.length === 0) {
        return [];
      }

      // التحقق من وجود البيانات المرتبطة
      const permissions: Permission[] = [];

      for (const item of data) {
        if (item && item.permissions) {
          permissions.push(item.permissions as unknown as Permission);
        }
      }

      return permissions;
    } catch (err) {
      console.error('Error in getRolePermissions:', err);
      return [];
    }
  }

  /**
   * الحصول على صلاحيات المستخدم
   * @param userId معرف المستخدم
   */
  static async getUserPermissions(userId: string): Promise<Permission[]> {
    try {
      // الحصول على دور المستخدم
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role_id')
        .eq('id', userId)
        .single();

      if (profileError || !profile || !profile.role_id) {
        console.error('Error fetching user profile or no role assigned:', profileError);
        return [];
      }

      // الحصول على صلاحيات الدور
      return this.getRolePermissions(profile.role_id);
    } catch (err) {
      console.error('Error in getUserPermissions:', err);
      return [];
    }
  }

  /**
   * التحقق من صلاحية المستخدم
   * @param userId معرف المستخدم
   * @param resource المورد
   * @param action الإجراء
   */
  static async checkPermission(
    userId: string,
    resource: Resource,
    action: Action
  ): Promise<boolean> {
    try {
      const permissions = await this.getUserPermissions(userId);

      return permissions.some(
        permission => permission.resource === resource && permission.action === action
      );
    } catch (err) {
      console.error('Error in checkPermission:', err);
      return false;
    }
  }

  /**
   * التحقق من دور المستخدم
   * @param userId معرف المستخدم
   * @param role الدور المطلوب التحقق منه
   */
  static async checkRole(userId: string, role: RoleType): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          roles:role_id (name)
        `)
        .eq('id', userId)
        .single();

      if (error || !data || !data.roles) {
        console.error('Error checking user role:', error);
        return false;
      }

      return (data.roles as any).name === role;
    } catch (err) {
      console.error('Error in checkRole:', err);
      return false;
    }
  }

  /**
   * تعيين دور للمستخدم
   * @param userId معرف المستخدم
   * @param roleId معرف الدور
   */
  static async assignRoleToUser(userId: string, roleId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ role_id: roleId })
        .eq('id', userId);

      if (error) {
        console.error('Error assigning role to user:', error);
        return false;
      }

      return true;
    } catch (err) {
      console.error('Error in assignRoleToUser:', err);
      return false;
    }
  }

  /**
   * إنشاء دور جديد
   * @param name اسم الدور
   * @param description وصف الدور
   */
  static async createRole(name: string, description?: string): Promise<Role | null> {
    try {
      const { data, error } = await supabase
        .from('roles')
        .insert({ name, description })
        .select()
        .single();

      if (error) {
        console.error('Error creating role:', error);
        return null;
      }

      return data;
    } catch (err) {
      console.error('Error in createRole:', err);
      return null;
    }
  }

  /**
   * إضافة صلاحية لدور
   * @param roleId معرف الدور
   * @param permissionId معرف الصلاحية
   */
  static async addPermissionToRole(roleId: string, permissionId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('role_permissions')
        .insert({ role_id: roleId, permission_id: permissionId });

      if (error) {
        console.error('Error adding permission to role:', error);
        return false;
      }

      return true;
    } catch (err) {
      console.error('Error in addPermissionToRole:', err);
      return false;
    }
  }

  /**
   * إزالة صلاحية من دور
   * @param roleId معرف الدور
   * @param permissionId معرف الصلاحية
   */
  static async removePermissionFromRole(roleId: string, permissionId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('role_permissions')
        .delete()
        .eq('role_id', roleId)
        .eq('permission_id', permissionId);

      if (error) {
        console.error('Error removing permission from role:', error);
        return false;
      }

      return true;
    } catch (err) {
      console.error('Error in removePermissionFromRole:', err);
      return false;
    }
  }
}
