-- تحديث الهيكل التنظيمي لربط المكاتب والفروع بشكل صحيح

-- إنشاء وظيفة RPC لتحديث الهيكل التنظيمي
CREATE OR REPLACE FUNCTION update_organization_hierarchy()
RETURNS VOID AS $$
DECLARE
  general_admin_id UUID;
  sabhaa_office_id UUID;
  alshati_office_id UUID;
BEGIN
  -- الحصول على معرف إدارة المحاماة العامة
  SELECT id INTO general_admin_id FROM units WHERE name = 'ادارة المحاماه العامة';

  -- إذا لم تكن موجودة، قم بإنشائها
  IF general_admin_id IS NULL THEN
    INSERT INTO units (name, type, description)
    VALUES ('ادارة المحاماه العامة', 'إدارة', 'إدارة المحاماة العامة')
    RETURNING id INTO general_admin_id;
  END IF;

  -- الحصول على معرف مكتب المحاماة سبها
  SELECT id INTO sabhaa_office_id FROM units WHERE name = 'مكتب المحاماه سبها';

  -- إذا لم يكن موجوداً، قم بإنشائه وربطه بإدارة المحاماة العامة
  IF sabhaa_office_id IS NULL THEN
    INSERT INTO units (name, type, description, parent_id)
    VALUES ('مكتب المحاماه سبها', 'فرع', 'مكتب المحاماة في سبها', general_admin_id)
    RETURNING id INTO sabhaa_office_id;
  ELSE
    -- تحديث نوع الوحدة وربطها بإدارة المحاماة العامة
    UPDATE units
    SET type = 'فرع', parent_id = general_admin_id
    WHERE id = sabhaa_office_id;
  END IF;

  -- الحصول على معرف مكتب المحاماة العامة الشاطئ
  SELECT id INTO alshati_office_id FROM units WHERE name = 'مكتب المحاماه العامة الشاطئ';

  -- إذا لم يكن موجوداً، قم بإنشائه وربطه بمكتب المحاماة سبها
  IF alshati_office_id IS NULL THEN
    INSERT INTO units (name, type, description, parent_id)
    VALUES ('مكتب المحاماه العامة الشاطئ', 'مكتب', 'مكتب المحاماة في الشاطئ', sabhaa_office_id)
    RETURNING id INTO alshati_office_id;
  ELSE
    -- تحديث نوع الوحدة وربطها بمكتب المحاماة سبها
    UPDATE units
    SET type = 'مكتب', parent_id = sabhaa_office_id
    WHERE id = alshati_office_id;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 2. إضافة وظيفة للتحقق من صحة الهيكل التنظيمي
CREATE OR REPLACE FUNCTION validate_unit_hierarchy()
RETURNS TABLE (
  id UUID,
  name TEXT,
  type TEXT,
  parent_id UUID,
  parent_name TEXT,
  parent_type TEXT,
  is_valid BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    u.id,
    u.name,
    u.type,
    u.parent_id,
    p.name AS parent_name,
    p.type AS parent_type,
    CASE
      WHEN u.parent_id IS NULL THEN TRUE -- الوحدات الجذرية صحيحة دائماً
      WHEN u.type = 'إدارة' AND p.type IN ('وزارة') THEN TRUE -- الإدارة يمكن أن تكون تابعة للوزارة
      WHEN u.type = 'فرع' AND p.type IN ('إدارة', 'وزارة') THEN TRUE -- الفرع يمكن أن يكون تابعاً للإدارة أو الوزارة
      WHEN u.type = 'قسم' AND p.type IN ('إدارة', 'فرع') THEN TRUE -- القسم يمكن أن يكون تابعاً للإدارة أو الفرع
      WHEN u.type = 'مكتب' AND p.type IN ('إدارة', 'فرع', 'قسم') THEN TRUE -- المكتب يمكن أن يكون تابعاً للإدارة أو الفرع أو القسم
      ELSE FALSE -- أي علاقة أخرى غير صحيحة
    END AS is_valid
  FROM units u
  LEFT JOIN units p ON u.parent_id = p.id;
END;
$$ LANGUAGE plpgsql;

-- 3. إضافة وظيفة لعرض الهيكل التنظيمي بشكل متدرج
CREATE OR REPLACE FUNCTION get_organization_hierarchy()
RETURNS TABLE (
  id UUID,
  name TEXT,
  type TEXT,
  parent_id UUID,
  level INT,
  path TEXT
) AS $$
WITH RECURSIVE org_hierarchy AS (
  -- الوحدات الجذرية (بدون وحدة أم)
  SELECT
    u.id,
    u.name,
    u.type,
    u.parent_id,
    0 AS level,
    u.name::TEXT AS path
  FROM units u
  WHERE u.parent_id IS NULL

  UNION ALL

  -- الوحدات التابعة بشكل متدرج
  SELECT
    u.id,
    u.name,
    u.type,
    u.parent_id,
    oh.level + 1,
    oh.path || ' > ' || u.name
  FROM units u
  JOIN org_hierarchy oh ON u.parent_id = oh.id
)
SELECT * FROM org_hierarchy
ORDER BY path;
$$ LANGUAGE SQL;
