/* ملف أنماط الوضع المظلم */

/* تطبيق الوضع المظلم على العناصر الرئيسية */
.dark {
  background-color: #111827;
  color: #f3f4f6;
}

.dark body {
  background-color: #111827;
  color: #f3f4f6;
}

.dark .bg-card {
  background-color: #1f2937;
}

.dark .bg-white {
  background-color: #111827;
}

.dark .text-gray-900 {
  color: #f3f4f6;
}

.dark .border-gray-200 {
  border-color: #374151;
}

.dark .text-muted-foreground {
  color: #9ca3af;
}

/* تطبيق الوضع المظلم على العناصر الأخرى */
.dark .hover\:bg-gray-100:hover {
  background-color: #1f2937;
}

.dark .hover\:bg-accent:hover {
  background-color: #1f2937;
}

.dark .hover\:text-accent-foreground:hover {
  color: #f3f4f6;
}

.dark .bg-muted\/50 {
  background-color: rgba(31, 41, 55, 0.5);
}

.dark .bg-primary\/10 {
  background-color: rgba(59, 130, 246, 0.1);
}

.dark .bg-primary\/5 {
  background-color: rgba(59, 130, 246, 0.05);
}

.dark .border-primary\/20 {
  border-color: rgba(59, 130, 246, 0.2);
}

.dark .bg-blue-100 {
  background-color: rgba(59, 130, 246, 0.2);
}

.dark .bg-green-100 {
  background-color: rgba(16, 185, 129, 0.2);
}

.dark .bg-amber-100 {
  background-color: rgba(245, 158, 11, 0.2);
}

.dark .bg-purple-100 {
  background-color: rgba(139, 92, 246, 0.2);
}

.dark .text-blue-700 {
  color: #60a5fa;
}

.dark .text-green-700 {
  color: #34d399;
}

.dark .text-amber-700 {
  color: #fbbf24;
}

.dark .text-purple-700 {
  color: #a78bfa;
}

.dark .text-primary {
  color: #60a5fa;
}

/* تطبيق الوضع المظلم على النماذج */
.dark input,
.dark textarea,
.dark select {
  background-color: #1f2937;
  border-color: #374151;
  color: #f3f4f6;
}

.dark button,
.dark .btn {
  border-color: #374151;
}

.dark .bg-destructive\/10 {
  background-color: rgba(239, 68, 68, 0.1);
}

.dark .border-destructive\/30 {
  border-color: rgba(239, 68, 68, 0.3);
}

.dark .text-destructive {
  color: #f87171;
}
