<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';
  import { PermissionService } from '$lib/services/permissionService';
  import { getRoleNameArabic } from '$lib/types/permissions';
  import { Action, Resource, RoleType } from '$lib/types/permissions';
  import type { Role, Permission } from '$lib/types/permissions';

  type User = {
    id: string;
    full_name: string;
    email: string;
    role_id: string;
    role_name?: string;
    unit_id: string | null;
    unit_name?: string;
    created_at: string;
  };

  type Unit = {
    id: string;
    name: string;
    type: string;
  };

  let users: User[] = [];
  let units: Unit[] = [];
  let systemRoles: Role[] = [];
  let isAdmin = false;
  let hasManageUsersPermission = false;
  let dialogOpen = false;
  let loading = true;

  let newUser = {
    full_name: '',
    email: '',
    password: '',
    role_id: '',
    unit_id: null as string | null
  };

  onMount(async () => {
    try {
      loading = true;

      // الحصول على المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        alert('يجب تسجيل الدخول للوصول إلى هذه الصفحة');
        return;
      }

      // التحقق من صلاحيات المستخدم
      const [hasAdminRole, hasPermission] = await Promise.all([
        PermissionService.checkRole(user.id, RoleType.ADMIN),
        PermissionService.checkPermission(user.id, Resource.USERS, Action.READ)
      ]);

      isAdmin = hasAdminRole;
      hasManageUsersPermission = hasPermission;

      if (!isAdmin && !hasManageUsersPermission) {
        alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
        return;
      }

      // جلب الأدوار
      systemRoles = await PermissionService.getAllRoles();

      // جلب الوحدات التنظيمية
      const { data: unitsData } = await supabase
        .from('units')
        .select('id, name, type')
        .order('name');

      if (unitsData) {
        units = unitsData;
      }

      // جلب المستخدمين مع أدوارهم والوحدات التنظيمية
      const { data: usersData } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          role_id,
          unit_id,
          created_at,
          roles:role_id (
            id,
            name
          ),
          units:unit_id (
            name
          )
        `)
        .order('full_name');

      if (usersData) {
        users = usersData.map(user => ({
          id: user.id,
          full_name: user.full_name,
          email: user.email,
          role_id: user.role_id,
          role_name: user.roles ? getRoleNameArabic(user.roles.name) : 'غير محدد',
          unit_id: user.unit_id,
          unit_name: user.units ? user.units.name : 'غير محدد',
          created_at: user.created_at
        }));
      }

      // تعيين الدور الافتراضي للمستخدم الجديد
      if (systemRoles.length > 0) {
        const userRole = systemRoles.find(r => r.name === RoleType.USER);
        newUser.role_id = userRole ? userRole.id : systemRoles[0].id;
      }
    } catch (error) {
      console.error('Error loading data:', error);
      alert('حدث خطأ أثناء تحميل البيانات');
    } finally {
      loading = false;
    }
  });

  async function handleAddUser() {
    if (!newUser.full_name || !newUser.email || !newUser.password || !newUser.role_id) {
      alert('يرجى إدخال جميع البيانات المطلوبة');
      return;
    }

    try {
      // إنشاء المستخدم في Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: newUser.email,
        password: newUser.password
      });

      if (authError) {
        alert(`حدث خطأ أثناء إنشاء المستخدم: ${authError.message}`);
        return;
      }

      if (authData.user) {
        // إنشاء الملف الشخصي
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .update({
            full_name: newUser.full_name,
            role_id: newUser.role_id,
            unit_id: newUser.unit_id
          })
          .eq('id', authData.user.id)
          .select();

        if (profileError) {
          alert(`حدث خطأ أثناء إنشاء الملف الشخصي: ${profileError.message}`);
          return;
        }

        if (profileData && profileData.length > 0) {
          // الحصول على معلومات الدور والوحدة
          const selectedRole = systemRoles.find(r => r.id === newUser.role_id);
          const selectedUnit = units.find(u => u.id === newUser.unit_id);

          // إضافة المستخدم الجديد إلى القائمة
          const newUserWithDetails: User = {
            id: authData.user.id,
            full_name: newUser.full_name,
            email: newUser.email,
            role_id: newUser.role_id,
            role_name: selectedRole ? getRoleNameArabic(selectedRole.name) : 'غير محدد',
            unit_id: newUser.unit_id,
            unit_name: selectedUnit ? selectedUnit.name : 'غير محدد',
            created_at: new Date().toISOString()
          };

          users = [...users, newUserWithDetails];

          // إعادة تعيين النموذج
          newUser = {
            full_name: '',
            email: '',
            password: '',
            role_id: newUser.role_id, // الاحتفاظ بنفس الدور للمستخدم التالي
            unit_id: null
          };

          dialogOpen = false;
        }
      }
    } catch (error) {
      console.error('Error adding user:', error);
      alert('حدث خطأ أثناء إضافة المستخدم');
    }
  }

  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  }

  // تغيير دور المستخدم
  async function changeUserRole(userId: string, roleId: string) {
    try {
      await PermissionService.assignRoleToUser(userId, roleId);

      // تحديث قائمة المستخدمين
      const selectedRole = systemRoles.find(r => r.id === roleId);

      users = users.map(user => {
        if (user.id === userId) {
          return {
            ...user,
            role_id: roleId,
            role_name: selectedRole ? getRoleNameArabic(selectedRole.name) : 'غير محدد'
          };
        }
        return user;
      });
    } catch (err) {
      console.error('Error changing user role:', err);
      alert('حدث خطأ أثناء تغيير دور المستخدم');
    }
  }
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">إدارة المستخدمين</h1>

    {#if isAdmin}
      <button
        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
        on:click={() => dialogOpen = true}
      >
        إضافة مستخدم جديد
      </button>
    {/if}
  </div>

  <!-- جدول المستخدمين -->
  <div class="bg-card rounded-lg shadow overflow-hidden">
    <div class="p-6">
      <h2 class="text-xl font-bold">قائمة المستخدمين</h2>
      <p class="text-muted-foreground">عرض جميع المستخدمين في النظام</p>
    </div>

    {#if loading}
      <div class="flex items-center justify-center h-64">
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary dark:border-blue-400"></div>
      </div>
    {:else if users.length === 0}
      <div class="text-center text-muted-foreground py-8">
        <p>لا يوجد مستخدمين بعد</p>
      </div>
    {:else}
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-muted">
            <tr>
              <th class="text-right p-3 font-medium">الاسم</th>
              <th class="text-right p-3 font-medium">البريد الإلكتروني</th>
              <th class="text-right p-3 font-medium">الدور</th>
              <th class="text-right p-3 font-medium">الوحدة التنظيمية</th>
              <th class="text-right p-3 font-medium">تاريخ الإنشاء</th>
              <th class="text-right p-3 font-medium">الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {#each users as user}
              <tr class="border-t hover:bg-muted/50">
                <td class="p-3 font-medium">{user.full_name}</td>
                <td class="p-3">{user.email}</td>
                <td class="p-3">{user.role_name || 'غير محدد'}</td>
                <td class="p-3">{user.unit_name}</td>
                <td class="p-3">{formatDate(user.created_at)}</td>
                <td class="p-3">
                  {#if isAdmin}
                    <div class="flex items-center space-x-2 space-x-reverse">
                      <select
                        value={user.role_id}
                        on:change={(e) => changeUserRole(user.id, (e.target as HTMLSelectElement).value)}
                        class="rounded-md border border-gray-200 bg-white px-3 py-1 text-sm ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200"
                      >
                        {#each systemRoles as role}
                          <option value={role.id}>{getRoleNameArabic(role.name)}</option>
                        {/each}
                      </select>
                    </div>
                  {:else}
                    <button
                      class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3"
                    >
                      عرض
                    </button>
                  {/if}
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>

  <!-- نموذج إضافة مستخدم جديد -->
  {#if dialogOpen}
    <div class="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="bg-card rounded-lg shadow-lg w-full max-w-md">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">إضافة مستخدم جديد</h2>
            <button
              class="text-muted-foreground hover:text-foreground"
              on:click={() => dialogOpen = false}
            >
              ✕
            </button>
          </div>

          <div class="grid gap-4 py-4">
            <div class="grid gap-2">
              <label for="full_name" class="block text-sm font-medium">الاسم الكامل</label>
              <input
                id="full_name"
                bind:value={newUser.full_name}
                placeholder="أدخل الاسم الكامل"
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>

            <div class="grid gap-2">
              <label for="email" class="block text-sm font-medium">البريد الإلكتروني</label>
              <input
                id="email"
                type="email"
                bind:value={newUser.email}
                placeholder="أدخل البريد الإلكتروني"
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>

            <div class="grid gap-2">
              <label for="password" class="block text-sm font-medium">كلمة المرور</label>
              <input
                id="password"
                type="password"
                bind:value={newUser.password}
                placeholder="أدخل كلمة المرور"
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>

            <div class="grid gap-2">
              <label for="role_id" class="block text-sm font-medium">الدور</label>
              <select
                id="role_id"
                bind:value={newUser.role_id}
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="" disabled selected>اختر الدور</option>
                {#each systemRoles as role}
                  <option value={role.id}>{getRoleNameArabic(role.name)}</option>
                {/each}
              </select>
            </div>

            <div class="grid gap-2">
              <label for="unit" class="block text-sm font-medium">الوحدة التنظيمية</label>
              <select
                id="unit"
                bind:value={newUser.unit_id}
                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value={null}>غير محدد</option>
                {#each units as unit}
                  <option value={unit.id}>{unit.name} ({unit.type})</option>
                {/each}
              </select>
            </div>
          </div>

          <div class="flex justify-end gap-2 mt-4">
            <button
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
              on:click={() => dialogOpen = false}
            >
              إلغاء
            </button>
            <button
              class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
              on:click={handleAddUser}
            >
              إضافة
            </button>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>
