/**
 * أنواع الأدوار المتاحة في النظام
 */
export const RoleType = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  USER: 'user'
};

/**
 * الموارد المتاحة في النظام
 */
export const Resource = {
  DOCUMENTS: 'documents',
  USERS: 'users',
  MESSAGES: 'messages',
  BROADCASTS: 'broadcasts',
  ORGANIZATION: 'organization',
  SYSTEM: 'system'
};

/**
 * الإجراءات المتاحة على الموارد
 */
export const Action = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  SETTINGS: 'settings',
  LOGS: 'logs'
};

/**
 * الحصول على الاسم العربي للدور
 * @param {string} roleName - اسم الدور باللغة الإنجليزية
 * @returns {string} - الاسم العربي للدور
 */
export function getRoleNameArabic(roleName) {
  const roleNames = {
    'admin': 'مدير النظام',
    'manager': 'مدير',
    'user': 'مستخدم عادي'
  };
  
  return roleNames[roleName] || roleName;
}

/**
 * الحصول على الاسم العربي للصلاحية
 * @param {string} resource - المورد
 * @param {string} action - الإجراء
 * @returns {string} - الاسم العربي للصلاحية
 */
export function getPermissionNameArabic(resource, action) {
  const resourceNames = {
    'documents': 'المستندات',
    'users': 'المستخدمين',
    'messages': 'المراسلات',
    'broadcasts': 'التعميمات',
    'organization': 'الهيكل التنظيمي',
    'system': 'النظام'
  };
  
  const actionNames = {
    'create': 'إنشاء',
    'read': 'قراءة',
    'update': 'تعديل',
    'delete': 'حذف',
    'settings': 'إعدادات',
    'logs': 'سجلات'
  };
  
  const resourceName = resourceNames[resource] || resource;
  const actionName = actionNames[action] || action;
  
  return `${actionName} ${resourceName}`;
}
