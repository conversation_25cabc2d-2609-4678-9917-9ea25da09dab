<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib/supabase';
  import { PermissionService } from '$lib/services/permissionService';
  import { RoleType } from '$lib/types/permissions';

  let loading = true;
  let error = null;

  onMount(async () => {
    try {
      // الحصول على المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        // إذا لم يكن المستخدم مسجل الدخول، توجيهه إلى صفحة تسجيل الدخول
        goto('/login');
        return;
      }

      // التحقق من صلاحيات المستخدم
      const isAdmin = await PermissionService.checkRole(user.id, RoleType.ADMIN);
      const isManager = await PermissionService.checkRole(user.id, RoleType.MANAGER);

      if (!isAdmin && !isManager) {
        // إذا لم يكن المستخدم مديرًا، توجيهه إلى لوحة التحكم العادية
        goto('/dashboard');
        return;
      }
    } catch (e) {
      console.error('Error in admin layout:', e);
      error = e.message;
    } finally {
      loading = false;
    }
  });
</script>

{#if loading}
  <div class="flex justify-center items-center h-screen">
    <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
  </div>
{:else if error}
  <div class="container mx-auto p-4 rtl" dir="rtl">
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{error}</p>
    </div>
  </div>
{:else}
  <div class="flex h-screen bg-gray-100 rtl" dir="rtl">
    <!-- القائمة الجانبية -->
    <div class="w-64 bg-white shadow-md">
      <div class="p-4 border-b">
        <h2 class="text-xl font-bold text-primary">لوحة تحكم المدير</h2>
      </div>

      <nav class="p-4">
        <ul class="space-y-2">
          <li>
            <a href="/admin/users" class="flex items-center p-2 rounded hover:bg-gray-100">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
              <span>إدارة المستخدمين</span>
            </a>
          </li>
          <li>
            <a href="/admin/units" class="flex items-center p-2 rounded hover:bg-gray-100">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect><path d="M3 9h18"></path><path d="M9 21V9"></path></svg>
              <span>الوحدات التنظيمية</span>
            </a>
          </li>
          <li>
            <a href="/admin/permissions" class="flex items-center p-2 rounded hover:bg-gray-100">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><rect width="18" height="11" x="3" y="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>
              <span>الصلاحيات</span>
            </a>
          </li>
          <li>
            <a href="/admin/organization-settings" class="flex items-center p-2 rounded hover:bg-gray-100">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>
              <span>إعدادات المنظمة</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="flex-1 overflow-auto">
      <div class="p-6">
        <slot />
      </div>
    </div>
  </div>
{/if}

<style>
  .rtl {
    direction: rtl;
    text-align: right;
  }
</style>
