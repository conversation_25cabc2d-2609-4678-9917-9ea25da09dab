import { supabase } from '$lib/supabase';

/**
 * خدمة إدارة تخزين محتوى المستندات في Supabase Storage
 */
export class DocumentStorageService {
  private static readonly BUCKET_NAME = 'saadabujnah';
  
  /**
   * حفظ محتوى المستند في Storage
   * @param documentId معرف المستند
   * @param content محتوى المستند (HTML)
   * @returns مسار الملف في Storage أو null في حالة الخطأ
   */
  static async saveDocumentContent(documentId: string, content: string): Promise<string | null> {
    try {
      // توليد مسار فريد للملف
      const fileName = `documents/${new Date().getFullYear()}/${new Date().getMonth() + 1}/${documentId}.html`;
      
      // تحويل المحتوى إلى Blob
      const blob = new Blob([content], { type: 'text/html; charset=utf-8' });
      
      // رفع الملف إلى Storage
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, blob, {
          cacheControl: '3600',
          upsert: true // استبدال الملف إذا كان موجوداً
        });

      if (error) {
        console.error('خطأ في رفع محتوى المستند:', error);
        return null;
      }

      console.log('تم حفظ محتوى المستند بنجاح:', data.path);
      return data.path;
    } catch (err) {
      console.error('خطأ في حفظ محتوى المستند:', err);
      return null;
    }
  }

  /**
   * حفظ محتوى المرفق في Storage
   * @param messageId معرف الرسالة
   * @param content محتوى المرفق (HTML)
   * @returns مسار الملف في Storage أو null في حالة الخطأ
   */
  static async saveAttachmentContent(messageId: string, content: string): Promise<string | null> {
    try {
      // توليد مسار فريد للملف
      const fileName = `attachments/${new Date().getFullYear()}/${new Date().getMonth() + 1}/${messageId}.html`;
      
      // تحويل المحتوى إلى Blob
      const blob = new Blob([content], { type: 'text/html; charset=utf-8' });
      
      // رفع الملف إلى Storage
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, blob, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        console.error('خطأ في رفع محتوى المرفق:', error);
        return null;
      }

      console.log('تم حفظ محتوى المرفق بنجاح:', data.path);
      return data.path;
    } catch (err) {
      console.error('خطأ في حفظ محتوى المرفق:', err);
      return null;
    }
  }

  /**
   * جلب محتوى المستند من Storage
   * @param filePath مسار الملف في Storage
   * @returns محتوى المستند أو null في حالة الخطأ
   */
  static async getDocumentContent(filePath: string): Promise<string | null> {
    try {
      // تحميل الملف من Storage
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .download(filePath);

      if (error) {
        console.error('خطأ في تحميل محتوى المستند:', error);
        return null;
      }

      // تحويل البيانات إلى نص
      const content = await data.text();
      return content;
    } catch (err) {
      console.error('خطأ في جلب محتوى المستند:', err);
      return null;
    }
  }

  /**
   * الحصول على رابط عام للملف
   * @param filePath مسار الملف في Storage
   * @returns رابط الملف أو null في حالة الخطأ
   */
  static async getPublicUrl(filePath: string): Promise<string | null> {
    try {
      const { data } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(filePath);

      return data.publicUrl;
    } catch (err) {
      console.error('خطأ في الحصول على الرابط العام:', err);
      return null;
    }
  }

  /**
   * الحصول على رابط موقع للملف (مع انتهاء صلاحية)
   * @param filePath مسار الملف في Storage
   * @param expiresIn مدة انتهاء الصلاحية بالثواني (افتراضي: ساعة واحدة)
   * @returns رابط الملف الموقع أو null في حالة الخطأ
   */
  static async getSignedUrl(filePath: string, expiresIn: number = 3600): Promise<string | null> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .createSignedUrl(filePath, expiresIn);

      if (error) {
        console.error('خطأ في إنشاء الرابط الموقع:', error);
        return null;
      }

      return data.signedUrl;
    } catch (err) {
      console.error('خطأ في الحصول على الرابط الموقع:', err);
      return null;
    }
  }

  /**
   * حذف ملف من Storage
   * @param filePath مسار الملف في Storage
   * @returns true في حالة النجاح، false في حالة الخطأ
   */
  static async deleteFile(filePath: string): Promise<boolean> {
    try {
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath]);

      if (error) {
        console.error('خطأ في حذف الملف:', error);
        return false;
      }

      console.log('تم حذف الملف بنجاح:', filePath);
      return true;
    } catch (err) {
      console.error('خطأ في حذف الملف:', err);
      return false;
    }
  }

  /**
   * تحديث محتوى المستند في قاعدة البيانات مع رابط Storage
   * @param documentId معرف المستند
   * @param contentUrl رابط المحتوى في Storage
   * @returns true في حالة النجاح، false في حالة الخطأ
   */
  static async updateDocumentContentUrl(documentId: string, contentUrl: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('documents')
        .update({ 
          content_url: contentUrl,
          // يمكن حذف المحتوى القديم من قاعدة البيانات لتوفير المساحة
          // content: null 
        })
        .eq('id', documentId);

      if (error) {
        console.error('خطأ في تحديث رابط محتوى المستند:', error);
        return false;
      }

      return true;
    } catch (err) {
      console.error('خطأ في تحديث رابط محتوى المستند:', err);
      return false;
    }
  }

  /**
   * تحديث محتوى المرفق في قاعدة البيانات مع رابط Storage
   * @param messageId معرف الرسالة
   * @param contentUrl رابط المحتوى في Storage
   * @returns true في حالة النجاح، false في حالة الخطأ
   */
  static async updateAttachmentContentUrl(messageId: string, contentUrl: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('messages')
        .update({ 
          attachment_content_url: contentUrl
        })
        .eq('id', messageId);

      if (error) {
        console.error('خطأ في تحديث رابط محتوى المرفق:', error);
        return false;
      }

      return true;
    } catch (err) {
      console.error('خطأ في تحديث رابط محتوى المرفق:', err);
      return false;
    }
  }

  /**
   * نقل المحتوى الموجود من قاعدة البيانات إلى Storage
   * @param documentId معرف المستند
   * @returns true في حالة النجاح، false في حالة الخطأ
   */
  static async migrateDocumentToStorage(documentId: string): Promise<boolean> {
    try {
      // جلب المحتوى الحالي من قاعدة البيانات
      const { data: document, error: fetchError } = await supabase
        .from('documents')
        .select('content')
        .eq('id', documentId)
        .single();

      if (fetchError || !document?.content) {
        console.error('لا يوجد محتوى للنقل:', fetchError);
        return false;
      }

      // حفظ المحتوى في Storage
      const storagePath = await this.saveDocumentContent(documentId, document.content);
      if (!storagePath) {
        return false;
      }

      // تحديث قاعدة البيانات برابط Storage
      const updated = await this.updateDocumentContentUrl(documentId, storagePath);
      if (!updated) {
        // حذف الملف من Storage في حالة فشل التحديث
        await this.deleteFile(storagePath);
        return false;
      }

      console.log('تم نقل المستند إلى Storage بنجاح:', documentId);
      return true;
    } catch (err) {
      console.error('خطأ في نقل المستند إلى Storage:', err);
      return false;
    }
  }
}
