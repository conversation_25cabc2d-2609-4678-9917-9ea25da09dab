-- تحديث سياسات الأمان للمستندات

-- حذف السياسات الحالية
DROP POLICY IF EXISTS documents_select_policy ON documents;
DROP POLICY IF EXISTS documents_insert_policy ON documents;
DROP POLICY IF EXISTS documents_update_policy ON documents;
DROP POLICY IF EXISTS documents_delete_policy ON documents;

-- سياسة قراءة المستندات (للمستخدمين المصرح لهم فقط)
CREATE POLICY documents_select_policy ON documents
FOR SELECT USING (
  -- المستخدم هو منشئ المستند
  created_by = auth.uid() 
  OR 
  -- المستخدم مشرف
  EXISTS (
    SELECT 1 FROM profiles p
    JOIN roles r ON p.role_id = r.id
    WHERE p.id = auth.uid() AND (r.name = 'admin' OR r.name = 'مشرف')
  )
  OR
  -- المستند ينتمي لوحدة المستخدم
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND unit_id = documents.unit_id
  )
  OR
  -- المستند ينتمي لوحدة تابعة لوحدة المستخدم
  EXISTS (
    WITH RECURSIVE subordinates AS (
      -- الوحدة الأساسية (وحدة المستخدم)
      SELECT 
        u.id
      FROM units u
      JOIN profiles p ON u.id = p.unit_id
      WHERE p.id = auth.uid()
      
      UNION ALL
      
      -- الوحدات التابعة بشكل متدرج
      SELECT 
        u.id
      FROM units u
      JOIN subordinates s ON u.parent_id = s.id
    )
    SELECT 1 FROM subordinates
    WHERE id = documents.unit_id
  )
);

-- سياسة إنشاء المستندات (للمستخدمين المصرح لهم فقط)
CREATE POLICY documents_insert_policy ON documents
FOR INSERT WITH CHECK (
  -- المستخدم مسجل الدخول
  auth.uid() IS NOT NULL
  AND
  (
    -- المستند ينتمي لوحدة المستخدم
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND unit_id = documents.unit_id
    )
    OR
    -- المستند ينتمي لوحدة تابعة لوحدة المستخدم
    EXISTS (
      WITH RECURSIVE subordinates AS (
        -- الوحدة الأساسية (وحدة المستخدم)
        SELECT 
          u.id
        FROM units u
        JOIN profiles p ON u.id = p.unit_id
        WHERE p.id = auth.uid()
        
        UNION ALL
        
        -- الوحدات التابعة بشكل متدرج
        SELECT 
          u.id
        FROM units u
        JOIN subordinates s ON u.parent_id = s.id
      )
      SELECT 1 FROM subordinates
      WHERE id = documents.unit_id
    )
    OR
    -- المستخدم مشرف
    EXISTS (
      SELECT 1 FROM profiles p
      JOIN roles r ON p.role_id = r.id
      WHERE p.id = auth.uid() AND (r.name = 'admin' OR r.name = 'مشرف')
    )
  )
);

-- سياسة تعديل المستندات (للمنشئ والمشرفين)
CREATE POLICY documents_update_policy ON documents
FOR UPDATE USING (
  created_by = auth.uid() 
  OR 
  EXISTS (
    SELECT 1 FROM profiles p
    JOIN roles r ON p.role_id = r.id
    WHERE p.id = auth.uid() AND (r.name = 'admin' OR r.name = 'مشرف')
  )
);

-- سياسة حذف المستندات (للمنشئ والمشرفين)
CREATE POLICY documents_delete_policy ON documents
FOR DELETE USING (
  created_by = auth.uid() 
  OR 
  EXISTS (
    SELECT 1 FROM profiles p
    JOIN roles r ON p.role_id = r.id
    WHERE p.id = auth.uid() AND (r.name = 'admin' OR r.name = 'مشرف')
  )
);

-- سياسات المرفقات

-- حذف السياسات الحالية
DROP POLICY IF EXISTS document_attachments_select_policy ON document_attachments;
DROP POLICY IF EXISTS document_attachments_insert_policy ON document_attachments;
DROP POLICY IF EXISTS document_attachments_delete_policy ON document_attachments;

-- سياسة قراءة المرفقات (نفس سياسة قراءة المستندات)
CREATE POLICY document_attachments_select_policy ON document_attachments
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM documents d
    WHERE d.id = document_attachments.document_id
    AND (
      -- المستخدم هو منشئ المستند
      d.created_by = auth.uid() 
      OR 
      -- المستخدم مشرف
      EXISTS (
        SELECT 1 FROM profiles p
        JOIN roles r ON p.role_id = r.id
        WHERE p.id = auth.uid() AND (r.name = 'admin' OR r.name = 'مشرف')
      )
      OR
      -- المستند ينتمي لوحدة المستخدم
      EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND unit_id = d.unit_id
      )
      OR
      -- المستند ينتمي لوحدة تابعة لوحدة المستخدم
      EXISTS (
        WITH RECURSIVE subordinates AS (
          -- الوحدة الأساسية (وحدة المستخدم)
          SELECT 
            u.id
          FROM units u
          JOIN profiles p ON u.id = p.unit_id
          WHERE p.id = auth.uid()
          
          UNION ALL
          
          -- الوحدات التابعة بشكل متدرج
          SELECT 
            u.id
          FROM units u
          JOIN subordinates s ON u.parent_id = s.id
        )
        SELECT 1 FROM subordinates
        WHERE id = d.unit_id
      )
    )
  )
);

-- سياسة إضافة المرفقات (للمنشئ والمشرفين)
CREATE POLICY document_attachments_insert_policy ON document_attachments
FOR INSERT WITH CHECK (
  -- المستخدم مسجل الدخول
  auth.uid() IS NOT NULL
  AND
  EXISTS (
    SELECT 1 FROM documents d
    WHERE d.id = document_attachments.document_id
    AND (
      -- المستخدم هو منشئ المستند
      d.created_by = auth.uid() 
      OR 
      -- المستخدم مشرف
      EXISTS (
        SELECT 1 FROM profiles p
        JOIN roles r ON p.role_id = r.id
        WHERE p.id = auth.uid() AND (r.name = 'admin' OR r.name = 'مشرف')
      )
    )
  )
);

-- سياسة حذف المرفقات (للمنشئ والمشرفين)
CREATE POLICY document_attachments_delete_policy ON document_attachments
FOR DELETE USING (
  created_by = auth.uid() 
  OR 
  EXISTS (
    SELECT 1 FROM profiles p
    JOIN roles r ON p.role_id = r.id
    WHERE p.id = auth.uid() AND (r.name = 'admin' OR r.name = 'مشرف')
  )
);
