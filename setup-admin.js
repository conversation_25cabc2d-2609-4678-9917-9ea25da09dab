// هذا الملف لإعداد المستخدم الأول (المشرف) في النظام
// يجب تشغيله مرة واحدة فقط عند بدء استخدام النظام

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';

// تحميل متغيرات البيئة
dotenv.config();

const supabaseUrl = process.env.PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('يرجى التأكد من وجود متغيرات البيئة PUBLIC_SUPABASE_URL و PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// بيانات المستخدم المشرف
const adminUser = {
  email: '<EMAIL>',
  password: 'Admin123!',
  full_name: 'مدير النظام',
  role: 'مشرف'
};

async function setupAdmin() {
  try {
    console.log('جاري إنشاء المستخدم المشرف...');
    
    // إنشاء المستخدم في نظام المصادقة
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: adminUser.email,
      password: adminUser.password,
      email_confirm: true
    });
    
    if (authError) {
      throw new Error(`خطأ في إنشاء المستخدم: ${authError.message}`);
    }
    
    console.log('تم إنشاء المستخدم بنجاح في نظام المصادقة');
    
    // إنشاء ملف تعريف المستخدم
    const { error: profileError } = await supabase
      .from('profiles')
      .insert([
        {
          id: authData.user.id,
          full_name: adminUser.full_name,
          email: adminUser.email,
          role: adminUser.role
        }
      ]);
    
    if (profileError) {
      throw new Error(`خطأ في إنشاء ملف تعريف المستخدم: ${profileError.message}`);
    }
    
    console.log('تم إنشاء ملف تعريف المستخدم بنجاح');
    console.log('-----------------------------------');
    console.log('بيانات تسجيل الدخول:');
    console.log(`البريد الإلكتروني: ${adminUser.email}`);
    console.log(`كلمة المرور: ${adminUser.password}`);
    console.log('-----------------------------------');
    console.log('تم إعداد المستخدم المشرف بنجاح!');
    
  } catch (error) {
    console.error('حدث خطأ أثناء إعداد المستخدم المشرف:');
    console.error(error.message);
  }
}

// تنفيذ الإعداد
setupAdmin();
