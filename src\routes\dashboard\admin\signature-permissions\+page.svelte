<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';

  // تعريف أنواع البيانات
  interface User {
    id: string;
    full_name: string;
    email: string;
    role: string;
    unit_id: string | null;
    unit_name?: string;
    can_sign?: boolean;
  }

  // متغيرات الحالة
  let users: User[] = [];
  let loading = true;
  let error: string | null = null;
  let currentUser: any = null;
  let isAdmin = false;
  let searchQuery = '';
  let filteredUsers: User[] = [];
  let savingPermissions: Record<string, boolean> = {};

  // تحميل البيانات عند تحميل الصفحة
  onMount(async () => {
    try {
      // التحقق من المستخدم الحالي وصلاحياته
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        goto('/login');
        return;
      }

      currentUser = user;

      // التحقق من صلاحيات المستخدم
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile || (profile.role !== 'admin' && profile.role !== 'مشرف')) {
        goto('/dashboard');
        return;
      }

      isAdmin = true;
      await loadUsers();
    } catch (err) {
      console.error('Error in onMount:', err);
      error = 'حدث خطأ أثناء تحميل البيانات';
    } finally {
      loading = false;
    }
  });

  // تحميل قائمة المستخدمين مع صلاحيات التوقيع
  async function loadUsers() {
    try {
      loading = true;
      error = null;

      // جلب جميع المستخدمين
      const { data: usersData, error: usersError } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          role,
          unit_id,
          units:unit_id (name)
        `)
        .order('full_name');

      if (usersError) {
        throw usersError;
      }

      // جلب صلاحيات التوقيع
      const { data: permissionsData, error: permissionsError } = await supabase
        .from('signature_permissions')
        .select('user_id, can_sign');

      if (permissionsError) {
        throw permissionsError;
      }

      // دمج البيانات
      const permissionsMap = new Map();
      permissionsData?.forEach(permission => {
        permissionsMap.set(permission.user_id, permission.can_sign);
      });

      users = usersData?.map(user => ({
        id: user.id,
        full_name: user.full_name || 'بدون اسم',
        email: user.email || 'بدون بريد إلكتروني',
        role: user.role || 'مستخدم',
        unit_id: user.unit_id,
        unit_name: user.units?.name || 'غير محدد',
        can_sign: permissionsMap.get(user.id) || false
      })) || [];

      // تطبيق البحث الأولي
      applySearch();
    } catch (err: any) {
      console.error('Error loading users:', err);
      error = err.message || 'حدث خطأ أثناء تحميل بيانات المستخدمين';
    } finally {
      loading = false;
    }
  }

  // تطبيق البحث على قائمة المستخدمين
  function applySearch() {
    if (!searchQuery.trim()) {
      filteredUsers = [...users];
      return;
    }

    const query = searchQuery.toLowerCase();
    filteredUsers = users.filter(user => 
      user.full_name.toLowerCase().includes(query) || 
      user.email.toLowerCase().includes(query) ||
      user.role.toLowerCase().includes(query) ||
      user.unit_name?.toLowerCase().includes(query)
    );
  }

  // تحديث البحث عند تغيير قيمة البحث
  $: {
    if (users.length > 0) {
      applySearch();
    }
  }

  // تغيير صلاحية التوقيع للمستخدم
  async function toggleSignPermission(userId: string, canSign: boolean) {
    if (!currentUser || !isAdmin) return;

    try {
      savingPermissions[userId] = true;
      
      // استدعاء وظيفة منح أو إلغاء الصلاحية
      const functionName = canSign ? 'grant_signature_permission' : 'revoke_signature_permission';
      
      const { data, error: rpcError } = await supabase.rpc(
        functionName,
        { 
          p_admin_id: currentUser.id,
          p_user_id: userId
        }
      );

      if (rpcError) {
        throw rpcError;
      }

      // تحديث حالة المستخدم في القائمة
      users = users.map(user => 
        user.id === userId ? { ...user, can_sign: canSign } : user
      );
      
      // تحديث القائمة المفلترة أيضاً
      applySearch();
    } catch (err: any) {
      console.error(`Error ${canSign ? 'granting' : 'revoking'} signature permission:`, err);
      alert(`حدث خطأ أثناء ${canSign ? 'منح' : 'إلغاء'} صلاحية التوقيع: ${err.message || 'خطأ غير معروف'}`);
      
      // إعادة تحميل البيانات في حالة الخطأ
      await loadUsers();
    } finally {
      savingPermissions[userId] = false;
    }
  }
</script>

<div class="container mx-auto p-4">
  <div class="mb-6 flex justify-between items-center">
    <h1 class="text-2xl font-bold">إدارة صلاحيات التوقيع الإلكتروني</h1>
    
    <a
      href="/dashboard/admin"
      class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="m15 18-6-6 6-6"/></svg>
      العودة إلى لوحة التحكم
    </a>
  </div>

  <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-6 border-b">
      <p class="text-gray-600 mb-4">
        يمكنك من خلال هذه الصفحة إدارة صلاحيات التوقيع الإلكتروني للمستخدمين. المستخدمون الذين لديهم صلاحية التوقيع يمكنهم إنشاء رسائل موقعة إلكترونياً.
      </p>
      
      <div class="relative mb-4">
        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>
        </div>
        <input
          type="text"
          bind:value={searchQuery}
          placeholder="البحث عن مستخدم..."
          class="block w-full p-2 pr-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
    </div>

    {#if loading}
      <div class="flex justify-center items-center p-8">
        <p class="text-gray-500">جاري تحميل البيانات...</p>
      </div>
    {:else if error}
      <div class="p-4 bg-red-50 text-red-600 border-t border-red-200">
        <p>{error}</p>
      </div>
    {:else if filteredUsers.length === 0}
      <div class="p-8 text-center text-gray-500">
        <p>لا توجد نتائج مطابقة للبحث</p>
      </div>
    {:else}
      <div class="overflow-x-auto">
        <table class="w-full text-sm text-right">
          <thead class="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
              <th class="px-6 py-3">الاسم</th>
              <th class="px-6 py-3">البريد الإلكتروني</th>
              <th class="px-6 py-3">الدور</th>
              <th class="px-6 py-3">الوحدة التنظيمية</th>
              <th class="px-6 py-3">صلاحية التوقيع</th>
            </tr>
          </thead>
          <tbody>
            {#each filteredUsers as user}
              <tr class="bg-white border-b hover:bg-gray-50">
                <td class="px-6 py-4 font-medium text-gray-900">{user.full_name}</td>
                <td class="px-6 py-4">{user.email}</td>
                <td class="px-6 py-4">
                  <span class="px-2 py-1 text-xs rounded-full {user.role === 'admin' || user.role === 'مشرف' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}">
                    {user.role}
                  </span>
                </td>
                <td class="px-6 py-4">{user.unit_name}</td>
                <td class="px-6 py-4">
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      class="sr-only peer"
                      checked={user.can_sign}
                      disabled={savingPermissions[user.id] || user.role === 'admin' || user.role === 'مشرف'}
                      on:change={() => toggleSignPermission(user.id, !user.can_sign)}
                    />
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:right-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    <span class="mr-3 text-sm font-medium text-gray-900">
                      {#if savingPermissions[user.id]}
                        <svg class="animate-spin h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      {:else if user.role === 'admin' || user.role === 'مشرف'}
                        <span class="text-green-600">صلاحية تلقائية</span>
                      {:else}
                        {user.can_sign ? 'مفعل' : 'غير مفعل'}
                      {/if}
                    </span>
                  </label>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    {/if}
  </div>
</div>
