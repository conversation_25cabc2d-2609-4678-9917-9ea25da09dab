# إصلاح مشكلة جدول المراسلات

تم إصلاح مشكلة "column 'receiver_id' referenced in foreign key constraint does not exist" التي تظهر عند تنفيذ ملف `fix_message_relationships.sql`.

## المشكلة

عند تنفيذ ملف `fix_message_relationships.sql` في قاعدة البيانات Supabase، ظهر الخطأ التالي:

```
ERROR:  42703: column "receiver_id" referenced in foreign key constraint does not exist
CONTEXT:  SQL statement "ALTER TABLE messages 
    ADD CONSTRAINT messages_receiver_id_fkey 
    FOREIGN KEY (receiver_id) 
    REFERENCES profiles(id) 
    ON DELETE SET NULL"
PL/pgSQL function fix_message_relationships() line 19 at SQL statement
```

هذا الخطأ يشير إلى أن عمود `receiver_id` غير موجود في جدول `messages`.

## الحل

تم تحسين ملف `fix_message_relationships.sql` ليكون أكثر مرونة ويتعامل مع الحالات التالية:

1. التحقق من وجود جدول `messages` وإنشائه إذا لم يكن موجوداً
2. التحقق من وجود الأعمدة المطلوبة وإنشائها إذا لم تكن موجودة
3. إعادة تعريف العلاقات فقط للأعمدة الموجودة
4. إنشاء فهارس للبحث السريع فقط للأعمدة الموجودة
5. إضافة وظائف للتحقق من بنية الجدول وإصلاح المشاكل

## التغييرات التي تم تنفيذها:

### 1. إضافة وظيفة `ensure_messages_table_exists`

تم إضافة وظيفة للتحقق من وجود جدول `messages` وإنشائه إذا لم يكن موجوداً:

```sql
CREATE OR REPLACE FUNCTION ensure_messages_table_exists()
RETURNS VOID AS $$
DECLARE
  table_exists BOOLEAN;
BEGIN
  -- التحقق من وجود جدول المراسلات
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.tables
    WHERE table_name = 'messages'
  ) INTO table_exists;
  
  -- إذا لم يكن الجدول موجوداً، قم بإنشائه
  IF NOT table_exists THEN
    RAISE NOTICE 'Table messages does not exist. Creating it...';
    
    CREATE TABLE messages (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      subject TEXT NOT NULL,
      content TEXT NOT NULL,
      sender_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
      receiver_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
      receiver_unit_id UUID REFERENCES units(id) ON DELETE SET NULL,
      document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
      status TEXT NOT NULL DEFAULT 'sent',
      parent_id UUID REFERENCES messages(id) ON DELETE SET NULL,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      CHECK (receiver_id IS NOT NULL OR receiver_unit_id IS NOT NULL)
    );
    
    -- إنشاء فهارس للبحث السريع
    CREATE INDEX idx_messages_sender_id ON messages(sender_id);
    CREATE INDEX idx_messages_receiver_id ON messages(receiver_id);
    CREATE INDEX idx_messages_receiver_unit_id ON messages(receiver_unit_id);
  END IF;
END;
$$ LANGUAGE plpgsql;
```

### 2. تحسين وظيفة `fix_message_relationships`

تم تحسين وظيفة `fix_message_relationships` للتحقق من وجود الأعمدة قبل محاولة إنشاء العلاقات:

```sql
CREATE OR REPLACE FUNCTION fix_message_relationships()
RETURNS VOID AS $$
DECLARE
  column_exists BOOLEAN;
  table_exists BOOLEAN;
BEGIN
  -- التحقق من وجود جدول المراسلات
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.tables
    WHERE table_name = 'messages'
  ) INTO table_exists;
  
  -- إذا لم يكن الجدول موجوداً، قم بإنشائه
  IF NOT table_exists THEN
    PERFORM ensure_messages_table_exists();
    RETURN;
  END IF;
  
  -- التحقق من وجود الأعمدة قبل إنشاء العلاقات
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'messages'
    AND column_name = 'receiver_id'
  ) INTO column_exists;
  
  -- إذا لم يكن العمود موجوداً، قم بإنشائه
  IF NOT column_exists THEN
    RAISE NOTICE 'Column receiver_id does not exist in messages table. Adding it...';
    ALTER TABLE messages ADD COLUMN receiver_id UUID REFERENCES profiles(id) ON DELETE SET NULL;
  ELSE
    -- إزالة العلاقات الحالية فقط إذا كان العمود موجوداً
    ALTER TABLE IF EXISTS messages DROP CONSTRAINT IF EXISTS messages_receiver_id_fkey;
    
    -- إعادة إنشاء العلاقة
    ALTER TABLE messages 
      ADD CONSTRAINT messages_receiver_id_fkey 
      FOREIGN KEY (receiver_id) 
      REFERENCES profiles(id) 
      ON DELETE SET NULL;
  END IF;
  
  -- ... (نفس الشيء للأعمدة الأخرى)
END;
$$ LANGUAGE plpgsql;
```

### 3. إضافة وظيفة `check_messages_table_structure`

تم إضافة وظيفة للتحقق من بنية جدول المراسلات:

```sql
CREATE OR REPLACE FUNCTION check_messages_table_structure()
RETURNS TABLE (
  column_name TEXT,
  data_type TEXT,
  is_nullable TEXT,
  column_default TEXT,
  constraint_name TEXT,
  constraint_type TEXT,
  foreign_table TEXT,
  foreign_column TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.column_name::TEXT,
    c.data_type::TEXT,
    c.is_nullable::TEXT,
    c.column_default::TEXT,
    tc.constraint_name::TEXT,
    tc.constraint_type::TEXT,
    ccu.table_name::TEXT AS foreign_table,
    ccu.column_name::TEXT AS foreign_column
  FROM 
    information_schema.columns c
    LEFT JOIN information_schema.key_column_usage kcu
      ON c.table_name = kcu.table_name AND c.column_name = kcu.column_name
    LEFT JOIN information_schema.table_constraints tc
      ON kcu.constraint_name = tc.constraint_name
    LEFT JOIN information_schema.constraint_column_usage ccu
      ON tc.constraint_name = ccu.constraint_name
  WHERE 
    c.table_name = 'messages'
  ORDER BY 
    c.ordinal_position;
END;
$$ LANGUAGE plpgsql;
```

## كيفية تطبيق الحل:

1. قم بتنفيذ ملف `src\lib\db\fix_message_relationships.sql` في قاعدة البيانات Supabase باستخدام SQL Editor
2. للتحقق من بنية جدول المراسلات، قم بتنفيذ الأمر التالي:

```sql
SELECT * FROM check_messages_table_structure();
```

3. لتحديث ذاكرة التخزين المؤقت للمخطط، قم بتنفيذ الأمر التالي:

```sql
SELECT refresh_schema_cache();
```

## ملاحظات هامة:

1. هذا الحل يتعامل مع الحالات التالية:
   - عدم وجود جدول المراسلات
   - عدم وجود الأعمدة المطلوبة
   - عدم وجود العلاقات المطلوبة
   - عدم وجود الفهارس المطلوبة

2. يمكن استخدام هذا النهج لإصلاح مشاكل مماثلة في جداول أخرى

3. إذا استمرت المشكلة، يمكنك تنفيذ الأمر التالي للحصول على معلومات أكثر تفصيلاً:

```sql
SELECT * FROM check_messages_table_structure();
```
