import { supabase } from '$lib/supabase';

/**
 * أنواع الأدوار المتاحة في النظام
 */
export const RoleType = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  USER: 'user'
};

/**
 * الموارد المتاحة في النظام
 */
export const Resource = {
  DOCUMENTS: 'documents',
  USERS: 'users',
  MESSAGES: 'messages',
  BROADCASTS: 'broadcasts',
  ORGANIZATION: 'organization',
  SYSTEM: 'system'
};

/**
 * الإجراءات المتاحة على الموارد
 */
export const Action = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  SETTINGS: 'settings',
  LOGS: 'logs'
};

/**
 * خدمة التحقق من الصلاحيات
 */
export class PermissionService {
  /**
   * التحقق من دور المستخدم
   * @param {string} userId - معرف المستخدم
   * @param {string} role - الدور المطلوب التحقق منه
   * @returns {Promise<boolean>} - يعيد true إذا كان المستخدم يملك الدور المطلوب
   */
  static async checkRole(userId, role) {
    try {
      // جلب دور المستخدم من قاعدة البيانات
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          role,
          roles:role_id (
            name
          )
        `)
        .eq('id', userId)
        .single();

      if (error) throw error;

      // التحقق من الدور باستخدام عمود role أو role_id
      return (data && (data.role === role || (data.roles && data.roles.name === role)));
    } catch (error) {
      console.error('Error checking role:', error);
      return false;
    }
  }

  /**
   * التحقق من صلاحية المستخدم على مورد معين
   * @param {string} userId - معرف المستخدم
   * @param {string} resource - المورد المطلوب التحقق منه
   * @param {string} action - الإجراء المطلوب التحقق منه
   * @returns {Promise<boolean>} - يعيد true إذا كان المستخدم يملك الصلاحية المطلوبة
   */
  static async checkPermission(userId, resource, action) {
    try {
      // التحقق من دور المستخدم أولاً
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('role_id')
        .eq('id', userId)
        .single();

      if (userError) throw userError;

      if (!userData || !userData.role_id) return false;

      // التحقق من وجود الصلاحية لهذا الدور
      const { data: permData, error: permError } = await supabase
        .from('permissions')
        .select('id')
        .eq('resource', resource)
        .eq('action', action)
        .single();

      if (permError) throw permError;

      if (!permData) return false;

      // التحقق من وجود علاقة بين الدور والصلاحية
      const { data: rolePermData, error: rolePermError } = await supabase
        .from('role_permissions')
        .select('id')
        .eq('role_id', userData.role_id)
        .eq('permission_id', permData.id)
        .single();

      if (rolePermError && rolePermError.code !== 'PGRST116') throw rolePermError;

      return !!rolePermData;
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * جلب جميع صلاحيات المستخدم
   * @param {string} userId - معرف المستخدم
   * @returns {Promise<Array>} - يعيد مصفوفة من الصلاحيات
   */
  static async getUserPermissions(userId) {
    try {
      // جلب دور المستخدم
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('role_id')
        .eq('id', userId)
        .single();

      if (userError) throw userError;

      if (!userData || !userData.role_id) return [];

      // جلب صلاحيات الدور
      const { data: permData, error: permError } = await supabase
        .from('role_permissions')
        .select(`
          permission_id,
          permissions:permission_id (
            name,
            description,
            resource,
            action
          )
        `)
        .eq('role_id', userData.role_id);

      if (permError) throw permError;

      return permData.map(item => item.permissions);
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }

  /**
   * جلب جميع الأدوار المتاحة في النظام
   * @returns {Promise<Array>} - يعيد مصفوفة من الأدوار
   */
  static async getAllRoles() {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('name');

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error getting all roles:', error);
      return [];
    }
  }

  /**
   * تعيين دور للمستخدم
   * @param {string} userId - معرف المستخدم
   * @param {string} roleId - معرف الدور
   * @returns {Promise<boolean>} - يعيد true إذا تم تعيين الدور بنجاح
   */
  static async assignRoleToUser(userId, roleId) {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ role_id: roleId })
        .eq('id', userId);

      if (error) throw error;

      return true;
    } catch (error) {
      console.error('Error assigning role to user:', error);
      return false;
    }
  }
}

export default PermissionService;
