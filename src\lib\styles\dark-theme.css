/* ملف أنماط الوضع المظلم */

:root.dark {
  --background-color: #111827;
  --text-color: #f3f4f6;
  --card-background: #1f2937;
  --border-color: #374151;
  --muted-color: #9ca3af;
  --primary-color: #60a5fa;
  --hover-color: #1f2937;
  --input-background: #1f2937;
}

/* فئات مخصصة للوضع المظلم */
.dark-bg {
  background-color: #111827 !important;
  color: #f3f4f6 !important;
}

.dark-card {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
}

.dark {
  background-color: var(--background-color);
  color: var(--text-color);
}

.dark body {
  background-color: var(--background-color);
  color: var(--text-color);
}

/* تطبيق الوضع المظلم على العناصر الرئيسية */
.dark .bg-card {
  background-color: var(--card-background);
}

.dark .bg-white {
  background-color: var(--background-color);
}

.dark .text-gray-900 {
  color: var(--text-color);
}

.dark .border-gray-200 {
  border-color: var(--border-color);
}

.dark .text-muted-foreground {
  color: var(--muted-color);
}

/* تطبيق الوضع المظلم على العناصر الأخرى */
.dark .hover\:bg-gray-100:hover {
  background-color: var(--hover-color);
}

.dark .bg-muted\/50 {
  background-color: rgba(31, 41, 55, 0.5);
}

.dark .bg-primary\/10 {
  background-color: rgba(59, 130, 246, 0.1);
}

.dark .bg-primary\/5 {
  background-color: rgba(59, 130, 246, 0.05);
}

.dark .border-primary\/20 {
  border-color: rgba(59, 130, 246, 0.2);
}

.dark .bg-blue-100 {
  background-color: rgba(59, 130, 246, 0.2);
}

.dark .bg-green-100 {
  background-color: rgba(16, 185, 129, 0.2);
}

.dark .bg-amber-100 {
  background-color: rgba(245, 158, 11, 0.2);
}

.dark .bg-purple-100 {
  background-color: rgba(139, 92, 246, 0.2);
}

.dark .text-blue-700 {
  color: #60a5fa;
}

.dark .text-green-700 {
  color: #34d399;
}

.dark .text-amber-700 {
  color: #fbbf24;
}

.dark .text-purple-700 {
  color: #a78bfa;
}

.dark .text-primary {
  color: var(--primary-color);
}

/* تطبيق الوضع المظلم على النماذج */
.dark input,
.dark textarea,
.dark select {
  background-color: var(--input-background);
  border-color: var(--border-color);
  color: var(--text-color);
}

.dark button,
.dark .btn {
  border-color: var(--border-color);
}

.dark .bg-destructive\/10 {
  background-color: rgba(239, 68, 68, 0.1);
}

.dark .border-destructive\/30 {
  border-color: rgba(239, 68, 68, 0.3);
}

.dark .text-destructive {
  color: #f87171;
}
