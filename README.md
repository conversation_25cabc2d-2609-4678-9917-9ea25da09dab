# نظام الأرشفة الإلكترونية

نظام متكامل لإدارة المستندات والمراسلات الداخلية مبني باستخدام SvelteKit وSupabase.

## المميزات

- واجهة مستخدم عربية بدعم RTL
- نظام تسجيل دخول متكامل
- إدارة المستندات والأرشفة الإلكترونية
- نظام مراسلات داخلية
- إدارة الهيكل التنظيمي
- إدارة المستخدمين
- نظام صلاحيات متكامل

## متطلبات النظام

- Node.js (الإصدار 18 أو أحدث)
- حساب Supabase

## خطوات الإعداد

### 1. إعداد قاعدة البيانات Supabase

1. قم بإنشاء مشروع جديد في [Supabase](https://supabase.com)
2. انتقل إلى صفحة SQL Editor
3. قم بنسخ محتوى ملف `setup-db.sql` وتنفيذه في محرر SQL
4. انتقل إلى Storage وقم بإنشاء مجلد جديد باسم `documents` مع السياسات التالية:
   - القراءة: للجميع
   - الكتابة: للمستخدمين المسجلين فقط

### 2. إعداد ملف البيئة

قم بإنشاء ملف `.env` في المجلد الرئيسي للمشروع وأضف المتغيرات التالية:

```
PUBLIC_SUPABASE_URL=https://your-project-url.supabase.co
PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

استبدل القيم بالقيم الخاصة بمشروعك في Supabase.

### 3. تثبيت التبعيات

```bash
npm install
```

### 4. إعداد المستخدم المشرف

قم بتعديل بيانات المستخدم المشرف في ملف `setup-admin.js` ثم قم بتشغيله:

```bash
node setup-admin.js
```

### 5. تشغيل التطبيق

```bash
npm run dev
```

سيتم تشغيل التطبيق على المنفذ 5173 (أو منفذ آخر إذا كان هذا المنفذ مشغولاً).

## هيكل المشروع

- `src/routes`: صفحات التطبيق
  - `/`: الصفحة الرئيسية
  - `/login`: صفحة تسجيل الدخول
  - `/dashboard`: لوحة التحكم
    - `/dashboard/documents`: إدارة المستندات
    - `/dashboard/messages`: المراسلات الداخلية
    - `/dashboard/organization`: الهيكل التنظيمي
    - `/dashboard/users`: إدارة المستخدمين
- `src/lib`: المكونات والوظائف المشتركة
  - `supabase.ts`: اتصال Supabase

## الاستخدام

1. قم بتسجيل الدخول باستخدام بيانات المستخدم المشرف
2. قم بإنشاء الهيكل التنظيمي للمؤسسة
3. قم بإضافة المستخدمين وتعيينهم للوحدات التنظيمية
4. ابدأ باستخدام النظام لإدارة المستندات والمراسلات

## الصلاحيات

- **مشرف**: يمتلك جميع الصلاحيات في النظام
- **مدير قسم**: يمكنه إدارة المستندات والمراسلات الخاصة بقسمه
- **موظف**: يمكنه عرض المستندات وإرسال المراسلات

## التخصيص

يمكن تخصيص النظام حسب احتياجات المؤسسة من خلال:

1. تعديل أنواع المستندات في ملف `src/routes/dashboard/documents/+page.svelte`
2. تعديل أنواع الوحدات التنظيمية في ملف `src/routes/dashboard/organization/+page.svelte`
3. تعديل أدوار المستخدمين في ملف `src/routes/dashboard/users/+page.svelte`

## المساهمة

نرحب بالمساهمات لتحسين النظام. يرجى إنشاء fork للمشروع وتقديم طلب سحب مع التغييرات المقترحة.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
