-- إنشاء سياسات الوصول (RLS Policies)

-- تفعيل RLS على الجداول
ALTER TABLE public.official_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_settings ENABLE ROW LEVEL SECURITY;

-- سياسات الوصول للوثائق الرسمية

-- سياسة القراءة للوثائق الرسمية
CREATE POLICY "Users can view their own documents"
  ON public.official_documents
  FOR SELECT
  USING (
    auth.uid() = creator_id
    OR auth.uid() = recipient_id
    OR EXISTS (
      SELECT 1 FROM public.document_shares
      WHERE document_id = official_documents.id
      AND shared_with = auth.uid()
      AND is_active = true
      AND (expires_at IS NULL OR expires_at > NOW())
    )
    OR EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
      AND (
        role = 'admin'
        OR (
          unit_id = official_documents.unit_id
          AND role IN ('manager', 'supervisor')
        )
      )
    )
  );

-- سياسة الإنشاء للوثائق الرسمية
CREATE POLICY "Users can create documents if authorized"
  ON public.official_documents
  FOR INSERT
  WITH CHECK (
    auth.uid() = creator_id
    AND public.can_create_official_document(auth.uid())
  );

-- سياسة التعديل للوثائق الرسمية
CREATE POLICY "Users can update their own documents"
  ON public.official_documents
  FOR UPDATE
  USING (
    auth.uid() = creator_id
    AND status IN ('draft', 'pending')
  )
  WITH CHECK (
    auth.uid() = creator_id
    AND status IN ('draft', 'pending')
  );

-- سياسة التعديل للمشرفين
CREATE POLICY "Supervisors can update documents from their unit"
  ON public.official_documents
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
      AND (
        role = 'admin'
        OR (
          unit_id = official_documents.unit_id
          AND role IN ('manager', 'supervisor')
        )
      )
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
      AND (
        role = 'admin'
        OR (
          unit_id = official_documents.unit_id
          AND role IN ('manager', 'supervisor')
        )
      )
    )
  );

-- سياسة الحذف للوثائق الرسمية
CREATE POLICY "Users can delete their own draft documents"
  ON public.official_documents
  FOR DELETE
  USING (
    auth.uid() = creator_id
    AND status = 'draft'
  );

-- سياسة الحذف للمشرفين
CREATE POLICY "Admins can delete any document"
  ON public.official_documents
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- سياسات الوصول لتاريخ الوثائق

-- سياسة القراءة لتاريخ الوثائق
CREATE POLICY "Users can view history of their documents"
  ON public.document_history
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.official_documents
      WHERE id = document_history.document_id
      AND (
        creator_id = auth.uid()
        OR recipient_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM public.document_shares
          WHERE document_id = official_documents.id
          AND shared_with = auth.uid()
          AND is_active = true
          AND (expires_at IS NULL OR expires_at > NOW())
        )
        OR EXISTS (
          SELECT 1 FROM public.profiles
          WHERE id = auth.uid()
          AND (
            role = 'admin'
            OR (
              unit_id = official_documents.unit_id
              AND role IN ('manager', 'supervisor')
            )
          )
        )
      )
    )
  );

-- سياسة الإنشاء لتاريخ الوثائق (تتم تلقائياً عبر المشغل)

-- سياسات الوصول للتعليقات

-- سياسة القراءة للتعليقات
CREATE POLICY "Users can view comments on their documents"
  ON public.document_comments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.official_documents
      WHERE id = document_comments.document_id
      AND (
        creator_id = auth.uid()
        OR recipient_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM public.document_shares
          WHERE document_id = official_documents.id
          AND shared_with = auth.uid()
          AND is_active = true
          AND (expires_at IS NULL OR expires_at > NOW())
        )
        OR EXISTS (
          SELECT 1 FROM public.profiles
          WHERE id = auth.uid()
          AND (
            role = 'admin'
            OR (
              unit_id = official_documents.unit_id
              AND role IN ('manager', 'supervisor')
            )
          )
        )
      )
    )
  );

-- سياسة الإنشاء للتعليقات
CREATE POLICY "Users can comment on documents they can view"
  ON public.document_comments
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id
    AND EXISTS (
      SELECT 1 FROM public.official_documents
      WHERE id = document_comments.document_id
      AND (
        creator_id = auth.uid()
        OR recipient_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM public.document_shares
          WHERE document_id = official_documents.id
          AND shared_with = auth.uid()
          AND is_active = true
          AND (expires_at IS NULL OR expires_at > NOW())
        )
      )
    )
  );

-- سياسة التعديل للتعليقات
CREATE POLICY "Users can update their own comments"
  ON public.document_comments
  FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- سياسة الحذف للتعليقات
CREATE POLICY "Users can delete their own comments"
  ON public.document_comments
  FOR DELETE
  USING (auth.uid() = user_id);

-- سياسات الوصول لمشاركة الوثائق

-- سياسة القراءة لمشاركة الوثائق
CREATE POLICY "Users can view shares of their documents"
  ON public.document_shares
  FOR SELECT
  USING (
    shared_by = auth.uid()
    OR shared_with = auth.uid()
    OR EXISTS (
      SELECT 1 FROM public.official_documents
      WHERE id = document_shares.document_id
      AND creator_id = auth.uid()
    )
    OR EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );

-- سياسة الإنشاء لمشاركة الوثائق
CREATE POLICY "Users can share their own documents"
  ON public.document_shares
  FOR INSERT
  WITH CHECK (
    auth.uid() = shared_by
    AND EXISTS (
      SELECT 1 FROM public.official_documents
      WHERE id = document_shares.document_id
      AND (
        creator_id = auth.uid()
        OR EXISTS (
          SELECT 1 FROM public.profiles
          WHERE id = auth.uid()
          AND (
            role = 'admin'
            OR (
              unit_id = official_documents.unit_id
              AND role IN ('manager', 'supervisor')
            )
          )
        )
      )
    )
  );

-- سياسة التعديل لمشاركة الوثائق
CREATE POLICY "Users can update their own shares"
  ON public.document_shares
  FOR UPDATE
  USING (shared_by = auth.uid())
  WITH CHECK (shared_by = auth.uid());

-- سياسة الحذف لمشاركة الوثائق
CREATE POLICY "Users can delete their own shares"
  ON public.document_shares
  FOR DELETE
  USING (shared_by = auth.uid());

-- سياسات الوصول لقوالب الوثائق

-- سياسة القراءة لقوالب الوثائق
CREATE POLICY "Users can view public templates and their own"
  ON public.document_templates
  FOR SELECT
  USING (
    is_public = true
    OR creator_id = auth.uid()
    OR EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
      AND (
        role = 'admin'
        OR (
          unit_id = document_templates.unit_id
          AND role IN ('manager', 'supervisor')
        )
      )
    )
  );

-- سياسة الإنشاء لقوالب الوثائق
CREATE POLICY "Users can create templates"
  ON public.document_templates
  FOR INSERT
  WITH CHECK (
    auth.uid() = creator_id
    AND public.can_create_official_document(auth.uid())
  );

-- سياسة التعديل لقوالب الوثائق
CREATE POLICY "Users can update their own templates"
  ON public.document_templates
  FOR UPDATE
  USING (creator_id = auth.uid())
  WITH CHECK (creator_id = auth.uid());

-- سياسة الحذف لقوالب الوثائق
CREATE POLICY "Users can delete their own templates"
  ON public.document_templates
  FOR DELETE
  USING (creator_id = auth.uid());

-- سياسات الوصول لإعدادات المنظمة

-- سياسة القراءة لإعدادات المنظمة
CREATE POLICY "All users can view organization settings"
  ON public.organization_settings
  FOR SELECT
  USING (true);

-- سياسة التعديل لإعدادات المنظمة
CREATE POLICY "Only admins can update organization settings"
  ON public.organization_settings
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
      AND role = 'admin'
    )
  );
