<script lang="ts">
  import { onMount } from 'svelte';
  import { isDarkMode, toggleTheme, initTheme } from '$lib/stores/theme';

  // تهيئة الوضع المظلم عند تحميل المكون
  onMount(() => {
    initTheme();

    // إضافة مستمع للتغييرات في الوضع المظلم
    const darkModeObserver = isDarkMode.subscribe(value => {
      if (value) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      }
    });

    return () => {
      darkModeObserver();
    };
  });

  function handleToggle() {
    toggleTheme();

    // تطبيق الوضع المظلم مباشرة
    if (!$isDarkMode) {
      document.documentElement.classList.add('dark');
      document.body.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('dark');
    }
  }
</script>

<button
  type="button"
  class="inline-flex items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:text-gray-200 dark:hover:bg-gray-800"
  on:click={handleToggle}
  aria-label={$isDarkMode ? 'تفعيل الوضع الفاتح' : 'تفعيل الوضع المظلم'}
>
  <span class="sr-only">{$isDarkMode ? 'تفعيل الوضع الفاتح' : 'تفعيل الوضع المظلم'}</span>
  <div class="relative w-6 h-6 transition-all duration-300">
    {#if $isDarkMode}
      <!-- Sun icon -->
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute inset-0 transition-opacity duration-300 opacity-100">
        <circle cx="12" cy="12" r="5"/>
        <line x1="12" y1="1" x2="12" y2="3"/>
        <line x1="12" y1="21" x2="12" y2="23"/>
        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
        <line x1="1" y1="12" x2="3" y2="12"/>
        <line x1="21" y1="12" x2="23" y2="12"/>
        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
      </svg>
    {:else}
      <!-- Moon icon -->
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute inset-0 transition-opacity duration-300 opacity-100">
        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
      </svg>
    {/if}
  </div>
</button>
