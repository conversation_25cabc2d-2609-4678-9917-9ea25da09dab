<script lang="ts">
  import { onMount } from 'svelte';
  import { getSignatureInfo } from '$lib/utils/signatureUtils';

  // المدخلات
  export let subject: string = '';
  export let content: string = '';
  export let unitName: string = '';
  export let stateName: string = 'دولة ليبيا';  // اسم الدولة (يمكن تغييره)
  export let logoText: string = '';  // نص الشعار (وزارة العدل سابقاً)
  export let unitLogo: string | null = null;
  export let organizationLogo: string | null = null;
  export let stamp: string | null = null;
  export let signature: any = null;
  export let sender: any = null;
  export let printMode: boolean = false;
  export let referenceNumber: string = '';
  export let date: string = '';

  // حالة المكون
  let mounted = false;
  let logoError = false;
  let organizationLogoError = false;
  let stampError = false;

  // معلومات التوقيع الإلكتروني
  let signatureInfo: any = null;
  let hasElectronicSignature = false;

  // تحويل روابط صور Google Drive إلى روابط مباشرة
  function convertGoogleDriveUrl(url: string): string {
    if (!url) return '';

    // التحقق مما إذا كان الرابط من Google Drive
    if (url.includes('drive.google.com')) {
      // استخراج معرف الملف
      const fileIdMatch = url.match(/[-\w]{25,}/);
      if (fileIdMatch && fileIdMatch[0]) {
        return `https://drive.google.com/uc?export=view&id=${fileIdMatch[0]}`;
      }
    }

    return url;
  }

  // تنسيق التاريخ بالعربية
  function formatDate(dateString: string): string {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  }

  // معالجة التوقيع الإلكتروني
  function processElectronicSignature() {
    if (signature && (signature.signatureMethod === 'HASH-SHA256' || signature.contentHash)) {
      hasElectronicSignature = true;
      signatureInfo = getSignatureInfo(signature);
    } else {
      hasElectronicSignature = false;
      signatureInfo = null;
    }
  }

  // تحميل الصور عند تركيب المكون
  onMount(() => {
    mounted = true;
    processElectronicSignature();
  });

  // تحويل روابط الصور
  $: processedUnitLogo = convertGoogleDriveUrl(unitLogo || '');
  $: processedOrganizationLogo = convertGoogleDriveUrl(organizationLogo || '');
  $: processedStamp = convertGoogleDriveUrl(stamp || '');

  // تنسيق التاريخ
  $: formattedDate = formatDate(date);

  // معالجة التوقيع عند تغييره
  $: if (signature) {
    processElectronicSignature();
  }
</script>

<div class="official-document {printMode ? 'print-mode' : ''}" dir="rtl">
  <!-- رأس الوثيقة -->
  <div class="document-header">
    <div class="logo right-logo">
      {#if processedUnitLogo && mounted}
        <img
          src={processedUnitLogo}
          alt="شعار الوحدة"
          on:error={() => logoError = true}
          class:hidden={logoError}
        />
      {/if}
    </div>

    <div class="header-text">
      <h1>{stateName}</h1>
      <h2>{logoText || unitName || ''}</h2>
    </div>

    <div class="logo left-logo">
      {#if processedOrganizationLogo && mounted}
        <img
          src={processedOrganizationLogo}
          alt="شعار المؤسسة"
          on:error={() => organizationLogoError = true}
          class:hidden={organizationLogoError}
        />
      {/if}
    </div>
  </div>

  <!-- عنوان الوثيقة -->
  <div class="document-title">
    <h2>{subject}</h2>
  </div>

  <!-- معلومات الوثيقة -->
  <div class="document-info">
    <div class="info-item">
      <span class="info-label">التاريخ:</span>
      <span class="info-value">{formattedDate}</span>
    </div>

    <div class="info-item">
      <span class="info-label">الرقم الإشاري:</span>
      <span class="info-value">{referenceNumber}</span>
    </div>
  </div>

  <!-- محتوى الوثيقة -->
  <div class="document-content">
    {@html content}
  </div>

  <!-- تذييل الوثيقة -->
  <div class="document-footer">
    <!-- التوقيع الإلكتروني -->
    {#if hasElectronicSignature && signatureInfo}
      <div class="electronic-signature-area">
        <div class="signature-header">
          <div class="signature-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 12l2 2 4-4"></path>
              <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h18z"></path>
              <path d="M3 12v6c0 .552.448 1 1 1h16c.552 0 1-.448 1-1v-6"></path>
            </svg>
          </div>
          <div class="signature-title">توقيع إلكتروني معتمد</div>
        </div>

        <div class="signature-details">
          <div class="signature-info">
            <div class="info-row">
              <span class="label">الموقع:</span>
              <span class="value">{signatureInfo.details?.userName || sender?.full_name || 'غير محدد'}</span>
            </div>
            <div class="info-row">
              <span class="label">البريد الإلكتروني:</span>
              <span class="value">{signatureInfo.details?.email || sender?.email || 'غير محدد'}</span>
            </div>
            <div class="info-row">
              <span class="label">تاريخ التوقيع:</span>
              <span class="value">{signatureInfo.details?.date || formattedDate}</span>
            </div>
            <div class="info-row">
              <span class="label">معرف التوقيع:</span>
              <span class="value signature-id">{signatureInfo.details?.signatureId || 'غير محدد'}</span>
            </div>
            <div class="info-row">
              <span class="label">طريقة التوقيع:</span>
              <span class="value">{signatureInfo.verificationMethod || 'HASH-SHA256'}</span>
            </div>
            <div class="info-row">
              <span class="label">مستوى الأمان:</span>
              <span class="value security-level">{signatureInfo.securityLevel || 'عالي'}</span>
            </div>
            {#if signatureInfo.details?.passwordProtected}
              <div class="info-row">
                <span class="label">الحماية:</span>
                <span class="value protected">محمي بكلمة مرور 🔒</span>
              </div>
            {/if}
          </div>

          <!-- الختم إذا كان متوفراً -->
          {#if processedStamp && mounted}
            <div class="stamp">
              <img
                src={processedStamp}
                alt="ختم"
                on:error={() => stampError = true}
                class:hidden={stampError}
              />
            </div>
          {/if}
        </div>

        <div class="signature-verification">
          <div class="verification-badge">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <span>تم التحقق من صحة التوقيع الإلكتروني</span>
          </div>
        </div>
      </div>
    {:else}
      <!-- التوقيع العادي (للمستندات القديمة) -->
      <div class="signature-area">
        {#if processedStamp && mounted}
          <div class="stamp">
            <img
              src={processedStamp}
              alt="ختم"
              on:error={() => stampError = true}
              class:hidden={stampError}
            />
          </div>
        {/if}

        <div class="signature-text">
          <div class="signature-title">التوقيع</div>
          {#if sender}
            <div class="sender-name">{sender.full_name}</div>
          {/if}
        </div>
      </div>
    {/if}

    <div class="issue-date">
      <div>صدر في: {formattedDate}</div>
    </div>
  </div>
</div>

<style>
  .official-document {
    width: 100%;
    max-width: 210mm; /* عرض A4 */
    margin: 0 auto;
    padding: 20mm;
    box-sizing: border-box;
    position: relative;
    font-family: 'Calibri', 'Arial', sans-serif;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
  }

  .print-mode {
    box-shadow: none;
    border: none;
    padding: 0;
  }

  /* رأس الوثيقة */
  .document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #4a5568;
  }

  .logo {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .header-text {
    text-align: center;
    flex: 1;
  }

  .header-text h1 {
    font-size: 24px;
    margin: 0 0 5px 0;
    font-weight: bold;
  }

  .header-text h2 {
    font-size: 18px;
    margin: 0;
    font-weight: bold;
  }

  /* عنوان الوثيقة */
  .document-title {
    text-align: center;
    margin: 20px 0;
  }

  .document-title h2 {
    font-size: 22px;
    font-weight: bold;
    display: inline-block;
    padding: 5px 30px;
    border-bottom: 2px solid #000;
    margin: 0;
  }

  /* معلومات الوثيقة */
  .document-info {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
    padding: 10px 0;
    border-top: 1px solid #4a5568;
    border-bottom: 1px solid #4a5568;
    position: relative;
    font-size: 14px;
    font-weight: bold;
  }

  .info-item {
    display: flex;
    align-items: center;
  }

  .info-label {
    margin-left: 5px;
    font-weight: bold;
  }

  /* محتوى الوثيقة */
  .document-content {
    min-height: 300px;
    margin-bottom: 30px;
    line-height: 1.6;
    text-align: justify;
    font-size: 16px;
  }

  /* تذييل الوثيقة */
  .document-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #4a5568;
    position: relative;
  }

  /* التوقيع الإلكتروني */
  .electronic-signature-area {
    border: 2px solid #10b981;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  }

  .signature-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    gap: 10px;
  }

  .signature-icon {
    color: #10b981;
  }

  .signature-title {
    font-weight: bold;
    font-size: 18px;
    color: #065f46;
  }

  .signature-details {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
  }

  .signature-info {
    flex: 1;
  }

  .info-row {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
  }

  .info-row .label {
    font-weight: bold;
    color: #374151;
    min-width: 120px;
    margin-left: 10px;
  }

  .info-row .value {
    color: #1f2937;
    flex: 1;
  }

  .signature-id {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
  }

  .security-level {
    font-weight: bold;
    color: #059669;
  }

  .protected {
    color: #dc2626;
    font-weight: bold;
  }

  .signature-verification {
    margin-top: 15px;
    text-align: center;
  }

  .verification-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #10b981;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
  }

  /* التوقيع العادي */
  .signature-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
  }

  .stamp {
    width: 80px;
    height: 80px;
    margin-bottom: 10px;
  }

  .stamp img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  .signature-text {
    text-align: center;
  }

  .signature-text .signature-title {
    font-weight: bold;
    margin-bottom: 5px;
  }

  .sender-name {
    font-size: 16px;
    font-weight: bold;
  }

  .issue-date {
    text-align: left;
    font-size: 14px;
    font-weight: bold;
    margin-top: 20px;
  }

  .hidden {
    display: none;
  }

  /* تنسيقات الطباعة */
  @media print {
    .official-document {
      width: 210mm;
      height: 297mm;
      margin: 0;
      padding: 20mm;
      box-sizing: border-box;
      box-shadow: none;
      border: none;
      page-break-inside: avoid;
    }

    .document-content {
      page-break-inside: auto;
    }

    .document-footer {
      page-break-inside: avoid;
    }

    .electronic-signature-area {
      border: 2px solid #000;
      background: white;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }

    .signature-icon {
      color: #000;
    }

    .signature-title {
      color: #000;
    }

    .verification-badge {
      background: #000 !important;
      color: white !important;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }

    .hidden {
      display: none;
    }
  }
</style>
