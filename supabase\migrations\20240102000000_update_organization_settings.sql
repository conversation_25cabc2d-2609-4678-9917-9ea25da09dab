-- تحديث جدول إعدادات المنظمة لإضافة أعمدة الشعارات
ALTER TABLE public.organization_settings
ADD COLUMN IF NOT EXISTS right_logo TEXT,
ADD COLUMN IF NOT EXISTS left_logo TEXT,
ADD COLUMN IF NOT EXISTS default_stamp TEXT;

-- إنشاء سياسات الوصول لجدول إعدادات المنظمة
CREATE POLICY IF NOT EXISTS "Allow admins to update organization settings"
  ON public.organization_settings
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- إنشاء وظيفة للتحقق من صلاحيات المدير
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  );
END;
$$;
