-- تطبيق migration لإضافة الأعمدة المفقودة

-- التأكد من وجود عمود rejection_reason في جدول signed_documents
DO $$
BEGIN
  -- إضافة عمود rejection_reason إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'signed_documents'
    AND column_name = 'rejection_reason'
  ) THEN
    ALTER TABLE public.signed_documents ADD COLUMN rejection_reason TEXT;
    RAISE NOTICE 'Added column rejection_reason to signed_documents table';
  ELSE
    RAISE NOTICE 'Column rejection_reason already exists in signed_documents table';
  END IF;

  -- إضافة عمود revision_comments إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'signed_documents'
    AND column_name = 'revision_comments'
  ) THEN
    ALTER TABLE public.signed_documents ADD COLUMN revision_comments TEXT;
    RAISE NOTICE 'Added column revision_comments to signed_documents table';
  ELSE
    RAISE NOTICE 'Column revision_comments already exists in signed_documents table';
  END IF;

  -- التأكد من وجود عمود review_notes إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'signed_documents'
    AND column_name = 'review_notes'
  ) THEN
    ALTER TABLE public.signed_documents ADD COLUMN review_notes TEXT;
    RAISE NOTICE 'Added column review_notes to signed_documents table';
  ELSE
    RAISE NOTICE 'Column review_notes already exists in signed_documents table';
  END IF;

END $$;

-- إضافة تعليقات للأعمدة
COMMENT ON COLUMN public.signed_documents.rejection_reason IS 'سبب الرفض إذا تم رفض التوقيع';
COMMENT ON COLUMN public.signed_documents.revision_comments IS 'تعليقات طلب التعديل';
COMMENT ON COLUMN public.signed_documents.review_notes IS 'ملاحظات المراجعة';

-- عرض بنية الجدول للتأكد
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'signed_documents'
ORDER BY ordinal_position;

-- تحديث ذاكرة التخزين المؤقت للمخطط
NOTIFY pgrst, 'reload schema';
