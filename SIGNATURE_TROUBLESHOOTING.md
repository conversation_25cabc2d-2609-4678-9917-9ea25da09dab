# دليل استكشاف أخطاء التوقيع الإلكتروني

## المشكلة الحالية

**الوصف**: بعد توقيع المستند، التوقيع الإلكتروني لا يظهر في المستند المرفق ولا يزال يعرض "في انتظار التوقيع الإلكتروني".

## الأسباب المحتملة

### 1. مشكلة في تحديث المحتوى في Storage
- المحتوى قد يكون في Supabase Storage وليس في قاعدة البيانات
- دالة `updateDocumentContentWithSignature` لا تحدث Storage بشكل صحيح

### 2. مشكلة في نمط البحث والاستبدال
- النمط المستخدم للبحث عن مساحة التوقيع المؤقتة قد لا يطابق المحتوى الفعلي
- HTML قد يكون مختلف عن المتوقع

### 3. مشكلة في تحديث قاعدة البيانات
- رابط Storage لا يتم تحديثه بشكل صحيح
- المحتوى لا يتم حفظه في المكان الصحيح

## الحلول المطبقة

### 1. تحديث دالة `updateDocumentContentWithSignature`
```typescript
// إضافة دعم Storage
if (!updatedContent && message.attachment_content_url) {
  console.log('جلب محتوى المرفق من Storage...');
  updatedContent = await DocumentStorageService.getDocumentContent(message.attachment_content_url);
}

// حفظ المحتوى المحدث في Storage
if (message.attachment_content_url) {
  const newStoragePath = await DocumentStorageService.saveAttachmentContent(message.id, updatedContent);
  // تحديث رابط Storage في قاعدة البيانات
}
```

### 2. تحسين نمط البحث والاستبدال
```typescript
// النمط الأصلي
const tempSignaturePattern = /<div style="background-color: #fffbeb;[^>]*>[\s\S]*?في انتظار التوقيع الإلكتروني[\s\S]*?<\/div>\s*<\/div>/g;

// النمط البديل البسيط
const simplePattern = /في انتظار التوقيع الإلكتروني/g;
if (simpleMatches) {
  updatedContent = updatedContent.replace(
    /(<div[^>]*>[\s\S]*?)في انتظار التوقيع الإلكتروني([\s\S]*?<\/div>)/g,
    electronicSignatureHTML
  );
}
```

### 3. إضافة أدوات التشخيص
- `debugSignatureIssue()`: تشخيص مشاكل إنشاء التوقيع
- `debugAttachmentSignature()`: تشخيص محتوى المرفق والتوقيع
- تسجيل مفصل لكل خطوة في العملية

### 4. إضافة إعادة تحميل الصفحة
```typescript
// إضافة تأخير قصير ثم إعادة تحميل الصفحة لضمان عرض التوقيع المحدث
setTimeout(() => {
  window.location.reload();
}, 1000);
```

## خطوات استكشاف الأخطاء

### الخطوة 1: فتح Developer Console
1. اضغط F12 في المتصفح
2. انتقل إلى تبويب Console
3. قم بتوقيع المستند
4. راقب الرسائل في Console

### الخطوة 2: تحليل رسائل التشخيص
ابحث عن الرسائل التالية:

#### أ. تشخيص إنشاء التوقيع:
```
🔍 بدء تشخيص مشكلة التوقيع...
📋 تقرير تشخيص التوقيع الإلكتروني
=====================================
🆔 معرف المستند: [UUID]
📄 المستند موجود: ✅ نعم
👤 المستخدم: ✅ مسجل دخول
📝 مصدر المحتوى: storage/database
📏 طول المحتوى: [عدد] حرف
```

#### ب. تشخيص محتوى المرفق:
```
🔍 تشخيص محتوى المرفق قبل التحديث...
📋 تقرير تشخيص محتوى المرفق والتوقيع
==========================================
📧 معرف الرسالة: [UUID]
📄 الرسالة موجودة: ✅ نعم
📎 يحتوي على مرفق: ✅ نعم
🔄 يحتوي على مساحة التوقيع المؤقتة: ✅ نعم
✅ يحتوي على التوقيع الفعلي: ❌ لا
```

#### ج. عملية الاستبدال:
```
البحث عن مساحة التوقيع المؤقتة في المحتوى...
طول المحتوى الأصلي: [عدد]
عدد المطابقات الموجودة: [عدد]
نتيجة الاستبدال:
- طول المحتوى قبل الاستبدال: [عدد]
- طول المحتوى بعد الاستبدال: [عدد]
- تم الاستبدال: true/false
```

### الخطوة 3: تحديد المشكلة

#### إذا كان "تم الاستبدال: false":
- المشكلة في نمط البحث
- النص "في انتظار التوقيع الإلكتروني" غير موجود
- HTML مختلف عن المتوقع

#### إذا كان "تم الاستبدال: true" لكن التوقيع لا يظهر:
- المشكلة في حفظ المحتوى المحدث
- Storage لا يتم تحديثه
- قاعدة البيانات لا تتم تحديثها

#### إذا كان "مصدر المحتوى: none":
- المحتوى غير موجود في قاعدة البيانات أو Storage
- مشكلة في إنشاء المستند الأصلي

### الخطوة 4: الحلول المقترحة

#### أ. إذا لم يتم العثور على مساحة التوقيع:
1. تحقق من محتوى المستند الأصلي
2. تأكد من أن النص "في انتظار التوقيع الإلكتروني" موجود
3. تحقق من HTML المحيط بالنص

#### ب. إذا لم يتم حفظ المحتوى المحدث:
1. تحقق من صلاحيات Storage
2. تأكد من أن `DocumentStorageService.saveAttachmentContent` يعمل
3. تحقق من تحديث قاعدة البيانات

#### ج. إذا كان المحتوى غير موجود:
1. تحقق من إنشاء المستند الأصلي
2. تأكد من نقل المحتوى إلى Storage بشكل صحيح
3. راجع Migration للـ Storage

## الاختبارات اليدوية

### 1. اختبار وجود المحتوى:
```sql
-- في Supabase SQL Editor
SELECT 
  m.id,
  m.attachment->>'title' as title,
  m.attachment_content_url,
  LENGTH(m.attachment->>'content') as content_length_db
FROM messages m 
WHERE m.attachment->>'signature_request_id' = '[SIGNATURE_REQUEST_ID]';
```

### 2. اختبار Storage:
```typescript
// في Browser Console
const { data, error } = await supabase.storage
  .from('saadabujnah')
  .list('attachments', { limit: 100 });
console.log('Storage files:', data);
```

### 3. اختبار جلب المحتوى:
```typescript
// في Browser Console
import { DocumentStorageService } from '$lib/services/documentStorageService';
const content = await DocumentStorageService.getDocumentContent('[STORAGE_PATH]');
console.log('Content from storage:', content?.substring(0, 500));
```

## المراقبة المستمرة

### 1. إضافة تسجيل مخصص:
```typescript
console.log('🔍 Custom Debug - Message ID:', messageId);
console.log('🔍 Custom Debug - Attachment:', attachment);
console.log('🔍 Custom Debug - Storage URL:', attachmentContentUrl);
```

### 2. مراقبة Network Tab:
- تحقق من طلبات Storage API
- تأكد من نجاح عمليات PUT/POST
- راقب أخطاء 403/404

### 3. مراقبة Application Tab:
- تحقق من Local Storage
- راجع Session Storage
- تأكد من Cookies

## الدعم والمساعدة

في حالة استمرار المشكلة:
1. جمع جميع رسائل Console
2. أخذ لقطات شاشة للمشكلة
3. تسجيل خطوات إعادة إنتاج المشكلة
4. مراجعة logs قاعدة البيانات
5. التحقق من Storage permissions
