-- ملف SQL لإعداد دور المدير وربطه بالصلاحيات المناسبة

-- 1. التأكد من وجود دور "مدير" في جدول roles
DO $$
DECLARE
  manager_role_exists BOOLEAN;
BEGIN
  -- التحقق من وجود دور المدير
  SELECT EXISTS (
    SELECT 1 FROM roles WHERE name = 'manager'
  ) INTO manager_role_exists;
  
  -- إذا لم يكن موجوداً، قم بإضافته
  IF NOT manager_role_exists THEN
    INSERT INTO roles (name, description) 
    VALUES ('manager', 'مدير مع صلاحيات إدارية محدودة');
  END IF;
END $$;

-- 2. تعيين دور "مدير" للمستخدمين المحددين
DO $$
DECLARE
  manager_role_id UUID;
BEGIN
  -- الحصول على معرف دور المدير
  SELECT id INTO manager_role_id FROM roles WHERE name = 'manager';
  
  -- تعيين دور المدير للمستخدمين الذين لديهم دور 'manager' في عمود role
  UPDATE profiles 
  SET role_id = manager_role_id 
  WHERE role = 'manager';
  
  -- يمكنك أيضاً تعيين دور المدير لمستخدمين محددين بمعرفاتهم
  -- مثال:
  -- UPDATE profiles 
  -- SET role_id = manager_role_id 
  -- WHERE id = 'معرف-المستخدم-هنا';
END $$;

-- 3. ربط دور "مدير" بالصلاحيات المناسبة
DO $$
DECLARE
  manager_role_id UUID;
BEGIN
  -- الحصول على معرف دور المدير
  SELECT id INTO manager_role_id FROM roles WHERE name = 'manager';
  
  -- حذف الصلاحيات الحالية لدور المدير (اختياري)
  -- DELETE FROM role_permissions WHERE role_id = manager_role_id;
  
  -- إضافة صلاحيات المستندات
  INSERT INTO role_permissions (role_id, permission_id)
  SELECT manager_role_id, id FROM permissions 
  WHERE resource = 'documents' AND action IN ('create', 'read', 'update')
  ON CONFLICT (role_id, permission_id) DO NOTHING;
  
  -- صلاحيات المستخدمين
  INSERT INTO role_permissions (role_id, permission_id)
  SELECT manager_role_id, id FROM permissions 
  WHERE resource = 'users' AND action = 'read'
  ON CONFLICT (role_id, permission_id) DO NOTHING;
  
  -- صلاحيات المراسلات
  INSERT INTO role_permissions (role_id, permission_id)
  SELECT manager_role_id, id FROM permissions 
  WHERE resource = 'messages' AND action IN ('create', 'read', 'update')
  ON CONFLICT (role_id, permission_id) DO NOTHING;
  
  -- صلاحيات التعميمات
  INSERT INTO role_permissions (role_id, permission_id)
  SELECT manager_role_id, id FROM permissions 
  WHERE resource = 'broadcasts' AND action IN ('create', 'read', 'update')
  ON CONFLICT (role_id, permission_id) DO NOTHING;
  
  -- صلاحيات الهيكل التنظيمي
  INSERT INTO role_permissions (role_id, permission_id)
  SELECT manager_role_id, id FROM permissions 
  WHERE resource = 'organization' AND action IN ('read', 'update')
  ON CONFLICT (role_id, permission_id) DO NOTHING;
END $$;

-- 4. التحقق من إعداد دور المدير
-- هذا الاستعلام يعرض جميع صلاحيات دور المدير
SELECT 
  r.name AS role_name, 
  r.description AS role_description,
  p.name AS permission_name,
  p.resource,
  p.action,
  p.description AS permission_description
FROM 
  roles r
JOIN 
  role_permissions rp ON r.id = rp.role_id
JOIN 
  permissions p ON rp.permission_id = p.id
WHERE 
  r.name = 'manager'
ORDER BY 
  p.resource, p.action;
