<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib/supabase';
  import { PermissionService } from '$lib/services/permissionService';
  import { RoleType, Resource, Action } from '$lib/types/permissions';
  import { getRoleNameArabic, getPermissionNameArabic } from '$lib/types/permissions';

  let isAdmin = false;
  let loading = true;
  let roles = [];
  let permissions = [];
  let rolePermissions = {};

  // نموذج إضافة دور جديد
  let showAddForm = false;
  let newRole = {
    name: '',
    description: ''
  };

  // نموذج تعديل دور
  let showEditForm = false;
  let editingRole = null;
  let editingRolePermissions = [];

  onMount(async () => {
    try {
      // الحصول على المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        goto('/login');
        return;
      }

      // التحقق من صلاحيات المستخدم
      isAdmin = await PermissionService.checkRole(user.id, RoleType.ADMIN);

      if (!isAdmin) {
        goto('/dashboard');
        return;
      }

      // جلب الأدوار والصلاحيات
      await Promise.all([
        loadRoles(),
        loadPermissions()
      ]);

      // جلب العلاقات بين الأدوار والصلاحيات
      await loadRolePermissions();
    } catch (error) {
      console.error('Error loading roles page:', error);
    } finally {
      loading = false;
    }
  });

  async function loadRoles() {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('name');

      if (error) throw error;

      roles = data || [];
    } catch (error) {
      console.error('Error loading roles:', error);
    }
  }

  async function loadPermissions() {
    try {
      const { data, error } = await supabase
        .from('permissions')
        .select('*')
        .order('resource')
        .order('action');

      if (error) throw error;

      permissions = data || [];
    } catch (error) {
      console.error('Error loading permissions:', error);
    }
  }

  async function loadRolePermissions() {
    try {
      const { data, error } = await supabase
        .from('role_permissions')
        .select('*');

      if (error) throw error;

      // تنظيم العلاقات حسب الدور
      rolePermissions = {};

      if (data) {
        data.forEach(rp => {
          if (!rolePermissions[rp.role_id]) {
            rolePermissions[rp.role_id] = [];
          }
          rolePermissions[rp.role_id].push(rp.permission_id);
        });
      }
    } catch (error) {
      console.error('Error loading role permissions:', error);
    }
  }

  async function handleAddRole() {
    try {
      const { data, error } = await supabase
        .from('roles')
        .insert([newRole])
        .select();

      if (error) throw error;

      if (data && data.length > 0) {
        // إضافة الدور الجديد إلى القائمة
        await loadRoles();

        // إعادة تعيين النموذج
        newRole = {
          name: '',
          description: ''
        };

        showAddForm = false;
      }
    } catch (error) {
      console.error('Error adding role:', error);
      alert('حدث خطأ أثناء إضافة الدور');
    }
  }

  function openEditForm(role) {
    editingRole = { ...role };
    editingRolePermissions = rolePermissions[role.id] || [];
    showEditForm = true;
  }

  async function handleEditRole() {
    try {
      if (!editingRole || !editingRole.id) return;

      // تحديث معلومات الدور
      const { error } = await supabase
        .from('roles')
        .update({
          name: editingRole.name,
          description: editingRole.description
        })
        .eq('id', editingRole.id);

      if (error) throw error;

      // تحديث صلاحيات الدور

      // 1. حذف جميع الصلاحيات الحالية
      const { error: deleteError } = await supabase
        .from('role_permissions')
        .delete()
        .eq('role_id', editingRole.id);

      if (deleteError) throw deleteError;

      // 2. إضافة الصلاحيات الجديدة
      if (editingRolePermissions.length > 0) {
        const rolePermissionsToInsert = editingRolePermissions.map(permissionId => ({
          role_id: editingRole.id,
          permission_id: permissionId
        }));

        const { error: insertError } = await supabase
          .from('role_permissions')
          .insert(rolePermissionsToInsert);

        if (insertError) throw insertError;
      }

      // تحديث القائمة
      await Promise.all([
        loadRoles(),
        loadRolePermissions()
      ]);

      showEditForm = false;
      editingRole = null;
      editingRolePermissions = [];
    } catch (error) {
      console.error('Error updating role:', error);
      alert('حدث خطأ أثناء تحديث الدور');
    }
  }

  async function handleDeleteRole(roleId) {
    if (!confirm('هل أنت متأكد من حذف هذا الدور؟')) return;

    try {
      // التحقق من وجود مستخدمين مرتبطين بالدور
      const { count, error: countError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .eq('role_id', roleId);

      if (countError) throw countError;

      if (count > 0) {
        alert('لا يمكن حذف هذا الدور لأنه مرتبط بمستخدمين');
        return;
      }

      // حذف العلاقات بين الدور والصلاحيات
      const { error: deleteRPError } = await supabase
        .from('role_permissions')
        .delete()
        .eq('role_id', roleId);

      if (deleteRPError) throw deleteRPError;

      // حذف الدور
      const { error } = await supabase
        .from('roles')
        .delete()
        .eq('id', roleId);

      if (error) throw error;

      // تحديث القائمة
      await Promise.all([
        loadRoles(),
        loadRolePermissions()
      ]);
    } catch (error) {
      console.error('Error deleting role:', error);
      alert('حدث خطأ أثناء حذف الدور');
    }
  }

  function togglePermission(permissionId) {
    const index = editingRolePermissions.indexOf(permissionId);

    if (index === -1) {
      editingRolePermissions = [...editingRolePermissions, permissionId];
    } else {
      editingRolePermissions = editingRolePermissions.filter(id => id !== permissionId);
    }
  }

  // تجميع الصلاحيات حسب المورد
  function groupPermissionsByResource() {
    const grouped = {};

    permissions.forEach(permission => {
      if (!grouped[permission.resource]) {
        grouped[permission.resource] = [];
      }

      grouped[permission.resource].push(permission);
    });

    return grouped;
  }
</script>

<svelte:head>
  <title>إدارة الأدوار والصلاحيات</title>
</svelte:head>

<div class="container mx-auto p-4 rtl" dir="rtl">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">إدارة الأدوار والصلاحيات</h1>

    <button
      class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
      on:click={() => showAddForm = true}
    >
      إضافة دور جديد
    </button>
  </div>

  {#if loading}
    <div class="flex justify-center items-center h-64">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  {:else}
    <!-- جدول الأدوار -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
      <h2 class="text-xl font-bold p-4 bg-gray-50 border-b">الأدوار المتاحة</h2>

      {#if roles.length === 0}
        <div class="p-6 text-center text-gray-500">
          لا توجد أدوار متاحة
        </div>
      {:else}
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الصلاحيات</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {#each roles as role}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="font-medium text-gray-900">{getRoleNameArabic(role.name)}</div>
                    <div class="text-sm text-gray-500">{role.name}</div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900">{role.description || '-'}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      {rolePermissions[role.id] ? rolePermissions[role.id].length : 0}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-left">
                    <button
                      class="text-indigo-600 hover:text-indigo-900 ml-3"
                      on:click={() => openEditForm(role)}
                    >
                      تعديل
                    </button>
                    <button
                      class="text-red-600 hover:text-red-900"
                      on:click={() => handleDeleteRole(role.id)}
                    >
                      حذف
                    </button>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {/if}
    </div>
  {/if}

  <!-- نموذج إضافة دور جديد -->
  {#if showAddForm}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        <h2 class="text-xl font-bold mb-4">إضافة دور جديد</h2>

        <div class="mb-4">
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">اسم الدور</label>
          <input
            id="name"
            type="text"
            bind:value={newRole.name}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
          <p class="text-xs text-gray-500 mt-1">مثال: admin, manager, editor</p>
        </div>

        <div class="mb-4">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
          <textarea
            id="description"
            bind:value={newRole.description}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            rows="3"
          ></textarea>
        </div>

        <div class="flex justify-end gap-2">
          <button
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            on:click={() => showAddForm = false}
          >
            إلغاء
          </button>
          <button
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            on:click={handleAddRole}
          >
            إضافة
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- نموذج تعديل دور -->
  {#if showEditForm && editingRole}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-2xl p-6 max-h-screen overflow-y-auto">
        <h2 class="text-xl font-bold mb-4">تعديل دور: {getRoleNameArabic(editingRole.name)}</h2>

        <div class="mb-4">
          <label for="edit-name" class="block text-sm font-medium text-gray-700 mb-1">اسم الدور</label>
          <input
            id="edit-name"
            type="text"
            bind:value={editingRole.name}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>

        <div class="mb-4">
          <label for="edit-description" class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
          <textarea
            id="edit-description"
            bind:value={editingRole.description}
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            rows="3"
          ></textarea>
        </div>

        <div class="mb-4">
          <h3 class="text-lg font-medium mb-2">الصلاحيات</h3>

          {#if permissions.length === 0}
            <p class="text-gray-500">لا توجد صلاحيات متاحة</p>
          {:else}
            <div class="border rounded-md divide-y">
              {#each Object.entries(groupPermissionsByResource()) as [resource, resourcePermissions]}
                <div class="p-4">
                  <h4 class="font-medium mb-2">{resource}</h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {#each resourcePermissions as permission}
                      <label class="flex items-center space-x-2 space-x-reverse">
                        <input
                          type="checkbox"
                          checked={editingRolePermissions.includes(permission.id)}
                          on:change={() => togglePermission(permission.id)}
                          class="form-checkbox h-5 w-5 text-blue-600"
                        />
                        <span>{getPermissionNameArabic(permission.resource, permission.action)}</span>
                      </label>
                    {/each}
                  </div>
                </div>
              {/each}
            </div>
          {/if}
        </div>

        <div class="flex justify-end gap-2">
          <button
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            on:click={() => showEditForm = false}
          >
            إلغاء
          </button>
          <button
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            on:click={handleEditRole}
          >
            حفظ التغييرات
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .rtl {
    direction: rtl;
    text-align: right;
  }
</style>
