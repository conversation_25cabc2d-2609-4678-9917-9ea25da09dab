# نظام الوصول للمستندات حسب الهيكل التنظيمي

تم تنفيذ نظام للوصول إلى المستندات بناءً على الهيكل التنظيمي، بحيث:

1. يستطيع كل مستخدم إضافة المستندات داخل قسمه أو وحدته أو إدارته فقط
2. لا يستطيع المستخدم رؤية مستندات أقسام أخرى إلا إذا كانت تابعة له في الهيكل التنظيمي

## التغييرات التي تم تنفيذها:

### 1. تعديل خدمة المستندات (DocumentService)

- تم تعديل وظيفة `getAllDocuments` لتقييد الوصول حسب الوحدة التنظيمية
- تم إضافة وظيفة `canAccessDocument` للتحقق من صلاحية المستخدم للوصول إلى مستند معين
- تم تعديل وظيفة `addDocument` للتحقق من أن المستخدم ينتمي للوحدة التي يحاول إضافة المستند إليها

### 2. تعديل صفحة المستندات

- تم تعديل وظيفة `fetchDocuments` لتقييد الوصول حسب الوحدة التنظيمية
- تم تعديل وظيفة `handleAddDocument` لتقييد إضافة المستندات حسب الوحدة التنظيمية
- تم تعديل واجهة المستخدم لعرض الوحدات التي يمكن للمستخدم الوصول إليها فقط

### 3. تعديل صفحة تفاصيل المستند

- تم تعديل وظيفة `onMount` للتحقق من صلاحية المستخدم للوصول إلى المستند
- تم تعديل وظيفة `handleFileUpload` للتحقق من صلاحية المستخدم لإضافة مرفقات للمستند

### 4. تعديل سياسات الأمان في قاعدة البيانات

تم إنشاء ملف `update_document_policies.sql` لتعديل سياسات الأمان في قاعدة البيانات:

- سياسة قراءة المستندات: للمستخدمين المصرح لهم فقط (المنشئ، المشرف، أعضاء الوحدة، أعضاء الوحدات الأعلى)
- سياسة إنشاء المستندات: للمستخدمين المصرح لهم فقط (أعضاء الوحدة، أعضاء الوحدات الأعلى، المشرفين)
- سياسة تعديل المستندات: للمنشئ والمشرفين فقط
- سياسة حذف المستندات: للمنشئ والمشرفين فقط
- سياسات مماثلة للمرفقات

## كيفية تطبيق التغييرات:

1. تم تعديل الكود المصدري للتطبيق
2. يجب تنفيذ ملف `update_document_policies.sql` في قاعدة البيانات Supabase لتطبيق سياسات الأمان الجديدة

## كيفية استخدام النظام:

### للمستخدمين العاديين:

- يمكنك رؤية المستندات الخاصة بوحدتك التنظيمية فقط
- يمكنك إضافة مستندات لوحدتك التنظيمية فقط
- يمكنك تعديل وحذف المستندات التي قمت بإنشائها فقط

### للمديرين:

- يمكنك رؤية المستندات الخاصة بوحدتك التنظيمية والوحدات التابعة لها
- يمكنك إضافة مستندات لوحدتك التنظيمية والوحدات التابعة لها
- يمكنك تعديل وحذف المستندات التي قمت بإنشائها فقط

### للمشرفين:

- يمكنك رؤية جميع المستندات في النظام
- يمكنك إضافة مستندات لأي وحدة تنظيمية
- يمكنك تعديل وحذف أي مستند في النظام

## ملاحظات هامة:

1. يجب تعيين الوحدة التنظيمية لكل مستخدم في النظام
2. يجب تعيين الدور المناسب لكل مستخدم (مستخدم عادي، مدير، مشرف)
3. يجب إنشاء الهيكل التنظيمي بشكل صحيح (علاقات التبعية بين الوحدات)

## تنفيذ سياسات الأمان:

لتنفيذ سياسات الأمان الجديدة، يجب تنفيذ الأمر التالي في SQL Editor في Supabase:

```sql
-- تنفيذ ملف update_document_policies.sql
```

يمكنك نسخ محتوى الملف `src\lib\db\update_document_policies.sql` وتنفيذه في SQL Editor.
