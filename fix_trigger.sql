-- إصلاح trigger تاريخ المستندات الموقعة

-- حذف trigger القديم
DROP TRIGGER IF EXISTS update_signed_document_history ON public.signed_documents;
DROP FUNCTION IF EXISTS public.update_signed_document_history();

-- إعادة إنشاء دالة trigger مع إصلاح مشكلة user_id
CREATE OR REPLACE FUNCTION public.update_signed_document_history()
RETURNS TRIGGER AS $$
BEGIN
  -- التحقق من وجود user_id صالح
  DECLARE
    target_user_id UUID;
  BEGIN
    -- تحديد المستخدم بناءً على نوع العملية والحالة
    target_user_id := CASE
      WHEN TG_OP = 'INSERT' THEN NEW.creator_id
      WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        CASE
          WHEN NEW.status = 'signed' THEN NEW.signer_id
          WHEN NEW.status = 'rejected' THEN NEW.signer_id
          WHEN NEW.status = 'revision_requested' THEN NEW.signer_id
          WHEN NEW.status = 'under_review' THEN NEW.signer_id
          WHEN NEW.status = 'approved' THEN NEW.signer_id
          WHEN NEW.status = 'returned_for_edit' THEN NEW.signer_id
          ELSE COALESCE(auth.uid(), NEW.creator_id, NEW.signer_id)
        END
      ELSE COALESCE(auth.uid(), NEW.creator_id, NEW.signer_id)
    END;

    -- التأكد من وجود user_id صالح
    IF target_user_id IS NULL THEN
      target_user_id := NEW.creator_id;
    END IF;

    -- إذا كان لا يزال NULL، استخدم قيمة افتراضية
    IF target_user_id IS NULL THEN
      RAISE WARNING 'لا يمكن تحديد user_id للمستند %', NEW.id;
      RETURN NEW; -- تجاهل إدراج السجل في التاريخ
    END IF;

    -- إدراج سجل في تاريخ المستندات الموقعة
    INSERT INTO public.signed_document_history (
      signed_document_id,
      user_id,
      action,
      status,
      details
    ) VALUES (
      NEW.id,
      target_user_id,
      -- تحديد نوع الإجراء
      CASE
        WHEN TG_OP = 'INSERT' THEN 'create'
        WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
          CASE
            WHEN NEW.status = 'pending_signature' THEN 'request_signature'
            WHEN NEW.status = 'signed' THEN 'sign'
            WHEN NEW.status = 'sent' THEN 'send'
            WHEN NEW.status = 'under_review' THEN 'review'
            WHEN NEW.status = 'approved' THEN 'approve'
            WHEN NEW.status = 'rejected' THEN 'reject'
            WHEN NEW.status = 'revision_requested' THEN 'request_revision'
            WHEN NEW.status = 'returned_for_edit' THEN 'return_for_edit'
            ELSE 'update'
          END
        ELSE 'update'
      END,
      NEW.status,
      jsonb_build_object(
        'document_id', NEW.document_id,
        'reference_number', NEW.reference_number,
        'rejection_reason', COALESCE(NEW.rejection_reason, ''),
        'revision_comments', COALESCE(NEW.revision_comments, ''),
        'old_status', CASE WHEN TG_OP = 'UPDATE' THEN OLD.status ELSE NULL END
      )
    );
  EXCEPTION
    WHEN OTHERS THEN
      RAISE WARNING 'خطأ في إدراج سجل التاريخ للمستند %: %', NEW.id, SQLERRM;
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إعادة إنشاء trigger
CREATE TRIGGER update_signed_document_history
AFTER INSERT OR UPDATE ON public.signed_documents
FOR EACH ROW
EXECUTE FUNCTION public.update_signed_document_history();

-- التأكد من أن جدول signed_document_history يحتوي على الأعمدة المطلوبة
DO $$
BEGIN
  -- التحقق من وجود عمود details
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'signed_document_history'
    AND column_name = 'details'
  ) THEN
    ALTER TABLE public.signed_document_history ADD COLUMN details JSONB;
  END IF;

  -- التأكد من أن عمود user_id يسمح بـ NULL مؤقتاً
  ALTER TABLE public.signed_document_history ALTER COLUMN user_id DROP NOT NULL;
  
  -- إضافة constraint جديد يسمح بـ NULL فقط في حالات استثنائية
  -- ALTER TABLE public.signed_document_history ALTER COLUMN user_id SET NOT NULL;
END $$;

-- إضافة تعليق للدالة
COMMENT ON FUNCTION public.update_signed_document_history() IS 'دالة لتحديث تاريخ المستندات الموقعة مع معالجة مشكلة user_id المفقود';

-- تحديث ذاكرة التخزين المؤقت للمخطط
NOTIFY pgrst, 'reload schema';

-- عرض نتيجة الإصلاح
SELECT 'تم إصلاح trigger بنجاح' as result;
