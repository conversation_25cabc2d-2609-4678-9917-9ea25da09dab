# دليل نقل البيانات إلى Supabase Storage

## نظرة عامة

هذا الدليل يوضح كيفية نقل محتوى المستندات والمرفقات من قاعدة البيانات إلى Supabase Storage لتحسين الأداء وتقليل الضغط على قاعدة البيانات.

## المشكلة التي تم حلها

### خطأ Syntax في Migration الأول:
```
ERROR: 42601: syntax error at or near "NOT"
LINE 11: CREATE POLICY IF NOT EXISTS "Authenticated users can read documents"
```

**السبب**: PostgreSQL لا يدعم `CREATE POLICY IF NOT EXISTS`

**الحل**: استخدام `DROP POLICY IF EXISTS` ثم `CREATE POLICY`

## الملفات المحدثة

### 1. Migration الجديد المحدث:
- `supabase/migrations/20241207000002_storage_setup_fixed.sql`
- يحل مشاكل الـ syntax
- يتضمن معالجة أفضل للأخطاء

### 2. خدمة إدارة التخزين:
- `src/lib/services/documentStorageService.ts`
- وظائف شاملة لإدارة Storage

### 3. سكريبت النقل:
- `src/lib/scripts/migrateToStorage.ts`
- أدوات نقل البيانات الموجودة

### 4. صفحة الإدارة:
- `src/routes/admin/storage-migration/+page.svelte`
- واجهة لتشغيل عمليات النقل

## خطوات التنفيذ

### الخطوة 1: تشغيل Migration الجديد
```bash
# تشغيل Migration المحدث
supabase db push

# أو تشغيل migration محدد
supabase migration up 20241207000002_storage_setup_fixed
```

### الخطوة 2: التحقق من إعداد Storage
```sql
-- التحقق من وجود bucket
SELECT * FROM storage.buckets WHERE id = 'saadabujnah';

-- التحقق من السياسات
SELECT * FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage';

-- التحقق من الأعمدة الجديدة
\d public.documents
\d public.messages
```

### الخطوة 3: مراقبة حالة النقل
```sql
-- عرض إحصائيات النقل
SELECT * FROM public.get_storage_migration_stats();

-- عرض حالة النقل التفصيلية
SELECT * FROM public.storage_migration_status;
```

### الخطوة 4: تشغيل النقل
1. زيارة `/admin/storage-migration`
2. مراجعة الإحصائيات
3. تشغيل النقل التدريجي:
   - نقل المستندات فقط (اختبار)
   - نقل المرفقات فقط (اختبار)
   - نقل جميع البيانات (إنتاج)

### الخطوة 5: تنظيف البيانات القديمة
```sql
-- بعد التأكد من نجاح النقل
-- تنظيف محتوى المستندات
UPDATE public.documents 
SET content = NULL 
WHERE content_url IS NOT NULL AND content IS NOT NULL;

-- تنظيف محتوى المرفقات (يتطلب معالجة خاصة)
-- سيتم تنفيذه من خلال صفحة الإدارة
```

## هيكل التخزين الجديد

### في قاعدة البيانات:
```sql
-- جدول documents
documents {
  id: UUID
  title: TEXT
  content: TEXT (NULL بعد النقل)
  content_url: TEXT (رابط Storage)
  ...
}

-- جدول messages
messages {
  id: UUID
  subject: TEXT
  content: TEXT
  attachment: JSONB (بدون content)
  attachment_content_url: TEXT (رابط Storage)
  ...
}
```

### في Supabase Storage:
```
bucket: saadabujnah/
├── documents/
│   ├── 2024/
│   │   ├── 12/
│   │   │   ├── uuid1.html
│   │   │   ├── uuid2.html
│   │   │   └── ...
│   │   └── ...
│   └── ...
└── attachments/
    ├── 2024/
    │   ├── 12/
    │   │   ├── uuid1.html
    │   │   ├── uuid2.html
    │   │   └── ...
    │   └── ...
    └── ...
```

## استخدام النظام الجديد

### للمطورين:
```typescript
import { DocumentStorageService } from '$lib/services/documentStorageService';

// حفظ محتوى جديد
const storagePath = await DocumentStorageService.saveDocumentContent(documentId, content);
await DocumentStorageService.updateDocumentContentUrl(documentId, storagePath);

// جلب محتوى موجود
const content = await DocumentStorageService.getDocumentContent(storagePath);

// نقل مستند موجود
const migrated = await DocumentStorageService.migrateDocumentToStorage(documentId);
```

### للمشرفين:
1. زيارة `/admin/storage-migration`
2. مراجعة الإحصائيات
3. تشغيل النقل التدريجي
4. مراقبة النتائج
5. تنظيف البيانات القديمة

## مراقبة الأداء

### قبل النقل:
```sql
-- حجم جدول documents
SELECT pg_size_pretty(pg_total_relation_size('public.documents'));

-- حجم جدول messages
SELECT pg_size_pretty(pg_total_relation_size('public.messages'));
```

### بعد النقل:
```sql
-- مقارنة الأحجام
SELECT 
  'documents' as table_name,
  pg_size_pretty(pg_total_relation_size('public.documents')) as size_after,
  COUNT(*) as total_records,
  COUNT(content_url) as migrated_records
FROM public.documents

UNION ALL

SELECT 
  'messages' as table_name,
  pg_size_pretty(pg_total_relation_size('public.messages')) as size_after,
  COUNT(*) as total_records,
  COUNT(attachment_content_url) as migrated_records
FROM public.messages;
```

## استكشاف الأخطاء

### مشكلة: فشل في إنشاء bucket
```sql
-- التحقق من وجود bucket
SELECT * FROM storage.buckets WHERE id = 'saadabujnah';

-- إنشاء bucket يدوياً
INSERT INTO storage.buckets (id, name, public) 
VALUES ('saadabujnah', 'Saadabujnah Documents', false);
```

### مشكلة: فشل في رفع الملفات
```typescript
// التحقق من صلاحيات المستخدم
const { data: { user } } = await supabase.auth.getUser();
console.log('User:', user);

// التحقق من سياسات Storage
const { data, error } = await supabase.storage
  .from('saadabujnah')
  .list('documents', { limit: 1 });
```

### مشكلة: فشل في جلب المحتوى
```typescript
// التحقق من وجود الملف
const { data, error } = await supabase.storage
  .from('saadabujnah')
  .list('documents/2024/12', { limit: 100 });

// استخدام رابط موقع
const { data: signedUrl } = await supabase.storage
  .from('saadabujnah')
  .createSignedUrl('documents/2024/12/uuid.html', 3600);
```

## النتائج المتوقعة

### تحسين الأداء:
- تقليل 80-90% في حجم قاعدة البيانات
- تحسن 50-70% في سرعة الاستعلامات
- تحميل أسرع للصفحات

### توفير التكلفة:
- تقليل استهلاك قاعدة البيانات
- استخدام أمثل للتخزين السحابي
- توفير في التكاليف الشهرية

### قابلية التوسع:
- دعم ملفات أكبر
- معالجة متوازية
- أداء ثابت مع نمو البيانات

## الدعم والمساعدة

في حالة وجود مشاكل:
1. تحقق من logs قاعدة البيانات
2. تحقق من console المتصفح
3. راجع صلاحيات Storage
4. تأكد من تشغيل Migration بنجاح
5. استخدم صفحة الإدارة لمراقبة التقدم
