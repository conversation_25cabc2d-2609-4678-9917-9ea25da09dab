# إصلاح مشكلة العلاقات في نظام المراسلات

تم إصلاح مشكلة "Could not find a relationship between 'messages' and 'receiver_id' in the schema cache" التي تظهر عند إرسال الرسائل.

## المشكلة

كان المستخدمون يواجهون خطأ عند محاولة إرسال رسالة جديدة، حيث تظهر رسالة:

```
حدث خطأ أثناء إرسال الرسالة: فشل إرسال الرسالة: Could not find a relationship between 'messages' and 'receiver_id' in the schema cache
```

هذه المشكلة تحدث بسبب عدم قدرة Supabase على العثور على العلاقة بين جدول `messages` وحقل `receiver_id` في ذاكرة التخزين المؤقت للمخطط.

## الحل

تم تنفيذ حلين متكاملين لإصلاح المشكلة:

### 1. تعديل استعلام إرسال الرسالة

تم تعديل وظيفة `handleSendMessage` في صفحة المراسلات لتجنب استخدام العلاقات المباشرة في استعلام الإدراج:

- تم فصل عملية الإدراج عن عملية جلب البيانات المرتبطة
- تم إدراج الرسالة أولاً بدون محاولة جلب البيانات المرتبطة
- ثم تم جلب الرسالة المدرجة مع البيانات المرتبطة في استعلام منفصل
- تم جلب بيانات المستقبل بشكل منفصل إذا كان هناك مستقبل محدد

### 2. إصلاح العلاقات في قاعدة البيانات

تم إنشاء ملف SQL `fix_message_relationships.sql` لإصلاح العلاقات في قاعدة البيانات:

- إعادة تعريف العلاقات في جدول المراسلات
- إنشاء فهارس للبحث السريع
- تحديث ذاكرة التخزين المؤقت للمخطط
- إنشاء وظيفة RPC لتحديث ذاكرة التخزين المؤقت للمخطط
- إنشاء وظيفة لإصلاح مشكلة العلاقات في استعلامات الجلب

## التغييرات التي تم تنفيذها:

### 1. تعديل وظيفة `handleSendMessage`

```typescript
// إرسال الرسالة
const { data: insertedData, error: insertError } = await supabase
  .from('messages')
  .insert([messageData])
  .select();

if (insertError) {
  console.error('Supabase insert error:', insertError);
  throw new Error(`فشل إرسال الرسالة: ${insertError.message}`);
}

// جلب الرسالة المرسلة مع البيانات المرتبطة
let data = null;
if (insertedData && insertedData.length > 0) {
  const messageId = insertedData[0].id;
  const { data: messageData, error: fetchError } = await supabase
    .from('messages')
    .select(`
      *,
      sender:sender_id(full_name),
      receiver_unit:receiver_unit_id(name)
    `)
    .eq('id', messageId)
    .single();
    
  if (fetchError) {
    console.error('Supabase fetch error:', fetchError);
  } else {
    data = [messageData];
    
    // إذا كان هناك مستقبل محدد، جلب بياناته
    if (messageData.receiver_id) {
      const { data: receiverData, error: receiverError } = await supabase
        .from('profiles')
        .select('full_name')
        .eq('id', messageData.receiver_id)
        .single();
        
      if (!receiverError && receiverData) {
        data[0].receiver = receiverData;
      }
    }
  }
}
```

### 2. إنشاء ملف SQL لإصلاح العلاقات

تم إنشاء ملف `src\lib\db\fix_message_relationships.sql` يحتوي على:

- وظيفة `fix_message_relationships` لإعادة تعريف العلاقات في جدول المراسلات
- وظيفة `refresh_schema_cache` لتحديث ذاكرة التخزين المؤقت للمخطط
- وظيفة `get_message_with_relations` لجلب الرسائل مع البيانات المرتبطة

## كيفية تطبيق الحل:

1. قم بتنفيذ ملف `src\lib\db\fix_message_relationships.sql` في قاعدة البيانات Supabase باستخدام SQL Editor
2. قم بتحديث الكود في صفحة المراسلات كما هو موضح أعلاه

## كيفية اختبار الحل:

1. انتقل إلى صفحة المراسلات الداخلية
2. انقر على زر "إرسال رسالة جديدة"
3. أدخل موضوع الرسالة ومحتواها
4. اختر نوع المستقبل (مستخدم أو وحدة تنظيمية)
5. اختر المستقبل المحدد
6. انقر على زر "إرسال"
7. يجب أن يتم إرسال الرسالة بنجاح وإضافتها إلى قائمة الرسائل المرسلة

إذا استمرت المشكلة، يمكنك تنفيذ الأمر التالي في SQL Editor لتحديث ذاكرة التخزين المؤقت للمخطط:

```sql
SELECT refresh_schema_cache();
```
