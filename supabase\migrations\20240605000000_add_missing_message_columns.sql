-- إضافة الحقول المفقودة لجدول messages للرسائل الرسمية الموقعة

-- التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
DO $$
BEGIN
  -- إضافة عمود is_official إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'is_official'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN is_official BOOLEAN DEFAULT FALSE;
    RAISE NOTICE 'Added column is_official to messages table';
  END IF;

  -- إضافة عمود is_signed إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'is_signed'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN is_signed BOOLEAN DEFAULT FALSE;
    RAISE NOTICE 'Added column is_signed to messages table';
  END IF;

  -- إضافة عمود signature إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'signature'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN signature JSONB DEFAULT NULL;
    RAISE NOTICE 'Added column signature to messages table';
  END IF;

  -- إضافة عمود unit_name إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'unit_name'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN unit_name TEXT;
    RAISE NOTICE 'Added column unit_name to messages table';
  END IF;

  -- إضافة عمود unit_logo إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'unit_logo'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN unit_logo TEXT;
    RAISE NOTICE 'Added column unit_logo to messages table';
  END IF;

  -- إضافة عمود organization_logo إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'organization_logo'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN organization_logo TEXT;
    RAISE NOTICE 'Added column organization_logo to messages table';
  END IF;

  -- إضافة عمود stamp إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'stamp'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN stamp TEXT;
    RAISE NOTICE 'Added column stamp to messages table';
  END IF;

  -- إضافة عمود is_read إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'is_read'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN is_read BOOLEAN DEFAULT FALSE;
    RAISE NOTICE 'Added column is_read to messages table';
  END IF;

  -- إضافة عمود status إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'status'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN status TEXT DEFAULT 'sent';
    RAISE NOTICE 'Added column status to messages table';
  END IF;

  -- إضافة عمود receiver_id إذا لم يكن موجوداً (للتوافق مع الإصدارات القديمة)
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'receiver_id'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN receiver_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
    RAISE NOTICE 'Added column receiver_id to messages table';
  END IF;

  -- إضافة عمود receiver_unit_id إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'receiver_unit_id'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN receiver_unit_id UUID REFERENCES public.units(id) ON DELETE SET NULL;
    RAISE NOTICE 'Added column receiver_unit_id to messages table';
  END IF;

  -- إضافة عمود parent_id إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'parent_id'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN parent_id UUID REFERENCES public.messages(id) ON DELETE SET NULL;
    RAISE NOTICE 'Added column parent_id to messages table';
  END IF;

  -- إضافة عمود created_at إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'created_at'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Added column created_at to messages table';
  END IF;

  -- إضافة عمود updated_at إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Added column updated_at to messages table';
  END IF;

  -- إضافة عمود attachment إذا لم يكن موجوداً
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'messages'
    AND column_name = 'attachment'
  ) THEN
    ALTER TABLE public.messages ADD COLUMN attachment JSONB DEFAULT NULL;
    RAISE NOTICE 'Added column attachment to messages table';
  END IF;

END $$;

-- إنشاء فهارس للأعمدة الجديدة لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_messages_is_official ON public.messages(is_official);
CREATE INDEX IF NOT EXISTS idx_messages_is_signed ON public.messages(is_signed);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON public.messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_recipient_id ON public.messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_messages_receiver_id ON public.messages(receiver_id);
CREATE INDEX IF NOT EXISTS idx_messages_receiver_unit_id ON public.messages(receiver_unit_id);
CREATE INDEX IF NOT EXISTS idx_messages_parent_id ON public.messages(parent_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON public.messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_attachment ON public.messages USING GIN (attachment);

-- تحديث ذاكرة التخزين المؤقت للمخطط
NOTIFY pgrst, 'reload schema';
