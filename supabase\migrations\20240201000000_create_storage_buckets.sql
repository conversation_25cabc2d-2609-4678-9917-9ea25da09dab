-- إنشاء مخازن التخزين (Storage Buckets)

-- إنشاء مخزن للشعارات
INSERT INTO storage.buckets (id, name, public)
VALUES ('logos', 'Logos', true)
ON CONFLICT (id) DO NOTHING;

-- إنشاء مخزن للأختام
INSERT INTO storage.buckets (id, name, public)
VALUES ('stamps', 'Stamps', true)
ON CONFLICT (id) DO NOTHING;

-- إنشاء مخزن saadabujnah
INSERT INTO storage.buckets (id, name, public)
VALUES ('saadabujnah', 'Saadabujnah', true)
ON CONFLICT (id) DO NOTHING;

-- إضافة سياسات الوصول للشعارات
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Logos Access Policy',
  '(bucket_id = ''logos''::text)',
  'logos'
)
ON CONFLICT (name, bucket_id) DO NOTHING;

-- إضافة سياسات الوصول للأختام
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Stamps Access Policy',
  '(bucket_id = ''stamps''::text)',
  'stamps'
)
ON CONFLICT (name, bucket_id) DO NOTHING;

-- إضافة سياسات الوصول لمجلد saadabujnah
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Saadabujnah Access Policy',
  '(bucket_id = ''saadabujnah''::text)',
  'saadabujnah'
)
ON CONFLICT (name, bucket_id) DO NOTHING;

-- إضافة سياسات الوصول للقراءة العامة
INSERT INTO storage.policies (name, definition, bucket_id)
VALUES (
  'Logos Public Read Policy',
  'true',
  'logos'
),
(
  'Stamps Public Read Policy',
  'true',
  'stamps'
),
(
  'Saadabujnah Public Read Policy',
  'true',
  'saadabujnah'
)
ON CONFLICT (name, bucket_id) DO NOTHING;

-- إضافة سياسات الوصول للكتابة للمستخدمين المصادق عليهم
INSERT INTO storage.policies (name, definition, bucket_id, operation)
VALUES (
  'Logos Authenticated Insert Policy',
  '(auth.role() = ''authenticated'')',
  'logos',
  'INSERT'
),
(
  'Stamps Authenticated Insert Policy',
  '(auth.role() = ''authenticated'')',
  'stamps',
  'INSERT'
),
(
  'Saadabujnah Authenticated Insert Policy',
  '(auth.role() = ''authenticated'')',
  'saadabujnah',
  'INSERT'
)
ON CONFLICT (name, bucket_id) DO NOTHING;

-- إضافة سياسات الوصول للتعديل والحذف للمستخدمين المصادق عليهم
INSERT INTO storage.policies (name, definition, bucket_id, operation)
VALUES (
  'Logos Authenticated Update Policy',
  '(auth.role() = ''authenticated'')',
  'logos',
  'UPDATE'
),
(
  'Stamps Authenticated Update Policy',
  '(auth.role() = ''authenticated'')',
  'stamps',
  'UPDATE'
),
(
  'Saadabujnah Authenticated Update Policy',
  '(auth.role() = ''authenticated'')',
  'saadabujnah',
  'UPDATE'
),
(
  'Logos Authenticated Delete Policy',
  '(auth.role() = ''authenticated'')',
  'logos',
  'DELETE'
),
(
  'Stamps Authenticated Delete Policy',
  '(auth.role() = ''authenticated'')',
  'stamps',
  'DELETE'
),
(
  'Saadabujnah Authenticated Delete Policy',
  '(auth.role() = ''authenticated'')',
  'saadabujnah',
  'DELETE'
)
ON CONFLICT (name, bucket_id) DO NOTHING;
