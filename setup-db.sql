-- إعد<PERSON> قاعدة بيانات نظام الأرشفة الإلكترونية

-- إ<PERSON><PERSON><PERSON><PERSON> جدول الملفات الشخصية للمستخدمين
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  role TEXT NOT NULL DEFAULT 'موظف',
  unit_id UUID REFERENCES units(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إن<PERSON>اء جدول الوحدات التنظيمية
CREATE TABLE IF NOT EXISTS units (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  parent_id UUID REFERENCES units(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المستندات
CREATE TABLE IF NOT EXISTS documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  document_number TEXT,
  document_date DATE NOT NULL,
  document_type TEXT NOT NULL,
  sender TEXT,
  receiver TEXT,
  notes TEXT,
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  unit_id UUID REFERENCES units(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول مرفقات المستندات
CREATE TABLE IF NOT EXISTS document_attachments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول قراء المستندات
CREATE TABLE IF NOT EXISTS document_readers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(document_id, user_id)
);

-- إنشاء جدول المراسلات
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  sender_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  receiver_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  receiver_unit_id UUID REFERENCES units(id) ON DELETE SET NULL,
  document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
  status TEXT NOT NULL DEFAULT 'sent',
  parent_id UUID REFERENCES messages(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CHECK (receiver_id IS NOT NULL OR receiver_unit_id IS NOT NULL)
);

-- إنشاء جدول التعميمات
CREATE TABLE IF NOT EXISTS broadcasts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  sender_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول قراء التعميمات
CREATE TABLE IF NOT EXISTS broadcast_readers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  broadcast_id UUID NOT NULL REFERENCES broadcasts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(broadcast_id, user_id)
);

-- إنشاء جدول رسائل الدردشة
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  content TEXT NOT NULL,
  sender_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  receiver_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الوظائف المساعدة

-- وظيفة تحديث وقت التعديل
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المحفزات

-- محفز تحديث وقت التعديل للملفات الشخصية
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- محفز تحديث وقت التعديل للوحدات التنظيمية
CREATE TRIGGER update_units_updated_at
BEFORE UPDATE ON units
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- محفز تحديث وقت التعديل للمستندات
CREATE TRIGGER update_documents_updated_at
BEFORE UPDATE ON documents
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- محفز تحديث وقت التعديل للمراسلات
CREATE TRIGGER update_messages_updated_at
BEFORE UPDATE ON messages
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- محفز تحديث وقت التعديل للتعميمات
CREATE TRIGGER update_broadcasts_updated_at
BEFORE UPDATE ON broadcasts
FOR EACH ROW
EXECUTE FUNCTION update_updated_at();

-- إنشاء السياسات الأمنية (RLS)

-- تفعيل RLS على جميع الجداول
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE units ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_readers ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE broadcasts ENABLE ROW LEVEL SECURITY;
ALTER TABLE broadcast_readers ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- سياسة قراءة الملفات الشخصية (للجميع)
CREATE POLICY profiles_select_policy ON profiles
FOR SELECT USING (true);

-- سياسة تعديل الملف الشخصي (للمستخدم نفسه والمشرفين)
CREATE POLICY profiles_update_policy ON profiles
FOR UPDATE USING (
  auth.uid() = id OR 
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'مشرف')
);

-- سياسة قراءة الوحدات التنظيمية (للجميع)
CREATE POLICY units_select_policy ON units
FOR SELECT USING (true);

-- سياسة إدارة الوحدات التنظيمية (للمشرفين فقط)
CREATE POLICY units_all_policy ON units
FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'مشرف')
);

-- سياسة قراءة المستندات (للجميع)
CREATE POLICY documents_select_policy ON documents
FOR SELECT USING (true);

-- سياسة إنشاء المستندات (للجميع)
CREATE POLICY documents_insert_policy ON documents
FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- سياسة تعديل المستندات (للمنشئ والمشرفين)
CREATE POLICY documents_update_policy ON documents
FOR UPDATE USING (
  created_by = auth.uid() OR 
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'مشرف')
);

-- سياسة حذف المستندات (للمنشئ والمشرفين)
CREATE POLICY documents_delete_policy ON documents
FOR DELETE USING (
  created_by = auth.uid() OR 
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'مشرف')
);

-- سياسة قراءة مرفقات المستندات (للجميع)
CREATE POLICY document_attachments_select_policy ON document_attachments
FOR SELECT USING (true);

-- سياسة إنشاء مرفقات المستندات (للجميع)
CREATE POLICY document_attachments_insert_policy ON document_attachments
FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- سياسة قراءة قراء المستندات (للجميع)
CREATE POLICY document_readers_select_policy ON document_readers
FOR SELECT USING (true);

-- سياسة إنشاء قراء المستندات (للجميع)
CREATE POLICY document_readers_insert_policy ON document_readers
FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- سياسة قراءة المراسلات (للمرسل والمستقبل)
CREATE POLICY messages_select_policy ON messages
FOR SELECT USING (
  sender_id = auth.uid() OR 
  receiver_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND 
    profiles.unit_id = messages.receiver_unit_id
  ) OR
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'مشرف')
);

-- سياسة إنشاء المراسلات (للجميع)
CREATE POLICY messages_insert_policy ON messages
FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- سياسة تعديل المراسلات (للمرسل والمستقبل)
CREATE POLICY messages_update_policy ON messages
FOR UPDATE USING (
  sender_id = auth.uid() OR 
  receiver_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND 
    profiles.unit_id = messages.receiver_unit_id
  )
);

-- سياسة قراءة التعميمات (للجميع)
CREATE POLICY broadcasts_select_policy ON broadcasts
FOR SELECT USING (true);

-- سياسة إدارة التعميمات (للمشرفين فقط)
CREATE POLICY broadcasts_all_policy ON broadcasts
FOR ALL USING (
  EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'مشرف')
);

-- سياسة قراءة قراء التعميمات (للجميع)
CREATE POLICY broadcast_readers_select_policy ON broadcast_readers
FOR SELECT USING (true);

-- سياسة إنشاء قراء التعميمات (للجميع)
CREATE POLICY broadcast_readers_insert_policy ON broadcast_readers
FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- سياسة قراءة رسائل الدردشة (للمرسل والمستقبل)
CREATE POLICY chat_messages_select_policy ON chat_messages
FOR SELECT USING (
  sender_id = auth.uid() OR 
  receiver_id = auth.uid()
);

-- سياسة إنشاء رسائل الدردشة (للجميع)
CREATE POLICY chat_messages_insert_policy ON chat_messages
FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- سياسة تعديل رسائل الدردشة (للمستقبل فقط - لتحديث حالة القراءة)
CREATE POLICY chat_messages_update_policy ON chat_messages
FOR UPDATE USING (
  receiver_id = auth.uid()
);
