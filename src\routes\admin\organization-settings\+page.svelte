<script lang="ts">
  import { onMount } from 'svelte';
  import { supabase } from '$lib/supabase';

  // متغيرات الصفحة
  let organizationName = '';
  let organizationLogo: string | null = null;
  let rightLogo: string | null = null;
  let leftLogo: string | null = null;
  let defaultStamp: string | null = null;
  let logoName = '';
  let address = '';
  let phone = '';
  let email = '';
  let website = '';

  // متغيرات لعناوين URL المباشرة
  let organizationLogoDirectUrl = '';
  let rightLogoDirectUrl = '';
  let leftLogoDirectUrl = '';
  let defaultStampDirectUrl = '';

  let organizationLogoUrl = '';
  let rightLogoUrl = '';
  let leftLogoUrl = '';
  let defaultStampUrl = '';

  let isLoading = true;
  let isSaving = false;
  let error: string | null = null;
  let success: string | null = null;

  let uploadingOrgLogo = false;
  let uploadingRightLogo = false;
  let uploadingLeftLogo = false;
  let uploadingStamp = false;

  let currentUser: any = null;
  let isAdmin = false;

  // جلب إعدادات المنظمة
  async function fetchOrganizationSettings() {
    try {
      isLoading = true;
      error = null;

      // جلب بيانات المستخدم الحالي
      const { data: { user } } = await supabase.auth.getUser();
      currentUser = user;

      if (!user) {
        error = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة';
        return;
      }

      // التحقق من صلاحيات المستخدم
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (userError) throw userError;

      // التحقق من أن المستخدم مدير
      isAdmin = userData?.role === 'admin';

      if (!isAdmin) {
        error = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
        return;
      }

      // جلب إعدادات المنظمة
      const { data: settings, error: settingsError } = await supabase
        .from('organization_settings')
        .select('*')
        .single();

      if (settingsError && settingsError.code !== 'PGRST116') {
        throw settingsError;
      }

      if (settings) {
        organizationName = settings.name || '';
        organizationLogo = settings.logo || null;
        rightLogo = settings.right_logo || null;
        leftLogo = settings.left_logo || null;
        defaultStamp = settings.default_stamp || null;
        logoName = settings.logo || '';
        address = settings.address || '';
        phone = settings.phone || '';
        email = settings.email || '';
        website = settings.website || '';

        // التحقق من عناوين URL المباشرة
        if (organizationLogo && organizationLogo.startsWith('http')) {
          organizationLogoDirectUrl = organizationLogo;
          organizationLogoUrl = organizationLogo;
        }

        if (rightLogo && rightLogo.startsWith('http')) {
          rightLogoDirectUrl = rightLogo;
          rightLogoUrl = rightLogo;
        }

        if (leftLogo && leftLogo.startsWith('http')) {
          leftLogoDirectUrl = leftLogo;
          leftLogoUrl = leftLogo;
        }

        if (defaultStamp && defaultStamp.startsWith('http')) {
          defaultStampDirectUrl = defaultStamp;
          defaultStampUrl = defaultStamp;
        }

        // جلب روابط الصور من التخزين إذا لم تكن عناوين URL مباشرة
        await loadImages();
      }
    } catch (err: any) {
      console.error('Error fetching organization settings:', err);
      error = 'حدث خطأ أثناء جلب إعدادات المنظمة: ' + (err.message || err);
    } finally {
      isLoading = false;
    }
  }

  // تحميل الصور
  async function loadImages() {
    try {
      // تحميل شعار المنظمة (فقط إذا لم يكن عنوان URL مباشر)
      if (organizationLogo && !organizationLogo.startsWith('http')) {
        const { data: orgLogoData, error: orgLogoError } = await supabase
          .storage
          .from('logos')
          .download(organizationLogo);

        if (!orgLogoError && orgLogoData) {
          organizationLogoUrl = URL.createObjectURL(orgLogoData);
        }
      }

      // تحميل الشعار الأيمن (فقط إذا لم يكن عنوان URL مباشر)
      if (rightLogo && !rightLogo.startsWith('http')) {
        const { data: rightLogoData, error: rightLogoError } = await supabase
          .storage
          .from('logos')
          .download(rightLogo);

        if (!rightLogoError && rightLogoData) {
          rightLogoUrl = URL.createObjectURL(rightLogoData);
        }
      }

      // تحميل الشعار الأيسر (فقط إذا لم يكن عنوان URL مباشر)
      if (leftLogo && !leftLogo.startsWith('http')) {
        const { data: leftLogoData, error: leftLogoError } = await supabase
          .storage
          .from('logos')
          .download(leftLogo);

        if (!leftLogoError && leftLogoData) {
          leftLogoUrl = URL.createObjectURL(leftLogoData);
        }
      }

      // تحميل الختم الافتراضي (فقط إذا لم يكن عنوان URL مباشر)
      if (defaultStamp && !defaultStamp.startsWith('http')) {
        const { data: stampData, error: stampError } = await supabase
          .storage
          .from('stamps')
          .download(defaultStamp);

        if (!stampError && stampData) {
          defaultStampUrl = URL.createObjectURL(stampData);
        }
      }
    } catch (err) {
      console.error('Error loading images:', err);
    }
  }

  // رفع شعار المنظمة
  async function uploadOrganizationLogo(event: any) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      uploadingOrgLogo = true;
      error = null;

      // إنشاء اسم فريد للملف
      const fileExt = file.name.split('.').pop();
      const fileName = `org_logo_${Date.now()}.${fileExt}`;

      // رفع الملف
      const { data, error: uploadError } = await supabase
        .storage
        .from('logos')
        .upload(fileName, file, { upsert: true });

      if (uploadError) throw uploadError;

      // تحديث مسار الشعار
      organizationLogo = fileName;

      // تحديث عنوان URL للعرض
      organizationLogoUrl = URL.createObjectURL(file);

      success = 'تم رفع شعار المنظمة بنجاح';
    } catch (err: any) {
      console.error('Error uploading organization logo:', err);
      error = 'حدث خطأ أثناء رفع شعار المنظمة: ' + (err.message || err);
    } finally {
      uploadingOrgLogo = false;
    }
  }

  // رفع الشعار الأيمن
  async function uploadRightLogo(event: any) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      uploadingRightLogo = true;
      error = null;

      // إنشاء اسم فريد للملف
      const fileExt = file.name.split('.').pop();
      const fileName = `right_logo_${Date.now()}.${fileExt}`;

      // رفع الملف
      const { data, error: uploadError } = await supabase
        .storage
        .from('logos')
        .upload(fileName, file, { upsert: true });

      if (uploadError) throw uploadError;

      // تحديث مسار الشعار
      rightLogo = fileName;

      // تحديث عنوان URL للعرض
      rightLogoUrl = URL.createObjectURL(file);

      success = 'تم رفع الشعار الأيمن بنجاح';
    } catch (err: any) {
      console.error('Error uploading right logo:', err);
      error = 'حدث خطأ أثناء رفع الشعار الأيمن: ' + (err.message || err);
    } finally {
      uploadingRightLogo = false;
    }
  }

  // رفع الشعار الأيسر
  async function uploadLeftLogo(event: any) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      uploadingLeftLogo = true;
      error = null;

      // إنشاء اسم فريد للملف
      const fileExt = file.name.split('.').pop();
      const fileName = `left_logo_${Date.now()}.${fileExt}`;

      // رفع الملف
      const { data, error: uploadError } = await supabase
        .storage
        .from('logos')
        .upload(fileName, file, { upsert: true });

      if (uploadError) throw uploadError;

      // تحديث مسار الشعار
      leftLogo = fileName;

      // تحديث عنوان URL للعرض
      leftLogoUrl = URL.createObjectURL(file);

      success = 'تم رفع الشعار الأيسر بنجاح';
    } catch (err: any) {
      console.error('Error uploading left logo:', err);
      error = 'حدث خطأ أثناء رفع الشعار الأيسر: ' + (err.message || err);
    } finally {
      uploadingLeftLogo = false;
    }
  }

  // رفع الختم الافتراضي
  async function uploadDefaultStamp(event: any) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      uploadingStamp = true;
      error = null;

      // إنشاء اسم فريد للملف
      const fileExt = file.name.split('.').pop();
      const fileName = `default_stamp_${Date.now()}.${fileExt}`;

      // رفع الملف
      const { data, error: uploadError } = await supabase
        .storage
        .from('stamps')
        .upload(fileName, file, { upsert: true });

      if (uploadError) throw uploadError;

      // تحديث مسار الختم
      defaultStamp = fileName;

      // تحديث عنوان URL للعرض
      defaultStampUrl = URL.createObjectURL(file);

      success = 'تم رفع الختم الافتراضي بنجاح';
    } catch (err: any) {
      console.error('Error uploading default stamp:', err);
      error = 'حدث خطأ أثناء رفع الختم الافتراضي: ' + (err.message || err);
    } finally {
      uploadingStamp = false;
    }
  }

  // حفظ إعدادات المنظمة
  async function saveOrganizationSettings() {
    try {
      isSaving = true;
      error = null;
      success = null;

      // التحقق من صلاحيات المستخدم
      if (!isAdmin) {
        error = 'ليس لديك صلاحية لحفظ إعدادات المنظمة';
        return;
      }

      // استخدام عناوين URL المباشرة إذا تم إدخالها
      if (organizationLogoDirectUrl) {
        organizationLogo = organizationLogoDirectUrl;
      }

      if (rightLogoDirectUrl) {
        rightLogo = rightLogoDirectUrl;
      }

      if (leftLogoDirectUrl) {
        leftLogo = leftLogoDirectUrl;
      }

      if (defaultStampDirectUrl) {
        defaultStamp = defaultStampDirectUrl;
      }

      // إعداد بيانات الإعدادات
      const settingsData = {
        name: organizationName,
        logo: logoName,
        right_logo: rightLogo,
        left_logo: leftLogo,
        default_stamp: defaultStamp,
        address: address,
        phone: phone,
        email: email,
        website: website,
        updated_at: new Date().toISOString()
      };

      // حفظ الإعدادات
      const { data, error: saveError } = await supabase
        .from('organization_settings')
        .upsert(settingsData)
        .select();

      if (saveError) throw saveError;

      success = 'تم حفظ إعدادات المنظمة بنجاح';
    } catch (err: any) {
      console.error('Error saving organization settings:', err);
      error = 'حدث خطأ أثناء حفظ إعدادات المنظمة: ' + (err.message || err);
    } finally {
      isSaving = false;
    }
  }

  // تحميل البيانات عند تحميل الصفحة
  onMount(fetchOrganizationSettings);
</script>

<svelte:head>
  <title>إعدادات المنظمة</title>
</svelte:head>

<div class="container mx-auto p-4 rtl">
  <div class="mb-6">
    <h1 class="text-2xl font-bold mb-2">إعدادات المنظمة</h1>
    <p class="text-gray-600">قم بتعديل إعدادات المنظمة وشعاراتها</p>
  </div>

  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      {error}
    </div>
  {/if}

  {#if success}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      {success}
    </div>
  {/if}

  {#if isLoading}
    <div class="flex justify-center items-center h-64">
      <div class="loader"></div>
    </div>
  {:else if !isAdmin}
    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
      <p>ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
    </div>
  {:else}
    <div class="bg-white p-6 rounded-lg shadow-md">
      <div class="mb-6">
        <label for="organization-name" class="block text-gray-700 mb-2">اسم المنظمة</label>
        <input
          type="text"
          id="organization-name"
          bind:value={organizationName}
          class="w-full p-2 border border-gray-300 rounded"
          placeholder="اسم المنظمة"
        />
      </div>

      <div class="mb-6">
        <label for="logo-text" class="block text-gray-700 mb-2">نص الشعار</label>
        <input
          type="text"
          id="logo-text"
          bind:value={logoName}
          class="w-full p-2 border border-gray-300 rounded"
          placeholder="أدخل نص الشعار"
        />
        <p class="text-xs text-gray-500 mt-1">هذا النص سيظهر في المستندات تحت اسم المنظمة</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- شعار المنظمة -->
        <div class="border p-4 rounded-lg">
          <h3 class="font-bold mb-3">شعار المنظمة</h3>
          <div class="flex flex-col items-center">
            {#if organizationLogoUrl}
              <div class="mb-3 w-32 h-32 flex items-center justify-center">
                <img src={organizationLogoUrl} alt="شعار المنظمة" class="max-w-full max-h-full" />
              </div>
            {/if}

            <div class="mb-3 w-full">
              <label for="org-logo-url" class="block text-gray-700 mb-2">عنوان URL للشعار</label>
              <input
                type="text"
                id="org-logo-url"
                bind:value={organizationLogoDirectUrl}
                class="w-full p-2 border border-gray-300 rounded mb-2"
                placeholder="أدخل عنوان URL للشعار"
              />
              <p class="text-xs text-gray-500 mb-3">يمكنك إدخال عنوان URL مباشر للشعار بدلاً من رفع ملف</p>
            </div>

            <div class="flex items-center">
              <span class="mx-2">أو</span>
            </div>

            <div class="mt-3">
              <label for="org-logo-upload" class="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                {#if uploadingOrgLogo}
                  <span>جاري الرفع...</span>
                {:else}
                  <span>{organizationLogoUrl ? 'تغيير الشعار' : 'رفع الشعار'}</span>
                {/if}
              </label>
              <input
                type="file"
                id="org-logo-upload"
                accept="image/*"
                on:change={uploadOrganizationLogo}
                disabled={uploadingOrgLogo}
                class="hidden"
              />
            </div>
          </div>
        </div>

        <!-- الشعار الأيمن -->
        <div class="border p-4 rounded-lg">
          <h3 class="font-bold mb-3">الشعار الأيمن</h3>
          <div class="flex flex-col items-center">
            {#if rightLogoUrl}
              <div class="mb-3 w-32 h-32 flex items-center justify-center">
                <img src={rightLogoUrl} alt="الشعار الأيمن" class="max-w-full max-h-full" />
              </div>
            {/if}

            <div class="mb-3 w-full">
              <label for="right-logo-url" class="block text-gray-700 mb-2">عنوان URL للشعار الأيمن</label>
              <input
                type="text"
                id="right-logo-url"
                bind:value={rightLogoDirectUrl}
                class="w-full p-2 border border-gray-300 rounded mb-2"
                placeholder="أدخل عنوان URL للشعار الأيمن"
              />
              <p class="text-xs text-gray-500 mb-3">يمكنك إدخال عنوان URL مباشر للشعار بدلاً من رفع ملف</p>
            </div>

            <div class="flex items-center">
              <span class="mx-2">أو</span>
            </div>

            <div class="mt-3">
              <label for="right-logo-upload" class="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                {#if uploadingRightLogo}
                  <span>جاري الرفع...</span>
                {:else}
                  <span>{rightLogoUrl ? 'تغيير الشعار' : 'رفع الشعار'}</span>
                {/if}
              </label>
              <input
                type="file"
                id="right-logo-upload"
                accept="image/*"
                on:change={uploadRightLogo}
                disabled={uploadingRightLogo}
                class="hidden"
              />
            </div>
          </div>
        </div>

        <!-- الشعار الأيسر -->
        <div class="border p-4 rounded-lg">
          <h3 class="font-bold mb-3">الشعار الأيسر</h3>
          <div class="flex flex-col items-center">
            {#if leftLogoUrl}
              <div class="mb-3 w-32 h-32 flex items-center justify-center">
                <img src={leftLogoUrl} alt="الشعار الأيسر" class="max-w-full max-h-full" />
              </div>
            {/if}

            <div class="mb-3 w-full">
              <label for="left-logo-url" class="block text-gray-700 mb-2">عنوان URL للشعار الأيسر</label>
              <input
                type="text"
                id="left-logo-url"
                bind:value={leftLogoDirectUrl}
                class="w-full p-2 border border-gray-300 rounded mb-2"
                placeholder="أدخل عنوان URL للشعار الأيسر"
              />
              <p class="text-xs text-gray-500 mb-3">يمكنك إدخال عنوان URL مباشر للشعار بدلاً من رفع ملف</p>
            </div>

            <div class="flex items-center">
              <span class="mx-2">أو</span>
            </div>

            <div class="mt-3">
              <label for="left-logo-upload" class="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                {#if uploadingLeftLogo}
                  <span>جاري الرفع...</span>
                {:else}
                  <span>{leftLogoUrl ? 'تغيير الشعار' : 'رفع الشعار'}</span>
                {/if}
              </label>
              <input
                type="file"
                id="left-logo-upload"
                accept="image/*"
                on:change={uploadLeftLogo}
                disabled={uploadingLeftLogo}
                class="hidden"
              />
            </div>
          </div>
        </div>

        <!-- الختم الافتراضي -->
        <div class="border p-4 rounded-lg">
          <h3 class="font-bold mb-3">الختم الافتراضي</h3>
          <div class="flex flex-col items-center">
            {#if defaultStampUrl}
              <div class="mb-3 w-32 h-32 flex items-center justify-center">
                <img src={defaultStampUrl} alt="الختم الافتراضي" class="max-w-full max-h-full" />
              </div>
            {/if}

            <div class="mb-3 w-full">
              <label for="stamp-url" class="block text-gray-700 mb-2">عنوان URL للختم</label>
              <input
                type="text"
                id="stamp-url"
                bind:value={defaultStampDirectUrl}
                class="w-full p-2 border border-gray-300 rounded mb-2"
                placeholder="أدخل عنوان URL للختم"
              />
              <p class="text-xs text-gray-500 mb-3">يمكنك إدخال عنوان URL مباشر للختم بدلاً من رفع ملف</p>
            </div>

            <div class="flex items-center">
              <span class="mx-2">أو</span>
            </div>

            <div class="mt-3">
              <label for="stamp-upload" class="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                {#if uploadingStamp}
                  <span>جاري الرفع...</span>
                {:else}
                  <span>{defaultStampUrl ? 'تغيير الختم' : 'رفع الختم'}</span>
                {/if}
              </label>
              <input
                type="file"
                id="stamp-upload"
                accept="image/*"
                on:change={uploadDefaultStamp}
                disabled={uploadingStamp}
                class="hidden"
              />
            </div>
          </div>
        </div>
      </div>



      <!-- معلومات الاتصال والنصوص -->
      <div class="border p-4 rounded-lg mb-6">
        <h3 class="font-bold mb-3">معلومات الاتصال والنصوص</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label for="address" class="block text-gray-700 mb-2">العنوان</label>
            <input
              type="text"
              id="address"
              bind:value={address}
              class="w-full p-2 border border-gray-300 rounded"
              placeholder="عنوان المنظمة"
            />
          </div>

          <div>
            <label for="phone" class="block text-gray-700 mb-2">رقم الهاتف</label>
            <input
              type="text"
              id="phone"
              bind:value={phone}
              class="w-full p-2 border border-gray-300 rounded"
              placeholder="رقم هاتف المنظمة"
            />
          </div>

          <div>
            <label for="email" class="block text-gray-700 mb-2">البريد الإلكتروني</label>
            <input
              type="email"
              id="email"
              bind:value={email}
              class="w-full p-2 border border-gray-300 rounded"
              placeholder="البريد الإلكتروني للمنظمة"
            />
          </div>

          <div>
            <label for="website" class="block text-gray-700 mb-2">الموقع الإلكتروني</label>
            <input
              type="text"
              id="website"
              bind:value={website}
              class="w-full p-2 border border-gray-300 rounded"
              placeholder="الموقع الإلكتروني للمنظمة"
            />
          </div>
        </div>




      </div>

      <div class="flex justify-end">
        <button
          type="button"
          class="bg-green-500 hover:bg-green-600 text-white py-2 px-6 rounded"
          on:click={saveOrganizationSettings}
          disabled={isSaving}
        >
          {isSaving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
        </button>
      </div>
    </div>
  {/if}
</div>

<style>
  .rtl {
    direction: rtl;
  }

  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
