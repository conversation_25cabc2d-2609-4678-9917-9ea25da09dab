-- إصلا<PERSON> العلاقات في جدول المراسلات

-- 0. إنشاء وظيفة للتحقق من وجود جدول المراسلات وإنشائه إذا لم يكن موجوداً
CREATE OR REPLACE FUNCTION ensure_messages_table_exists()
RETURNS VOID AS $$
DECLARE
  table_exists BOOLEAN;
BEGIN
  -- التحقق من وجود جدول المراسلات
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.tables
    WHERE table_name = 'messages'
  ) INTO table_exists;

  -- إذا لم يكن الجدول موجوداً، قم بإنشائه
  IF NOT table_exists THEN
    RAISE NOTICE 'Table messages does not exist. Creating it...';

    CREATE TABLE messages (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      subject TEXT NOT NULL,
      content TEXT NOT NULL,
      sender_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
      receiver_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
      receiver_unit_id UUID REFERENCES units(id) ON DELETE SET NULL,
      document_id UUID REFERENCES documents(id) ON DELETE SET NULL,
      status TEXT NOT NULL DEFAULT 'sent',
      parent_id UUID REFERENCES messages(id) ON DELETE SET NULL,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      CHECK (receiver_id IS NOT NULL OR receiver_unit_id IS NOT NULL)
    );

    -- إنشاء فهارس للبحث السريع
    CREATE INDEX idx_messages_sender_id ON messages(sender_id);
    CREATE INDEX idx_messages_receiver_id ON messages(receiver_id);
    CREATE INDEX idx_messages_receiver_unit_id ON messages(receiver_unit_id);
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 1. إنشاء وظيفة للتحقق من بنية جدول المراسلات وإصلاح العلاقات
CREATE OR REPLACE FUNCTION fix_message_relationships()
RETURNS VOID AS $$
DECLARE
  column_exists BOOLEAN;
  table_exists BOOLEAN;
BEGIN
  -- التحقق من وجود جدول المراسلات
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.tables
    WHERE table_name = 'messages'
  ) INTO table_exists;

  -- إذا لم يكن الجدول موجوداً، قم بإنشائه
  IF NOT table_exists THEN
    PERFORM ensure_messages_table_exists();
    RETURN;
  END IF;
  -- التحقق من وجود الأعمدة قبل إنشاء العلاقات
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'messages'
    AND column_name = 'receiver_id'
  ) INTO column_exists;

  -- إذا لم يكن العمود موجوداً، قم بإنشائه
  IF NOT column_exists THEN
    RAISE NOTICE 'Column receiver_id does not exist in messages table. Adding it...';
    ALTER TABLE messages ADD COLUMN receiver_id UUID REFERENCES profiles(id) ON DELETE SET NULL;
  ELSE
    -- إزالة العلاقات الحالية فقط إذا كان العمود موجوداً
    ALTER TABLE IF EXISTS messages DROP CONSTRAINT IF EXISTS messages_receiver_id_fkey;

    -- إعادة إنشاء العلاقة
    ALTER TABLE messages
      ADD CONSTRAINT messages_receiver_id_fkey
      FOREIGN KEY (receiver_id)
      REFERENCES profiles(id)
      ON DELETE SET NULL;
  END IF;

  -- التحقق من وجود عمود sender_id
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'messages'
    AND column_name = 'sender_id'
  ) INTO column_exists;

  IF column_exists THEN
    -- إزالة العلاقة الحالية
    ALTER TABLE IF EXISTS messages DROP CONSTRAINT IF EXISTS messages_sender_id_fkey;

    -- إعادة إنشاء العلاقة
    ALTER TABLE messages
      ADD CONSTRAINT messages_sender_id_fkey
      FOREIGN KEY (sender_id)
      REFERENCES profiles(id)
      ON DELETE CASCADE;
  END IF;

  -- التحقق من وجود عمود receiver_unit_id
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'messages'
    AND column_name = 'receiver_unit_id'
  ) INTO column_exists;

  IF column_exists THEN
    -- إزالة العلاقة الحالية
    ALTER TABLE IF EXISTS messages DROP CONSTRAINT IF EXISTS messages_receiver_unit_id_fkey;

    -- إعادة إنشاء العلاقة
    ALTER TABLE messages
      ADD CONSTRAINT messages_receiver_unit_id_fkey
      FOREIGN KEY (receiver_unit_id)
      REFERENCES units(id)
      ON DELETE SET NULL;
  END IF;

  -- التحقق من وجود عمود document_id
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'messages'
    AND column_name = 'document_id'
  ) INTO column_exists;

  IF column_exists THEN
    -- إزالة العلاقة الحالية
    ALTER TABLE IF EXISTS messages DROP CONSTRAINT IF EXISTS messages_document_id_fkey;

    -- إعادة إنشاء العلاقة
    ALTER TABLE messages
      ADD CONSTRAINT messages_document_id_fkey
      FOREIGN KEY (document_id)
      REFERENCES documents(id)
      ON DELETE SET NULL;
  END IF;

  -- التحقق من وجود عمود parent_id
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'messages'
    AND column_name = 'parent_id'
  ) INTO column_exists;

  IF column_exists THEN
    -- إزالة العلاقة الحالية
    ALTER TABLE IF EXISTS messages DROP CONSTRAINT IF EXISTS messages_parent_id_fkey;

    -- إعادة إنشاء العلاقة
    ALTER TABLE messages
      ADD CONSTRAINT messages_parent_id_fkey
      FOREIGN KEY (parent_id)
      REFERENCES messages(id)
      ON DELETE SET NULL;
  END IF;

  -- إنشاء فهارس للبحث السريع (فقط للأعمدة الموجودة)
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'messages'
    AND column_name = 'sender_id'
  ) INTO column_exists;

  IF column_exists THEN
    DROP INDEX IF EXISTS idx_messages_sender_id;
    CREATE INDEX idx_messages_sender_id ON messages(sender_id);
  END IF;

  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'messages'
    AND column_name = 'receiver_id'
  ) INTO column_exists;

  IF column_exists THEN
    DROP INDEX IF EXISTS idx_messages_receiver_id;
    CREATE INDEX idx_messages_receiver_id ON messages(receiver_id);
  END IF;

  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'messages'
    AND column_name = 'receiver_unit_id'
  ) INTO column_exists;

  IF column_exists THEN
    DROP INDEX IF EXISTS idx_messages_receiver_unit_id;
    CREATE INDEX idx_messages_receiver_unit_id ON messages(receiver_unit_id);
  END IF;

  -- تحديث ذاكرة التخزين المؤقت للمخطط
  NOTIFY pgrst, 'reload schema';
END;
$$ LANGUAGE plpgsql;

-- 2. تنفيذ الوظائف
-- أولاً: التأكد من وجود جدول المراسلات
SELECT ensure_messages_table_exists();
-- ثانياً: إصلاح العلاقات في جدول المراسلات
SELECT fix_message_relationships();

-- 3. إنشاء وظيفة RPC لتحديث ذاكرة التخزين المؤقت للمخطط
CREATE OR REPLACE FUNCTION refresh_schema_cache()
RETURNS VOID AS $$
BEGIN
  NOTIFY pgrst, 'reload schema';
END;
$$ LANGUAGE plpgsql;

-- 4. إنشاء وظيفة لفحص بنية جدول المراسلات
CREATE OR REPLACE FUNCTION check_messages_table_structure()
RETURNS TABLE (
  column_name TEXT,
  data_type TEXT,
  is_nullable TEXT,
  column_default TEXT,
  constraint_name TEXT,
  constraint_type TEXT,
  foreign_table TEXT,
  foreign_column TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.column_name::TEXT,
    c.data_type::TEXT,
    c.is_nullable::TEXT,
    c.column_default::TEXT,
    tc.constraint_name::TEXT,
    tc.constraint_type::TEXT,
    ccu.table_name::TEXT AS foreign_table,
    ccu.column_name::TEXT AS foreign_column
  FROM
    information_schema.columns c
    LEFT JOIN information_schema.key_column_usage kcu
      ON c.table_name = kcu.table_name AND c.column_name = kcu.column_name
    LEFT JOIN information_schema.table_constraints tc
      ON kcu.constraint_name = tc.constraint_name
    LEFT JOIN information_schema.constraint_column_usage ccu
      ON tc.constraint_name = ccu.constraint_name
  WHERE
    c.table_name = 'messages'
  ORDER BY
    c.ordinal_position;
END;
$$ LANGUAGE plpgsql;

-- 4. إنشاء وظيفة لإصلاح مشكلة العلاقات في استعلامات الجلب
CREATE OR REPLACE FUNCTION get_message_with_relations(message_id UUID)
RETURNS TABLE (
  id UUID,
  subject TEXT,
  content TEXT,
  sender_id UUID,
  receiver_id UUID,
  receiver_unit_id UUID,
  document_id UUID,
  status TEXT,
  parent_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  sender_name TEXT,
  receiver_name TEXT,
  receiver_unit_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    m.id,
    m.subject,
    m.content,
    m.sender_id,
    m.receiver_id,
    m.receiver_unit_id,
    m.document_id,
    m.status,
    m.parent_id,
    m.created_at,
    m.updated_at,
    sender.full_name AS sender_name,
    receiver.full_name AS receiver_name,
    unit.name AS receiver_unit_name
  FROM
    messages m
    LEFT JOIN profiles sender ON m.sender_id = sender.id
    LEFT JOIN profiles receiver ON m.receiver_id = receiver.id
    LEFT JOIN units unit ON m.receiver_unit_id = unit.id
  WHERE
    m.id = message_id;
END;
$$ LANGUAGE plpgsql;
