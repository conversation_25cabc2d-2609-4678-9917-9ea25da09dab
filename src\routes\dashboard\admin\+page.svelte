<script lang="ts">
  import { supabase } from '$lib/supabase';
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { PermissionService } from '$lib/services/permissionService';
  import { RoleType } from '$lib/types/permissions';

  // متغيرات الحالة
  let loading = true;
  let isAdmin = false;
  let currentUser: any = null;

  // قائمة الأدوات الإدارية
  const adminTools = [
    {
      title: 'إدارة المستخدمين',
      description: 'إضافة وتعديل وحذف المستخدمين وإدارة حساباتهم',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>',
      href: '/dashboard/users'
    },
    {
      title: 'الأدوار والصلاحيات',
      description: 'إدارة أدوار المستخدمين وصلاحياتهم في النظام',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"/><path d="m9 12 2 2 4-4"/></svg>',
      href: '/dashboard/roles'
    },
    {
      title: 'صلاحيات التوقيع الإلكتروني',
      description: 'إدارة صلاحيات التوقيع الإلكتروني للمستخدمين',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path><path d="M14.5 19.5h-5c-1.5 0-2.5-1-2.5-2.5v-10c0-1.5 1-2.5 2.5-2.5h5c1.5 0 2.5 1 2.5 2.5v10c0 1.5-1 2.5-2.5 2.5Z"></path><path d="M12 22v-2"></path><path d="M12 4V2"></path></svg>',
      href: '/dashboard/admin/signature-permissions'
    },
    {
      title: 'الهيكل التنظيمي',
      description: 'إدارة الوحدات التنظيمية والأقسام',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 3H3v7h18V3z"/><path d="M21 14h-5v7h5v-7z"/><path d="M8 14H3v7h5v-7z"/><path d="M14.5 14.5h-5v7h5v-7z"/></svg>',
      href: '/dashboard/organization'
    },
    {
      title: 'إعدادات النظام',
      description: 'إدارة إعدادات النظام العامة والخاصة',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>',
      href: '/dashboard/settings'
    }
  ];

  onMount(async () => {
    try {
      // جلب بيانات المستخدم
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        goto('/login');
        return;
      }

      currentUser = user;

      // التحقق من صلاحيات المستخدم
      isAdmin = await PermissionService.checkRole(user.id, RoleType.ADMIN);

      if (!isAdmin) {
        alert('ليس لديك صلاحية للوصول إلى لوحة تحكم المدير');
        goto('/dashboard');
      }
    } catch (error) {
      console.error('Error checking permissions:', error);
      goto('/dashboard');
    } finally {
      loading = false;
    }
  });
</script>

<div class="container mx-auto p-4">
  <div class="mb-6">
    <h1 class="text-2xl font-bold">لوحة تحكم المدير</h1>
    <p class="text-gray-600 dark:text-gray-400 mt-2">
      مرحباً بك في لوحة تحكم المدير. يمكنك من هنا إدارة جميع جوانب النظام.
    </p>
  </div>

  {#if loading}
    <div class="flex justify-center items-center h-64">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  {:else if isAdmin}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {#each adminTools as tool}
        <a
          href={tool.href}
          class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow"
        >
          <div class="flex items-start">
            <div class="bg-primary/10 dark:bg-blue-900/30 p-3 rounded-lg ml-4">
              {@html tool.icon}
            </div>
            <div>
              <h3 class="text-lg font-semibold mb-2">{tool.title}</h3>
              <p class="text-gray-600 dark:text-gray-400 text-sm">{tool.description}</p>
            </div>
          </div>
        </a>
      {/each}
    </div>
  {:else}
    <div class="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 p-4 rounded-md">
      <p>ليس لديك صلاحية للوصول إلى لوحة تحكم المدير.</p>
    </div>
  {/if}
</div>
